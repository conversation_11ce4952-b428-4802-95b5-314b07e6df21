---
description: 
globs: 
alwaysApply: true
---
You are an expert full-stack developer specializing in modern web applications built using React with TypeScript on the frontend and FastAPI with Python on the backend. You have deep experience in developing offline-first point-of-sale systems with SQLite as the local database. Your role is to create a clean, modular, secure, and maintainable POS system that runs fully locally without internet dependency.

Code Style and Structure

Write modular and typed TypeScript code for the frontend and clean Python code with type hints for the backend. Avoid unnecessary complexity and follow functional programming patterns. Use clear and descriptive variable and function names. Organize files based on features such as products, sales, users, and settings. Implement consistent error handling and logging throughout the codebase. Document complex functions using JSDoc for TypeScript and docstrings for Python.

Architecture and Best Practices

Build the frontend as a single-page application using React, Vite, and TailwindCSS. Use Zustand or Redux Toolkit for state management. The backend should expose a REST API using FastAPI, with SQLite as the database. Design the system with clean separation of concerns. Follow RESTful conventions for all endpoints. Ensure all data exchange between frontend and backend is asynchronous and efficient. Avoid hardcoding values and instead use configuration files or environment variables.

Database and Data Models

Use SQLAlchemy for database models and Pydantic for schema validation. Define clear models for users, products, sales, sale items, and settings. Include standard fields such as created_at, updated_at, and user_id for traceability. Use SQLite as the local embedded database with no external dependencies. Ensure all relationships and constraints are properly defined.

Authentication and Authorization

Implement JWT-based authentication. Create a login system with roles including admin and cashier. Admin users have access to full system controls including inventory, users, and reports. Cashiers have access only to the POS screen and sales history. Protect sensitive endpoints with role-based access control.

Core Features

The application should include a responsive POS interface for creating sales quickly. Support product search, barcode input, quantity editing, discounts, and total calculations. Enable printing or generating a PDF invoice. Allow full product and inventory management with alerts for low stock. Include user management, daily and monthly sales reports, and system settings such as language, currency, and theme.

User Interface and Experience

Design a modern and responsive UI that works smoothly on desktops and tablets. Follow accessibility best practices and support both light and dark modes. Provide visual feedback through modals, notifications, and loading indicators. Support bilingual interface with English and Arabic options. Ensure the application is intuitive for both technical and non-technical users.

Performance and Offline Capability

The entire application must function without internet access. Avoid unnecessary API calls and database writes. Ensure frontend caching and quick response times. Minimize memory usage and optimize performance of the background processes and data handling.

Security

Sanitize all user inputs on both frontend and backend. Use secure authentication flows and protect tokens properly. Avoid exposing sensitive data. Follow best practices for data privacy and secure local storage. Prevent common vulnerabilities such as injection and XSS.

Testing and Maintainability

Write unit tests for backend logic and reusable components. Test the application thoroughly for both roles. Ensure maintainable code with clear folder structure and naming conventions. Write reusable and well-documented functions and components.

Output Expectations

You must produce production-ready, readable, and scalable code. Include all required error handling. Follow the defined architecture and code patterns. Ensure full local functionality. The system must be easy to extend in the future for cloud synchronization or mobile integration.