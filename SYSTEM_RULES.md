# 📋 قواعد النظام - SmartPOS System Rules

> **⚠️ إلزامي**: يجب على جميع المطورين قراءة هذا الملف قبل أي تطوير أو تعديل

## 🎯 القواعد الأساسية

### 1. **البروتوكول الإلزامي قبل أي تطوير**
```bash
1. 📖 قراءة SYSTEM_MEMORY.md كاملاً
2. 🔍 استخدام codebase-retrieval للبحث عن الوظائف المشابهة
3. 🔎 فحص الملفات والخدمات المرتبطة
4. 🏗️ تطبيق مبادئ البرمجة الكائنية
5. 🧪 اختبار الكود قبل التطبيق
```

### 2. **مبادئ البرمجة الكائنية (OOP)**
- **استخدام Classes** بدلاً من Functions منفصلة
- **تطبيق Encapsulation** - إخفاء التفاصيل الداخلية
- **Single Responsibility** - مسؤولية واحدة لكل فئة
- **فحص الأنظمة الموجودة** قبل إنشاء جديدة

### 3. **قواعد PostgreSQL (إلزامية)**
- **استخدام PostgreSQL حصرياً** - لا SQLite
- **استخدام دوال PostgreSQL** بدلاً من SQLite
- **تطبيق connection pooling** المحسن
- **استخدام pg_dump** للنسخ الاحتياطي
- **تجنب أي مراجع لـ SQLite** في الكود الجديد

### 4. **إدارة الأخطاء**
- **معالجة شاملة للأخطاء** مع try-catch
- **رسائل خطأ واضحة** باللغة العربية
- **تسجيل مفصل** للأخطاء في console
- **Recovery mechanisms** للتعافي من الأخطاء

## 🏗️ هيكل المشروع

### Backend Structure
```
backend/
├── services/           # الخدمات الأساسية (OOP Classes)
├── routers/           # API endpoints
├── middleware/        # الوسطاء الأمنية
├── config/           # ملفات التكوين (هنا فقط!)
├── backups/          # النسخ الاحتياطية (هنا فقط!)
└── models/           # نماذج البيانات
```

### Frontend Structure
```
frontend/src/
├── services/         # خدمات الواجهة الأمامية (Classes)
├── components/       # المكونات
├── hooks/           # React Hooks
└── utils/           # الأدوات المساعدة
```

## 🔍 بروتوكول البحث الإلزامي

### قبل إنشاء أي وظيفة جديدة:
```typescript
// 1. البحث الشامل
codebase-retrieval: "البحث عن [اسم الوظيفة المطلوبة]"
codebase-retrieval: "البحث عن [نوع الخدمة المطلوبة]"
codebase-retrieval: "البحث عن صفحات/مكونات مشابهة تعمل بشكل صحيح"
codebase-retrieval: "البحث عن خدمات التاريخ والوقت الموجودة"
codebase-retrieval: "البحث عن useDateTimeFormatters hook"
codebase-retrieval: "البحث عن FormattedDate و FormattedTime components"
codebase-retrieval: "البحث عن formatDateTime function"
codebase-retrieval: "البحث عن الأيقونات والألوان المستخدمة"

// 2. فحص الملفات ذات الصلة
- services/ - للخدمات الخلفية
- components/ - لمكونات الواجهة
- hooks/ - لـ React Hooks (خاصة useDateTimeFormatters)
- pages/ - للصفحات المشابهة (مهم جداً!)
- utils/ - للأدوات المساعدة الموجودة
- types/ - لأنواع البيانات المعرفة
```

### استراتيجية الحلول:
```bash
🎯 إذا وُجد حل ناجح مشابه:
   → دراسة النهج المستخدم بدقة
   → تطبيق نفس النمط/الاستراتيجية
   → تكييف الحل مع المتطلبات الحالية

✅ إذا وُجدت وظيفة مشابهة:
   → تحسين الموجود بدلاً من إنشاء جديد
   → إضافة الميزات المطلوبة للموجود

❌ إذا لم توجد وظيفة مشابهة:
   → التأكد من عدم وجود خدمة قريبة
   → إنشاء حل جديد بمبادئ OOP
```

## 🎨 معايير التصميم والتطوير الموحد

### 🎯 إرشادات التصميم الإلزامية
```typescript
// ✅ 1. استخدام الأيقونات المعتمدة فقط
import {
  FiMessageCircle,    // للمحادثة
  FiShield,          // للأمان
  FiDatabase,        // لقاعدة البيانات PostgreSQL
  FiSettings,        // للإعدادات
  FiUsers,           // للمستخدمين
  FiBarChart3,       // للتقارير
  FiPackage,         // للمنتجات
  FiShoppingCart     // للمبيعات
} from 'react-icons/fi';

// ✅ 2. استخدام نمط الألوان الموحد
const SYSTEM_COLORS = {
  primary: '#3b82f6',      // الأزرق الأساسي
  secondary: '#64748b',    // الرمادي الثانوي
  success: '#10b981',      // الأخضر للنجاح
  warning: '#f59e0b',      // الأصفر للتحذير
  danger: '#ef4444',       // الأحمر للخطر
  info: '#06b6d4',         // الأزرق الفاتح للمعلومات
  dark: '#1f2937',         // الداكن للوضع المظلم
  light: '#f8fafc'         // الفاتح للوضع المضيء
};

// ✅ 3. استخدام خدمة التاريخ والوقت الموحدة
import { dateTimeService } from '../services/dateTimeService';
```

### 🏗️ هيكلة الخدمات الجديدة
```typescript
// ✅ نمط الخدمة المعتمد (OOP Class)
export class NewFeatureService {
  private static instance: NewFeatureService;

  private constructor() {
    // التهيئة الخاصة
  }

  public static getInstance(): NewFeatureService {
    if (!NewFeatureService.instance) {
      NewFeatureService.instance = new NewFeatureService();
    }
    return NewFeatureService.instance;
  }

  // الطرق العامة للخدمة
  public async performAction(): Promise<any> {
    try {
      // منطق الخدمة
    } catch (error) {
      console.error('خطأ في الخدمة:', error);
      throw error;
    }
  }
}
```

## 📦 إدارة التبعيات

### استخدام Package Managers دائماً
```bash
# ✅ الطريقة الصحيحة
npm install --include=dev        # للتثبيت الآمن
npm install package-name         # لإضافة تبعية جديدة
npm install --save-dev package   # لتبعيات التطوير

# ❌ الطريقة الخاطئة
# تعديل package.json يدوياً
```

### حل مشاكل التبعيات:
```bash
# عند مواجهة مشاكل البناء:
rm -rf node_modules package-lock.json
npm cache clean --force
npm install --include=dev

# فحص صحة التثبيت:
ls node_modules/.bin/ | grep -E "(tsc|vite)"
```

## � قاعدة البيانات PostgreSQL

### إعدادات قاعدة البيانات الإلزامية
```bash
# ✅ قاعدة البيانات الرسمية للنظام
DATABASE_URL=postgresql+psycopg2://postgres:password@localhost:5432/smartpos_db

# ✅ إعدادات الاتصال المحسنة
POOL_SIZE=20                    # حجم تجميع الاتصالات
MAX_OVERFLOW=30                 # الحد الأقصى للاتصالات الإضافية
POOL_TIMEOUT=30                 # مهلة انتظار الاتصال
POOL_RECYCLE=3600              # إعادة تدوير الاتصالات (ثانية)
```

### قواعد التعامل مع قاعدة البيانات
```python
# ✅ استخدام خدمة قاعدة البيانات الموحدة
from database.session import get_db, engine
from services.database_migration_service import DatabaseMigrationService

# ✅ معالجة الاستعلامات بطريقة آمنة
with engine.connect() as conn:
    result = conn.execute(text("SELECT * FROM table_name"))
    conn.commit()

# ❌ تجنب الاستعلامات المباشرة بدون معالجة أخطاء
```

### خدمات الترحيل والنسخ الاحتياطي
- **DatabaseMigrationService**: خدمة الترحيل الرئيسية (OOP)
- **النسخ الاحتياطي**: PostgreSQL dump مع pg_dump
- **التحقق من البيانات**: فحص سلامة البيانات تلقائياً
- **الفهرسة المحسنة**: فهارس PostgreSQL متقدمة

## �🔒 الأمان والشبكة

### تكوين الشبكة
- **الأجهزة البعيدة**: استخدم network IPs (192.168.x.x:8002)
- **تجنب localhost** للأجهزة البعيدة
- **الخادم الرئيسي**: ************* معفى من فحص البصمة المتقدمة

### نظام البصمة
- **البصمة المتقدمة فقط** للأجهزة البعيدة
- **رفض الأجهزة** التي لا تحتوي على بصمة متقدمة
- **معرف موحد للخادم الرئيسي**: `main_server_primary`

### أمان قاعدة البيانات
- **تشفير الاتصالات**: SSL/TLS للاتصالات الآمنة
- **مصادقة قوية**: كلمات مرور معقدة ومتغيرات بيئة آمنة
- **فصل البيانات**: عزل بيانات الإنتاج عن التطوير
- **مراقبة الوصول**: تسجيل جميع عمليات الوصول لقاعدة البيانات

## 🎨 واجهة المستخدم والتصميم الموحد

### 🎯 التصميم الموحد للنظام
```typescript
// ✅ الطريقة الصحيحة - اتباع التصميم الموحد
// 1. استخدام الأيقونات المعتمدة في المشروع
import { FiMessageCircle, FiShield, FiDatabase } from 'react-icons/fi';

// 2. استخدام الألوان المعتمدة في التطبيق
const colors = {
  primary: '#3b82f6',      // الأزرق الأساسي
  secondary: '#64748b',    // الرمادي الثانوي
  success: '#10b981',      // الأخضر للنجاح
  warning: '#f59e0b',      // الأصفر للتحذير
  danger: '#ef4444',       // الأحمر للخطر
  dark: '#1f2937',         // الداكن للوضع المظلم
  light: '#f8fafc'         // الفاتح للوضع المضيء
};

// ❌ تجنب استخدام ألوان أو أيقونات مختلفة
```

### 🎨 معايير النمط الموحد للمكونات (إلزامية)

#### 📋 **1. مكونات الإدخال (Input Components)**
```typescript
// ✅ النمط الموحد المعتمد
import { SelectInput, NumberInput, TextInput, DatePicker } from '../components/inputs';

// معايير التصميم الإلزامية:
const unifiedInputStyle = {
  container: "w-full",
  input: "rounded-xl border-2 py-3 px-4 transition-all duration-200 ease-in-out",
  borders: "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500",
  focus: "focus:outline-none focus:ring-4 focus:ring-primary-500/20 focus:border-primary-500",
  background: "bg-white dark:bg-gray-700",
  text: "text-gray-900 dark:text-gray-100"
};

// ❌ ممنوع استخدام المكونات القديمة
import SelectBox from '../components/SelectBox'; // مهجور
```

#### 🎛️ **2. مفاتيح التبديل (Toggle Switches)**
```typescript
// ✅ النمط الموحد لمفاتيح التبديل
<div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-12 flex items-center">
  <ToggleSwitch
    id="toggle-id"
    checked={state}
    onChange={(checked) => setState(checked)}
    label="تسمية المفتاح"
    className="w-full"
  />
</div>

// معايير التصميم:
const toggleStyle = {
  container: "bg-white dark:bg-gray-700 p-4 rounded-xl border-2",
  borders: "border-gray-300 dark:border-gray-600",
  height: "h-12 flex items-center",
  transitions: "transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500"
};
```

#### 🔘 **3. الأزرار (Buttons)**
```typescript
// ✅ النمط الموحد للأزرار

// أزرار رئيسية (Primary)
const primaryButton = `
  bg-primary-600 hover:bg-primary-700 text-white
  px-6 py-3 rounded-xl transition-all duration-200 ease-in-out
  border-2 border-primary-600 hover:border-primary-700
  flex items-center justify-center text-sm font-medium
  focus:outline-none focus:ring-4 focus:ring-primary-500/20
  shadow-lg hover:shadow-xl min-w-[140px]
`;

// أزرار ثانوية (Secondary)
const secondaryButton = `
  bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500
  text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl
  transition-all duration-200 ease-in-out border-2
  border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500
  flex items-center justify-center text-sm font-medium min-w-[140px]
`;

// أزرار التبويبات (Tabs)
const tabButton = `
  py-4 px-6 border-b-2 font-medium text-sm flex items-center
  transition-all duration-200 ease-in-out
  ${active
    ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 shadow-sm'
    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/30'
  }
`;
```

#### 📐 **4. معايير التباعد والأحجام**
```typescript
// ✅ معايير موحدة للتباعد
const spacing = {
  // الحواف المستديرة
  rounded: "rounded-xl",           // للمكونات الرئيسية
  roundedLg: "rounded-lg",         // للعناصر الفرعية

  // التباعد الداخلي
  padding: {
    small: "p-3",                  // للعناصر الصغيرة
    medium: "p-4",                 // للمكونات العادية
    large: "p-6",                  // للحاويات الكبيرة
  },

  // التباعد الخارجي
  margin: {
    small: "gap-3",                // بين العناصر الصغيرة
    medium: "gap-4",               // بين المكونات العادية
    large: "gap-6",                // بين الأقسام
  },

  // الارتفاعات المعيارية
  heights: {
    input: "h-12",                 // للمدخلات
    button: "py-3",                // للأزرار
    toggle: "h-12",                // لمفاتيح التبديل
  }
};
```

#### 🎭 **5. التأثيرات والانتقالات**
```typescript
// ✅ تأثيرات موحدة
const effects = {
  // الانتقالات الأساسية
  transition: "transition-all duration-200 ease-in-out",

  // التركيز
  focus: "focus:outline-none focus:ring-4 focus:ring-primary-500/20",

  // التحويم
  hover: {
    scale: "hover:scale-105",      // للعناصر التفاعلية
    shadow: "hover:shadow-lg",     // للبطاقات
    border: "hover:border-gray-400 dark:hover:border-gray-500",
  },

  // الظلال
  shadows: {
    small: "shadow-md",
    medium: "shadow-lg",
    large: "shadow-xl",
  }
};
```

#### 🌙 **6. دعم الوضع المظلم**
```typescript
// ✅ دعم كامل للوضع المظلم
const darkModeSupport = {
  background: "bg-white dark:bg-gray-700",
  text: "text-gray-900 dark:text-gray-100",
  borders: "border-gray-300 dark:border-gray-600",
  hover: "hover:bg-gray-50 dark:hover:bg-gray-700/30",
  secondary: "bg-gray-100 dark:bg-gray-600",
};

// ❌ ممنوع: ألوان ثابتة لا تدعم الوضع المظلم
className="bg-white text-black"; // خطأ!
```

### 🕒 خدمات الوقت والتاريخ الموحدة
```typescript
// ✅ 1. الحصول على وظائف التنسيق من قاعدة البيانات
import { useDateTimeFormatters } from '../hooks/useDateTimeFormatters';

// استخدام Hook للحصول على وظائف التنسيق
const { formatDateTime, formatDate, formatTime } = useDateTimeFormatters();

// ✅ 2. استخدام مكونات التنسيق الجاهزة
import { FormattedDate, FormattedTime } from '../components/datetime';

// عرض التاريخ والوقت بالتنسيق المحفوظ في الإعدادات
<FormattedDate date={someDate} />
<FormattedTime time={someTime} />

// ✅ 3. تنسيق التواريخ برمجياً حسب الإعدادات
const formattedDateTime = formatDateTime(dateTime, 'full'); // تنسيق كامل
const formattedDate = formatDate(date);                     // تنسيق التاريخ فقط
const formattedTime = formatTime(time);                     // تنسيق الوقت فقط

// ✅ 4. استخدام خدمة التاريخ والوقت الموجودة (للحالات المتقدمة)
import { dateTimeService } from '../services/dateTimeService';

// ❌ تجنب إنشاء خدمات تاريخ ووقت جديدة
// ❌ تجنب التنسيق اليدوي بدون استخدام الإعدادات المحفوظة
```

### 🏗️ الهيكلية التنظيمية للميزات الجديدة
```bash
# ✅ الهيكل الصحيح لإضافة ميزة جديدة
feature_name/
├── services/
│   └── FeatureService.ts        # خدمة الميزة (OOP Class)
├── components/
│   ├── FeatureComponent.tsx     # المكون الرئيسي
│   ├── FeatureModal.tsx         # النوافذ المنبثقة
│   └── FeatureCard.tsx          # البطاقات
├── hooks/
│   └── useFeature.ts            # React Hook مخصص
├── types/
│   └── feature.types.ts         # أنواع TypeScript
└── utils/
    └── featureUtils.ts          # الأدوات المساعدة
```

### أنماط التحميل
```typescript
// ✅ الطريقة الصحيحة - استخدام useRef
const initialLoadDone = useRef(false);

useEffect(() => {
  if (initialLoadDone.current) return;

  // تحميل البيانات مرة واحدة
  initializeData();
  initialLoadDone.current = true;
}, []); // Empty dependency array

// ❌ تجنب useState للتحكم في التحميل الأولي
```

### تصدير PDF
- **استخدام jsPDF مباشرة** للحصول على أفضل جودة
- **دمج خدمة التاريخ والوقت** الموجودة
- **تحميل مباشر للملفات** بدلاً من نوافذ الطباعة
- **دعم RTL والنصوص العربية**

## 🚫 ممنوع تماماً

```bash
❌ إنشاء وظائف جديدة بدون فحص الموجود أولاً
❌ تعديل package.json يدوياً
❌ استخدام eval() أو string evaluation
❌ إنشاء ملفات config مكررة في مسارات مختلفة
❌ استخدام localhost للأجهزة البعيدة
❌ تجاهل مبادئ البرمجة الكائنية
❌ إنشاء مجلدات backups في الجذر
❌ استخدام Canvas + Print Window لتصدير PDF
❌ عدم تطبيق نظام cache ذكي للبيانات
❌ تطبيق فحص البصمة المتقدمة على الخادم الرئيسي
❌ استخدام ألوان أو أيقونات مختلفة عن التصميم الموحد
❌ إنشاء خدمات تاريخ ووقت جديدة بدلاً من استخدام الموجودة
❌ تكرار كود التنسيق بدلاً من استخدام useDateTimeFormatters
❌ التنسيق اليدوي للتواريخ بدلاً من استخدام FormattedDate/FormattedTime
❌ عدم فصل الخدمات في ملفات منفصلة
❌ تجاهل الهيكلية التنظيمية المعتمدة للمشروع
❌ عدم اتباع نمط الألوان المعتمد في التطبيق
❌ استخدام SQLite في النظام الجديد (تم الترقية إلى PostgreSQL)
❌ تجاهل خدمة DatabaseMigrationService للترحيل
❌ عدم استخدام connection pooling في PostgreSQL
❌ تجاهل إعدادات الأمان لقاعدة البيانات
```

## ✅ مطلوب دائماً

```bash
✅ استخدام Classes بدلاً من Functions منفصلة
✅ معالجة شاملة للأخطاء مع try-catch
✅ فحص الأنظمة الموجودة قبل إنشاء جديدة
✅ استخدام network IPs للأجهزة البعيدة
✅ الاحتفاظ بملفات config في backend/config فقط
✅ استخدام معرفات موحدة للأجهزة
✅ اختبار النظام بعد كل تغيير مهم
✅ استخدام npm install --include=dev عند مشاكل devDependencies
✅ دمج خدمة التاريخ والوقت في جميع خدمات PDF
✅ تطبيق معالجة متزامنة للبيانات في الخلفية
✅ ربط الخدمات الموجودة بدلاً من إنشاء حلول جديدة
✅ اتباع التصميم الموحد للنظام في جميع الميزات الجديدة
✅ استخدام الأيقونات المعتمدة في المشروع (react-icons/fi)
✅ استخدام خدمات الوقت والتاريخ الموجودة (dateTimeService)
✅ استخدام useDateTimeFormatters للحصول على وظائف التنسيق من قاعدة البيانات
✅ استخدام FormattedDate و FormattedTime لعرض التواريخ والأوقات
✅ استخدام formatDateTime لتنسيق التواريخ حسب الإعدادات المحفوظة
✅ فصل كل خدمة في ملف منفصل ضمن مجلد services/
✅ اتباع الهيكلية التنظيمية المعتمدة للمشروع
✅ تطبيق مبادئ البرمجة الكائنية في جميع الخدمات
✅ استخدام نمط الألوان المعتمد في التطبيق
✅ إنشاء مكونات قابلة لإعادة الاستخدام
✅ توثيق جميع الخدمات والمكونات الجديدة
✅ تطبيق النمط الموحد للتصميم في جميع المكونات الجديدة
✅ استخدام PostgreSQL كقاعدة البيانات الرسمية للنظام
✅ تطبيق خدمة DatabaseMigrationService للترحيل الآمن
✅ استخدام connection pooling المحسن لـ PostgreSQL
✅ تطبيق إعدادات الأمان المتقدمة لقاعدة البيانات
✅ استخدام pg_dump للنسخ الاحتياطي المتقدم
```

## 🔍 قائمة التحقق من الجودة

### ✅ قبل تطبيق أي ميزة جديدة:
```bash
□ تم البحث في الكود الموجود باستخدام codebase-retrieval
□ تم فحص الخدمات المشابهة الموجودة
□ تم اتباع مبادئ البرمجة الكائنية (OOP)
□ تم استخدام الأيقونات المعتمدة من react-icons/fi
□ تم استخدام نمط الألوان الموحد للتطبيق
□ تم استخدام خدمة التاريخ والوقت الموجودة
□ تم استخدام useDateTimeFormatters للحصول على وظائف التنسيق
□ تم استخدام FormattedDate/FormattedTime لعرض التواريخ والأوقات
□ تم استخدام formatDateTime لتنسيق التواريخ حسب الإعدادات
□ تم فصل الخدمة في ملف منفصل
□ تم اتباع الهيكلية التنظيمية المعتمدة
□ تم إضافة معالجة شاملة للأخطاء
□ تم اختبار الميزة في البيئة المحلية
□ تم توثيق الميزة الجديدة
□ تم التأكد من التوافق مع الوضع المظلم والمضيء
□ تم التأكد من التوافق مع PostgreSQL
□ تم استخدام connection pooling المناسب
□ تم تطبيق إعدادات الأمان لقاعدة البيانات
```

### 🎯 معايير المراجعة للذكاء الاصطناعي
```typescript
// ✅ يجب أن يكون الكود قابلاً للفهم من قبل أي وكيل ذكاء اصطناعي
// 1. تعليقات واضحة باللغة العربية
// 2. أسماء متغيرات وصفية
// 3. هيكلة منطقية للكود
// 4. فصل الاهتمامات (Separation of Concerns)

// مثال على التعليق الواضح:
/**
 * خدمة إدارة المحادثة الفورية
 * تتعامل مع إرسال واستقبال الرسائل عبر WebSocket
 * تطبق مبادئ البرمجة الكائنية مع نمط Singleton
 */
export class ChatService {
  // ...
}
```

## 📝 التوثيق

### بعد كل مهمة:
1. **تحديث SYSTEM_MEMORY.md** بالقواعد الجديدة
2. **إنشاء توثيق مفصل** في مجلد docs/
3. **توثيق الدروس المستفادة**
4. **تحديث أمثلة الاستخدام**
5. **توثيق الأيقونات والألوان المستخدمة**
6. **توثيق الخدمات الجديدة مع أمثلة الاستخدام**

### هيكل التوثيق:
```
docs/
├── features/          # الميزات الجديدة
├── guides/           # أدلة الاستخدام
├── updates/          # التحديثات والتحسينات
├── design/           # معايير التصميم والألوان
└── archived/         # التوثيقات القديمة
```

## 🔄 سير العمل

```mermaid
graph TD
    A[📋 استلام المهمة] --> B[📖 قراءة SYSTEM_MEMORY.md]
    B --> C[🔍 البحث في الكود الموجود]
    C --> D{هل توجد وظيفة مشابهة؟}
    D -->|نعم| E[🔄 تحسين الموجود]
    D -->|لا| F[🏗️ إنشاء جديد بمبادئ OOP]
    E --> G[🧪 اختبار الحل]
    F --> G
    G --> H[📝 توثيق التغييرات]
    H --> I[✅ تطبيق الحل]
```

## 🎯 خلاصة مهمة

> **⚠️ تذكر دائماً**: هذا النظام يتطلب دقة عالية في التعامل مع الأمان والاستقرار.
> كل تغيير يجب أن يكون مدروساً ومختبراً جيداً.

> **🤖 للذكاء الاصطناعي**: يجب قراءة هذا الملف بالكامل قبل تنفيذ أي مهمة في المشروع.
> اتباع هذه القواعد ضروري لضمان التوافق والاستقرار.

### 🔑 النقاط الأساسية للذكاء الاصطناعي:
1. **🔍 البحث أولاً**: استخدم codebase-retrieval قبل إنشاء أي شيء جديد
2. **🎨 التصميم الموحد**: اتبع الألوان والأيقونات المعتمدة
3. **🕒 وظائف التنسيق**: استخدم useDateTimeFormatters و FormattedDate/FormattedTime و formatDateTime
4. **🕒 الخدمات الموجودة**: استخدم dateTimeService للتاريخ والوقت (للحالات المتقدمة)
5. **🏗️ البرمجة الكائنية**: طبق مبادئ OOP في جميع الخدمات
6. **📁 الهيكلية**: فصل كل خدمة في ملف منفصل
7. **🗄️ قاعدة البيانات**: استخدم PostgreSQL حصرياً (لا SQLite)
8. **🔄 الترحيل**: استخدم DatabaseMigrationService للترحيل الآمن
9. **📝 التوثيق**: وثق كل تغيير بوضوح

## 🐘 قواعد PostgreSQL المحدثة

### 📋 إعدادات قاعدة البيانات
```python
# إعدادات PostgreSQL الرسمية
DATABASE_URL = "postgresql+psycopg2://postgres:password@localhost:5432/smartpos_db"

# إعدادات الأداء المحسن
DATABASE_CONFIG = {
    "pool_size": 20,
    "max_overflow": 30,
    "pool_timeout": 30,
    "pool_recycle": 3600,
    "pool_pre_ping": True,
}
```

### 🔧 دوال PostgreSQL المعتمدة
```sql
-- بدلاً من SQLite pragma functions
SELECT pg_database_size(current_database());  -- حجم قاعدة البيانات
SHOW max_connections;                          -- الاتصالات القصوى
SHOW shared_buffers;                          -- ذاكرة التخزين المؤقت

-- بدلاً من SQLite date functions
SELECT func.date(created_at) = '2025-07-10';  -- مقارنة التاريخ
SELECT NOW();                                 -- التاريخ الحالي
```

### 🚫 ممنوع استخدامها (SQLite)
```python
# ❌ لا تستخدم هذه الدوال
.like(f"{date}%")           # استخدم func.date() بدلاً منها
pragma_page_count()         # استخدم pg_database_size()
pragma_page_size()          # استخدم pg_database_size()
PRAGMA journal_mode         # استخدم SHOW commands
```

### 📦 النسخ الاحتياطي
```bash
# استخدم pg_dump للنسخ الاحتياطي
pg_dump --host=localhost --port=5432 --username=postgres --dbname=smartpos_db --file=backup.sql

# ❌ لا تستخدم SQLite backup methods
```

### 🧪 اختبار التوافق
```python
# استخدم سكربت الاختبار الشامل
python fix_postgresql_compatibility.py

# تحقق من النتائج
✅ PostgreSQL متصل ويعمل بنجاح
✅ WebSocket متوافق 100% مع PostgreSQL
✅ جميع الوظائف الأساسية تعمل
```

## 📚 مراجع PostgreSQL

### 📖 ملفات التوثيق
- [دليل ترقية PostgreSQL](docs/guides/POSTGRESQL_MIGRATION_GUIDE.md)
- [إصلاحات التوافق](docs/guides/POSTGRESQL_COMPATIBILITY_FIXES.md)
- [تحديث النظام الشامل](docs/updates/POSTGRESQL_SYSTEM_UPDATE.md)
- [تحديث النظام الموحد للتصميم](docs/updates/UNIFIED_DESIGN_SYSTEM_UPDATE.md)

### 🔗 روابط مفيدة
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [SQLAlchemy PostgreSQL](https://docs.sqlalchemy.org/en/14/dialects/postgresql.html)
- [psycopg2 Documentation](https://www.psycopg.org/docs/)

---

## ⚠️ تذكير مهم

**النظام الآن يعمل بـ PostgreSQL حصرياً. أي كود جديد يجب أن يتوافق مع PostgreSQL ولا يحتوي على أي مراجع لـ SQLite.**

---

**آخر تحديث**: 11 يوليو 2025 - تحديث النظام الموحد للتصميم v2.1.0
**للمراجعة الشاملة**: راجع `SYSTEM_MEMORY.md`
**للتوثيق المفصل**: راجع مجلد `docs/`
**للتصميم والألوان**: راجع `docs/design/`
