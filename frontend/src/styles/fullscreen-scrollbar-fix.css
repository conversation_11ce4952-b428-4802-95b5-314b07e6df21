/*
 * 🔧 إصلاح شريط التمرير في وضع ملء الشاشة
 * 🌙 حل مشكلة عدم توافق الوضع المظلم مع ملء الشاشة
 * 🎯 يطبق إعدادات خاصة لضمان ظهور شريط التمرير بشكل صحيح
 */

/* ========================================
   إصلاح شريط التمرير في ملء الشاشة
   ======================================== */

/* إعدادات عامة لملء الشاشة */
:fullscreen,
:-webkit-full-screen,
:-moz-full-screen,
:-ms-fullscreen {
  /* التأكد من أن شريط التمرير يعمل */
  overflow: auto !important;
}

/* شريط التمرير الرئيسي في ملء الشاشة */
:fullscreen::-webkit-scrollbar,
:-webkit-full-screen::-webkit-scrollbar,
:-moz-full-screen::-webkit-scrollbar,
:-ms-fullscreen::-webkit-scrollbar {
  width: 12px !important;
  height: 12px !important;
  -webkit-appearance: none !important;
}

:fullscreen::-webkit-scrollbar-track,
:-webkit-full-screen::-webkit-scrollbar-track,
:-moz-full-screen::-webkit-scrollbar-track,
:-ms-fullscreen::-webkit-scrollbar-track {
  background: transparent !important;
  border-radius: 12px !important;
  border: none !important;
}

:fullscreen::-webkit-scrollbar-thumb,
:-webkit-full-screen::-webkit-scrollbar-thumb,
:-moz-full-screen::-webkit-scrollbar-thumb,
:-ms-fullscreen::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.4) !important;
  border-radius: 12px !important;
  transition: all 0.3s ease !important;
  border: none !important;
  -webkit-appearance: none !important;
}

:fullscreen::-webkit-scrollbar-thumb:hover,
:-webkit-full-screen::-webkit-scrollbar-thumb:hover,
:-moz-full-screen::-webkit-scrollbar-thumb:hover,
:-ms-fullscreen::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.7) !important;
}

/* ========================================
   الوضع المظلم في ملء الشاشة - الإصلاح الرئيسي
   ======================================== */

/* إعادة تعريف شريط التمرير في الوضع المظلم في ملء الشاشة */
html.dark:fullscreen::-webkit-scrollbar-thumb,
body.dark:fullscreen::-webkit-scrollbar-thumb,
html.dark:-webkit-full-screen::-webkit-scrollbar-thumb,
body.dark:-webkit-full-screen::-webkit-scrollbar-thumb,
html.dark:-moz-full-screen::-webkit-scrollbar-thumb,
body.dark:-moz-full-screen::-webkit-scrollbar-thumb,
html.dark:-ms-fullscreen::-webkit-scrollbar-thumb,
body.dark:-ms-fullscreen::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.6) !important;
}

html.dark:fullscreen::-webkit-scrollbar-thumb:hover,
body.dark:fullscreen::-webkit-scrollbar-thumb:hover,
html.dark:-webkit-full-screen::-webkit-scrollbar-thumb:hover,
body.dark:-webkit-full-screen::-webkit-scrollbar-thumb:hover,
html.dark:-moz-full-screen::-webkit-scrollbar-thumb:hover,
body.dark:-moz-full-screen::-webkit-scrollbar-thumb:hover,
html.dark:-ms-fullscreen::-webkit-scrollbar-thumb:hover,
body.dark:-ms-fullscreen::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.8) !important;
}

/* الوضع المظلم - شريط التمرير الرئيسي - طرق إضافية */
:fullscreen.dark::-webkit-scrollbar-thumb,
:-webkit-full-screen.dark::-webkit-scrollbar-thumb,
:-moz-full-screen.dark::-webkit-scrollbar-thumb,
:-ms-fullscreen.dark::-webkit-scrollbar-thumb,
.dark :fullscreen::-webkit-scrollbar-thumb,
.dark :-webkit-full-screen::-webkit-scrollbar-thumb,
.dark :-moz-full-screen::-webkit-scrollbar-thumb,
.dark :-ms-fullscreen::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.6) !important;
}

:fullscreen.dark::-webkit-scrollbar-thumb:hover,
:-webkit-full-screen.dark::-webkit-scrollbar-thumb:hover,
:-moz-full-screen.dark::-webkit-scrollbar-thumb:hover,
:-ms-fullscreen.dark::-webkit-scrollbar-thumb:hover,
.dark :fullscreen::-webkit-scrollbar-thumb:hover,
.dark :-webkit-full-screen::-webkit-scrollbar-thumb:hover,
.dark :-moz-full-screen::-webkit-scrollbar-thumb:hover,
.dark :-ms-fullscreen::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.8) !important;
}

/* إجبار التحديث للوضع المظلم */
[data-theme="dark"]:fullscreen::-webkit-scrollbar-thumb,
[data-theme="dark"]:-webkit-full-screen::-webkit-scrollbar-thumb,
[data-theme="dark"]:-moz-full-screen::-webkit-scrollbar-thumb,
[data-theme="dark"]:-ms-fullscreen::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.6) !important;
}

[data-theme="dark"]:fullscreen::-webkit-scrollbar-thumb:hover,
[data-theme="dark"]:-webkit-full-screen::-webkit-scrollbar-thumb:hover,
[data-theme="dark"]:-moz-full-screen::-webkit-scrollbar-thumb:hover,
[data-theme="dark"]:-ms-fullscreen::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.8) !important;
}

/* ========================================
   العناصر الفرعية في ملء الشاشة
   ======================================== */

/* جميع العناصر داخل ملء الشاشة */
:fullscreen *::-webkit-scrollbar,
:-webkit-full-screen *::-webkit-scrollbar,
:-moz-full-screen *::-webkit-scrollbar,
:-ms-fullscreen *::-webkit-scrollbar {
  width: 10px !important;
  height: 10px !important;
  -webkit-appearance: none !important;
}

:fullscreen *::-webkit-scrollbar-track,
:-webkit-full-screen *::-webkit-scrollbar-track,
:-moz-full-screen *::-webkit-scrollbar-track,
:-ms-fullscreen *::-webkit-scrollbar-track {
  background: transparent !important;
  border-radius: 10px !important;
  border: none !important;
}

:fullscreen *::-webkit-scrollbar-thumb,
:-webkit-full-screen *::-webkit-scrollbar-thumb,
:-moz-full-screen *::-webkit-scrollbar-thumb,
:-ms-fullscreen *::-webkit-scrollbar-thumb {
  background: transparent !important;
  border-radius: 10px !important;
  transition: all 0.3s ease !important;
  border: none !important;
  -webkit-appearance: none !important;
}

/* إظهار عند التحويم */
:fullscreen *:hover::-webkit-scrollbar-thumb,
:fullscreen *:focus-within::-webkit-scrollbar-thumb,
:-webkit-full-screen *:hover::-webkit-scrollbar-thumb,
:-webkit-full-screen *:focus-within::-webkit-scrollbar-thumb,
:-moz-full-screen *:hover::-webkit-scrollbar-thumb,
:-moz-full-screen *:focus-within::-webkit-scrollbar-thumb,
:-ms-fullscreen *:hover::-webkit-scrollbar-thumb,
:-ms-fullscreen *:focus-within::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5) !important;
}

:fullscreen *::-webkit-scrollbar-thumb:hover,
:-webkit-full-screen *::-webkit-scrollbar-thumb:hover,
:-moz-full-screen *::-webkit-scrollbar-thumb:hover,
:-ms-fullscreen *::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.7) !important;
}

/* ========================================
   الوضع المظلم للعناصر الفرعية
   ======================================== */

/* العناصر الفرعية في الوضع المظلم */
:fullscreen.dark *:hover::-webkit-scrollbar-thumb,
:fullscreen.dark *:focus-within::-webkit-scrollbar-thumb,
:-webkit-full-screen.dark *:hover::-webkit-scrollbar-thumb,
:-webkit-full-screen.dark *:focus-within::-webkit-scrollbar-thumb,
:-moz-full-screen.dark *:hover::-webkit-scrollbar-thumb,
:-moz-full-screen.dark *:focus-within::-webkit-scrollbar-thumb,
:-ms-fullscreen.dark *:hover::-webkit-scrollbar-thumb,
:-ms-fullscreen.dark *:focus-within::-webkit-scrollbar-thumb,
.dark :fullscreen *:hover::-webkit-scrollbar-thumb,
.dark :fullscreen *:focus-within::-webkit-scrollbar-thumb,
.dark :-webkit-full-screen *:hover::-webkit-scrollbar-thumb,
.dark :-webkit-full-screen *:focus-within::-webkit-scrollbar-thumb,
.dark :-moz-full-screen *:hover::-webkit-scrollbar-thumb,
.dark :-moz-full-screen *:focus-within::-webkit-scrollbar-thumb,
.dark :-ms-fullscreen *:hover::-webkit-scrollbar-thumb,
.dark :-ms-fullscreen *:focus-within::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.5) !important;
}

:fullscreen.dark *::-webkit-scrollbar-thumb:hover,
:-webkit-full-screen.dark *::-webkit-scrollbar-thumb:hover,
:-moz-full-screen.dark *::-webkit-scrollbar-thumb:hover,
:-ms-fullscreen.dark *::-webkit-scrollbar-thumb:hover,
.dark :fullscreen *::-webkit-scrollbar-thumb:hover,
.dark :-webkit-full-screen *::-webkit-scrollbar-thumb:hover,
.dark :-moz-full-screen *::-webkit-scrollbar-thumb:hover,
.dark :-ms-fullscreen *::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.7) !important;
}

/* ========================================
   Firefox في ملء الشاشة
   ======================================== */

/* Firefox - الوضع العادي */
:fullscreen,
:-moz-full-screen {
  scrollbar-width: thin !important;
  scrollbar-color: rgba(156, 163, 175, 0.4) transparent !important;
}

/* Firefox - الوضع المظلم */
:fullscreen.dark,
:-moz-full-screen.dark,
.dark :fullscreen,
.dark :-moz-full-screen {
  scrollbar-color: rgba(75, 85, 99, 0.5) transparent !important;
}

/* Firefox - العناصر الفرعية */
:fullscreen *,
:-moz-full-screen * {
  scrollbar-width: thin !important;
  scrollbar-color: transparent transparent !important;
}

:fullscreen *:hover,
:fullscreen *:focus-within,
:-moz-full-screen *:hover,
:-moz-full-screen *:focus-within {
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent !important;
}

:fullscreen.dark *:hover,
:fullscreen.dark *:focus-within,
:-moz-full-screen.dark *:hover,
:-moz-full-screen.dark *:focus-within,
.dark :fullscreen *:hover,
.dark :fullscreen *:focus-within,
.dark :-moz-full-screen *:hover,
.dark :-moz-full-screen *:focus-within {
  scrollbar-color: rgba(75, 85, 99, 0.5) transparent !important;
}

/* ========================================
   إزالة الأسهم في ملء الشاشة
   ======================================== */

/* إزالة جميع أسهم التمرير في ملء الشاشة */
:fullscreen *::-webkit-scrollbar-button,
:-webkit-full-screen *::-webkit-scrollbar-button,
:-moz-full-screen *::-webkit-scrollbar-button,
:-ms-fullscreen *::-webkit-scrollbar-button {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  background: none !important;
}

/* ========================================
   تحسينات إضافية
   ======================================== */

/* التأكد من عدم وجود مشاكل في التمرير */
:fullscreen,
:-webkit-full-screen,
:-moz-full-screen,
:-ms-fullscreen {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* إزالة أي تأثيرات غير مرغوب فيها */
:fullscreen *::-webkit-scrollbar-corner,
:-webkit-full-screen *::-webkit-scrollbar-corner,
:-moz-full-screen *::-webkit-scrollbar-corner,
:-ms-fullscreen *::-webkit-scrollbar-corner {
  background: transparent !important;
  display: none !important;
}

/*
 * 🎯 ملخص الإصلاح:
 * 
 * ✅ إصلاح شريط التمرير في ملء الشاشة
 * ✅ دعم كامل للوضع المظلم في ملء الشاشة
 * ✅ توافق مع جميع المتصفحات
 * ✅ إزالة الأسهم في جميع الحالات
 * ✅ ألوان متوافقة مع التصميم
 * ✅ انتقالات سلسة
 */
