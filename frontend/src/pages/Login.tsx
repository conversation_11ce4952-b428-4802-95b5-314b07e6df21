import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { TextInput } from '../components/inputs';
import { <PERSON>a<PERSON>ser, FaLock, FaSun, FaMoon, FaShieldAlt, FaChartLine, FaBoxes, FaReceipt, FaEye, FaEyeSlash } from 'react-icons/fa';

const Login: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('theme') === 'dark' ||
             (!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches);
    }
    return false;
  });

  const { login, error: authError, clearAuth } = useAuthStore();
  const navigate = useNavigate();

  // Toggle theme
  const toggleTheme = () => {
    const newTheme = isDarkMode ? 'light' : 'dark';
    setIsDarkMode(!isDarkMode);
    localStorage.setItem('theme', newTheme);
    document.documentElement.classList.toggle('dark', !isDarkMode);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await login(username, password);
      navigate('/');
    } catch (err) {
      // Error is handled by the store
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-3 sm:p-4 relative overflow-hidden">
      {/* Background Pattern - Responsive */}
      <div className="absolute inset-0 opacity-5 dark:opacity-10">
        <div className="absolute top-5 sm:top-10 left-5 sm:left-10 w-16 sm:w-20 h-16 sm:h-20 bg-primary-500 rounded-full blur-xl"></div>
        <div className="absolute top-32 sm:top-40 right-10 sm:right-20 w-24 sm:w-32 h-24 sm:h-32 bg-purple-500 rounded-full blur-xl"></div>
        <div className="absolute bottom-16 sm:bottom-20 left-1/4 w-20 sm:w-24 h-20 sm:h-24 bg-blue-500 rounded-full blur-xl"></div>
        <div className="absolute bottom-32 sm:bottom-40 right-5 sm:right-10 w-12 sm:w-16 h-12 sm:h-16 bg-green-500 rounded-full blur-xl"></div>
      </div>

      {/* Theme Toggle Button - Mobile Optimized */}
      <button
        onClick={toggleTheme}
        className="absolute top-4 sm:top-6 right-4 sm:right-6 p-2.5 sm:p-3 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-200 hover:bg-white dark:hover:bg-gray-800 transition-all duration-300 hover:scale-110 shadow-lg z-20"
        aria-label={isDarkMode ? 'التبديل إلى الوضع المضيء' : 'التبديل إلى الوضع المظلم'}
      >
        {isDarkMode ? <FaSun className="text-yellow-500 text-lg sm:text-xl" /> : <FaMoon className="text-gray-700 text-lg sm:text-xl" />}
      </button>

      {/* Main Container - Mobile Responsive */}
      <div className="w-full max-w-sm sm:max-w-md relative z-10">
        {/* Login Card - Mobile Responsive */}
        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-2xl sm:rounded-3xl shadow-2xl border border-white/20 dark:border-gray-700/50 p-5 sm:p-8 relative overflow-hidden">
          {/* Card Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute -top-10 -right-10 w-32 sm:w-40 h-32 sm:h-40 bg-primary-500 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-10 -left-10 w-24 sm:w-32 h-24 sm:h-32 bg-purple-500 rounded-full blur-3xl"></div>
          </div>

          {/* Logo Section - Mobile Optimized */}
          <div className="relative z-10 text-center mb-6 sm:mb-8">
            <div className="inline-flex items-center justify-center w-16 sm:w-20 h-16 sm:h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl sm:rounded-2xl shadow-lg mb-3 sm:mb-4 transform hover:scale-105 transition-transform duration-300">
              <FaShieldAlt className="text-2xl sm:text-3xl text-white" />
            </div>
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-primary-600 to-purple-600 bg-clip-text text-transparent mb-1 sm:mb-2">
              Smart POS
            </h1>
            <p className="text-gray-600 dark:text-gray-400 text-xs sm:text-sm font-medium">
              نظام نقاط البيع الذكي
            </p>
          </div>

          {/* Features Icons - Mobile Responsive */}
          <div className="flex justify-center gap-4 sm:gap-6 mb-6 sm:mb-8">
            <div className="flex flex-col items-center group">
              <div className="w-10 sm:w-12 h-10 sm:h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg sm:rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <FaChartLine className="text-blue-600 dark:text-blue-400 text-sm sm:text-lg" />
              </div>
              <span className="text-xs text-gray-600 dark:text-gray-400 mt-1 font-medium">التقارير</span>
            </div>
            <div className="flex flex-col items-center group">
              <div className="w-10 sm:w-12 h-10 sm:h-12 bg-green-100 dark:bg-green-900/30 rounded-lg sm:rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <FaBoxes className="text-green-600 dark:text-green-400 text-sm sm:text-lg" />
              </div>
              <span className="text-xs text-gray-600 dark:text-gray-400 mt-1 font-medium">المخزون</span>
            </div>
            <div className="flex flex-col items-center group">
              <div className="w-10 sm:w-12 h-10 sm:h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg sm:rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <FaReceipt className="text-purple-600 dark:text-purple-400 text-sm sm:text-lg" />
              </div>
              <span className="text-xs text-gray-600 dark:text-gray-400 mt-1 font-medium">المبيعات</span>
            </div>
          </div>

          {/* Error Message - Mobile Responsive */}
          {authError && (
            <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-xl animate-fadeIn">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-7 sm:w-8 h-7 sm:h-8 bg-red-100 dark:bg-red-800 rounded-full flex items-center justify-center">
                    <FaLock className="text-red-600 dark:text-red-400 text-xs sm:text-sm" />
                  </div>
                </div>
                <div className="mr-2 sm:mr-3">
                  <p className="text-xs sm:text-sm text-red-700 dark:text-red-300 font-medium">{authError}</p>
                </div>
              </div>
            </div>
          )}

          {/* Login Form - Mobile Responsive */}
          <form onSubmit={handleSubmit} className="relative z-10 space-y-4 sm:space-y-6">
            {/* Username Field */}
            <div className="space-y-2">
              <TextInput
                name="username"
                label="اسم المستخدم"
                value={username}
                onChange={setUsername}
                placeholder="أدخل اسم المستخدم"
                icon={<FaUser />}
                required
                autoFocus
                dir="rtl"
                className="transform transition-all duration-300 hover:scale-[1.02] focus-within:scale-[1.02]"
              />
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <div className="relative">
                <TextInput
                  name="password"
                  label="كلمة المرور"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={setPassword}
                  placeholder="أدخل كلمة المرور"
                  icon={<FaLock />}
                  required
                  dir="rtl"
                  className="transform transition-all duration-300 hover:scale-[1.02] focus-within:scale-[1.02]"
                />
                {/* Show/Hide Password Button - Mobile Optimized */}
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-2 sm:left-3 top-[38px] p-2 sm:p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors duration-200 touch-manipulation"
                  tabIndex={-1}
                  aria-label={showPassword ? 'إخفاء كلمة المرور' : 'إظهار كلمة المرور'}
                >
                  {showPassword ? <FaEyeSlash className="text-sm" /> : <FaEye className="text-sm" />}
                </button>
              </div>
            </div>

            {/* Login Button - Mobile Optimized */}
            <div className="pt-3 sm:pt-4">
              <button
                type="submit"
                disabled={isLoading || !username.trim() || !password.trim()}
                className={`
                  w-full py-3.5 sm:py-4 px-6 rounded-xl font-semibold text-white transition-all duration-300 transform touch-manipulation
                  ${isLoading || !username.trim() || !password.trim()
                    ? 'bg-gray-400 dark:bg-gray-600 cursor-not-allowed'
                    : 'bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 hover:scale-[1.02] hover:shadow-lg active:scale-[0.98]'
                  }
                  focus:outline-none focus:ring-4 focus:ring-primary-500/30
                  shadow-lg text-base sm:text-lg
                `}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center gap-2 sm:gap-3">
                    <div className="w-4 sm:w-5 h-4 sm:h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span className="text-sm sm:text-base">جاري تسجيل الدخول...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center gap-2 sm:gap-3">
                    <FaShieldAlt className="text-base sm:text-lg" />
                    <span className="text-sm sm:text-base">تسجيل الدخول</span>
                  </div>
                )}
              </button>
            </div>
          </form>

          {/* Clear Auth Button (for troubleshooting) - Mobile Responsive */}
          {authError && (
            <div className="relative z-10 mt-3 sm:mt-4 text-center">
              <button
                onClick={() => {
                  clearAuth();
                  window.location.reload();
                }}
                className="text-xs text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 underline touch-manipulation py-1"
              >
                مسح جميع بيانات الجلسة وإعادة المحاولة
              </button>
            </div>
          )}

          {/* Footer - Mobile Responsive */}
          <div className="relative z-10 mt-6 sm:mt-8 text-center">
            <div className="flex items-center justify-center space-x-3 sm:space-x-4 space-x-reverse mb-3 sm:mb-4">
              <div className="h-px bg-gray-300 dark:bg-gray-600 flex-1"></div>
              <span className="text-xs text-gray-500 dark:text-gray-400 px-2 sm:px-3 font-medium">نظام آمن ومحمي</span>
              <div className="h-px bg-gray-300 dark:bg-gray-600 flex-1"></div>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 leading-relaxed">
              الإصدار 1.0.0 | جميع الحقوق محفوظة © 2025
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;