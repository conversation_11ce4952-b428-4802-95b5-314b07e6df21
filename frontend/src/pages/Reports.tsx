import React, { useEffect, useState, useRef, useMemo } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import {
  FaChartBar,
  FaChartLine,
  FaChartPie,
  FaCalendarDay,
  FaCalendarWeek,
  FaCalendarAlt,
  FaCalendar,
  FaDownload,
  FaPrint,
  FaArrowUp,
  FaArrowDown,
  FaArrowLeft,
  FaShoppingCart,
  FaSync,
  FaServer,
  FaUsers,
  FaDatabase,
  FaClock,
  FaHdd,
  FaUserCheck,
  FaCog,
  FaTrash,

  FaExclamationTriangle,
  FaExclamationCircle,
  FaCalendarCheck,
  FaMoneyBillWave,
  FaBug,
  FaFlask,
  FaCoins,
  FaCloud,
  FaNetworkWired,
  FaUserShield,
  FaTrophy
} from 'react-icons/fa';
import { useTheme } from '../contexts/ThemeContext';
import ReactApexChart from 'react-apexcharts';
import { useReportsStore } from '../stores/reportsStore';
import { useAuthStore } from '../stores/authStore';
import apiClient from '../lib/axios';
import { ApexOptions } from 'apexcharts';
import { getCurrentTripoliDateTime, formatDateWithSettings } from '../services/dateTimeService';
import { DatePicker } from '../components/inputs';
import FormattedCurrency from '../components/FormattedCurrency';
import { LastUpdateTime, useCurrentFormattedDateTime, FormattedDate } from '../components/FormattedDateTime';
import { useCurrencySettings } from '../utils/currencyUtils';
import { CompactStatCard, GrowthRateCard } from '../components/CompactNumberDisplay';
import { useDateTimeSettings } from '../hooks/useDateTimeSettings';
import { ReportType, ReportPeriod } from '../stores/reportsStore';
import DeleteConfirmModal from '../components/DeleteConfirmModal';
import RestoreConfirmModal from '../components/RestoreConfirmModal';
import SuccessModal from '../components/SuccessModal';
import ConfirmModal from '../components/ConfirmModal';
import AllBackupsModal from '../components/AllBackupsModal';
import AuthDebug from '../components/AuthDebug';
import SystemLogs from '../components/SystemLogs';
// import TestAlerts from '../components/TestAlerts'; // تم حذف المكون
import GoogleDriveBackups from '../components/GoogleDriveBackups';
import LocalBackups from '../components/LocalBackups';
import ConnectedDevices from '../components/ConnectedDevices';
import DeviceSecurityManager from '../components/DeviceSecurityManager';

const Reports: React.FC = () => {
  const { type } = useParams<{ type?: string }>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  // Hook للتنسيق الموحد للتاريخ والوقت
  const { getCurrentFormattedDateTime } = useCurrentFormattedDateTime();
  // Hook للتنسيق الموحد للعملة
  const { formatCurrency } = useCurrencySettings();
  // Hook للحصول على إعدادات التاريخ والوقت
  const { settings: dateTimeSettings } = useDateTimeSettings();
  // State لتخزين تسميات الوقت المنسقة
  const [formattedTimeLabels, setFormattedTimeLabels] = useState<{[key: string]: string}>({});

  // قراءة معاملات URL
  const tabFromUrl = searchParams.get('tab');
  const subtabFromUrl = searchParams.get('subtab');



  // استخدام متجر البيانات
  const {
    salesTrends,
    previousPeriodSales,
    previousPeriodTotal,
    productCategories,
    inactiveProducts,
    inventoryStatus,
    systemStats,
    debtSummary,
    debtAging,
    topDebtors,
    debtTrends,
    collectionEfficiency,
    isLoading,
    selectedPeriod,
    selectedReportType,
    fetchSalesTrends,
    fetchPreviousPeriodTotal,
    fetchProductCategories,
    fetchInventoryStatus,
    fetchSystemStats,
    fetchDashboardStats,
    fetchDebtSummary,
    fetchDebtAging,
    fetchTopDebtors,
    fetchDebtTrends,
    fetchCollectionEfficiency,
    setPeriod
  } = useReportsStore();

  // استخدام متجر المصادقة في المستوى الأعلى
  const { user: currentUser } = useAuthStore();

  // مرجع لحاوية أزرار الفلترة الزمنية
  const periodButtonsRef = useRef<HTMLDivElement>(null);




  // متغيرات النوافذ
  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean;
    backupName: string;
    isLoading: boolean;
  }>({
    isOpen: false,
    backupName: '',
    isLoading: false
  });

  const [restoreModal, setRestoreModal] = useState<{
    isOpen: boolean;
    backupInfo: any;
    isLoading: boolean;
  }>({
    isOpen: false,
    backupInfo: null,
    isLoading: false
  });

  const [successModal, setSuccessModal] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    details: any;
    autoClose: boolean;
  }>({
    isOpen: false,
    title: '',
    message: '',
    details: null,
    autoClose: false
  });

  const [confirmModal, setConfirmModal] = useState<{
    isOpen: boolean;
    type: 'backup' | 'update' | 'clear-cache';
    title: string;
    message: string;
    description?: string;
    isLoading: boolean;
  }>({
    isOpen: false,
    type: 'backup',
    title: '',
    message: '',
    description: '',
    isLoading: false
  });

  const [allBackupsModal, setAllBackupsModal] = useState(false);

  // state للتبويبات الفرعية للنظام
  const [systemSubTab, setSystemSubTab] = useState<'system-actions' | 'system-logs' | 'test-alerts' | 'connected-devices' | 'device-security'>('system-actions');

  // state للتبويبات الفرعية للنسخ الاحتياطية
  const [backupSubTab, setBackupSubTab] = useState<'local' | 'google-drive'>('local');

  // مكون SystemLogs مع منع إعادة الإنشاء
  const systemLogsComponent = useMemo(() => {
    return <SystemLogs />;
  }, []); // بدون dependencies لمنع إعادة الإنشاء



  // فتح نافذة تأكيد الحذف
  const openDeleteModal = (backupName: string) => {
    setDeleteModal({
      isOpen: true,
      backupName,
      isLoading: false
    });
  };

  // تأكيد حذف النسخة الاحتياطية
  const confirmDeleteBackup = async () => {
    setDeleteModal(prev => ({ ...prev, isLoading: true }));

    try {
      const response = await apiClient.delete(`/api/dashboard/backups/${deleteModal.backupName}`);
      const data = response.data;

      if (data.success) {
        // إغلاق نافذة الحذف
        setDeleteModal({ isOpen: false, backupName: '', isLoading: false });

        // عرض نافذة النجاح
        setSuccessModal({
          isOpen: true,
          title: 'تم الحذف بنجاح',
          message: 'تم حذف النسخة الاحتياطية بنجاح',
          details: { backup_name: deleteModal.backupName },
          autoClose: true
        });

        // تحديث الإحصائيات
        fetchSystemStats();
      } else {
        throw new Error(data.message || 'خطأ غير معروف');
      }
    } catch (error) {
      console.error('Error deleting backup:', error);
      setDeleteModal(prev => ({ ...prev, isLoading: false }));

      // عرض رسالة خطأ
      setSuccessModal({
        isOpen: true,
        title: 'فشل في الحذف',
        message: `فشل في حذف النسخة الاحتياطية: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`,
        details: null,
        autoClose: false
      });
    }
  };

  // فتح نافذة تأكيد الاستعادة
  const openRestoreModal = (backupName: string) => {
    setRestoreModal({
      isOpen: true,
      backupInfo: { name: backupName },
      isLoading: false
    });
  };

  // تأكيد استعادة النسخة الاحتياطية
  const confirmRestoreBackup = async () => {
    if (!restoreModal.backupInfo) return;

    setRestoreModal(prev => ({ ...prev, isLoading: true }));

    try {
      const response = await apiClient.post(`/api/dashboard/restore-backup/${restoreModal.backupInfo.name}`);
      const data = response.data;

      if (data.success) {
        // إغلاق نافذة الاستعادة
        setRestoreModal({ isOpen: false, backupInfo: null, isLoading: false });

        // عرض نافذة النجاح مع إعادة تحميل تلقائية
        setSuccessModal({
          isOpen: true,
          title: 'تم الاستعادة بنجاح',
          message: 'تم استعادة النسخة الاحتياطية بنجاح. سيتم إعادة تحميل النظام خلال 3 ثوانٍ لتطبيق التغييرات.',
          details: {
            backup_name: data.backup_name,
            size: data.size,
            restored_at: data.restored_at,
            backup_before_restore: data.backup_before_restore
          },
          autoClose: true
        });

        // إعادة تحميل الصفحة بعد 3 ثوانٍ
        setTimeout(() => {
          window.location.reload();
        }, 3000);
      } else {
        throw new Error(data.message || 'خطأ غير معروف');
      }
    } catch (error) {
      console.error('Error restoring backup:', error);
      setRestoreModal(prev => ({ ...prev, isLoading: false }));

      // عرض رسالة خطأ
      setSuccessModal({
        isOpen: true,
        title: 'فشل في الاستعادة',
        message: `فشل في استعادة النسخة الاحتياطية: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`,
        details: null,
        autoClose: false
      });
    }
  };

  // متغيرات مبيعات المستخدمين اليومية
  const [dailyUserSales, setDailyUserSales] = useState<any>(null);
  const [dailyUserSalesLoading, setDailyUserSalesLoading] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date());

  // متغيرات الأرباح
  const [profitsData, setProfitsData] = useState<number>(0);
  const [profitsLoading, setProfitsLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState(() => {
    // استخدام خدمة التاريخ للحصول على التاريخ الحالي بتوقيت طرابلس
    const tripoliDate = getCurrentTripoliDateTime();
    const year = tripoliDate.getFullYear();
    const month = (tripoliDate.getMonth() + 1).toString().padStart(2, '0');
    const day = tripoliDate.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  });

  // جلب بيانات الأرباح للفترة المحددة
  const fetchProfitsData = async (period: ReportPeriod = selectedPeriod) => {
    try {
      setProfitsLoading(true);
      console.log(`جلب بيانات الأرباح للفترة: ${period}`);

      // استدعاء endpoint الأرباح الجديد للحصول على أرباح الفترة المحددة
      const response = await apiClient.get(`/api/dashboard/profits/${period}`);
      const data = response.data;

      const profits = data.profits || 0;
      setProfitsData(profits);

      console.log(`أرباح الفترة ${period}:`, {
        profits,
        period,
        userRole: data.user_role,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('خطأ في جلب بيانات الأرباح:', error);
      setProfitsData(0);
    } finally {
      setProfitsLoading(false);
    }
  };

  // جلب بيانات مبيعات المستخدمين اليومية
  const fetchDailyUserSales = async (date?: string, forceRefresh = false) => {
    try {
      const targetDate = date || selectedDate;

      // منع الاستدعاءات المتكررة للتاريخ نفسه
      if (dailyUserSalesLoading) {
        return;
      }

      // التحقق من وجود البيانات للتاريخ نفسه (إلا في حالة التحديث القسري)
      if (!forceRefresh && dailyUserSales && dailyUserSales.date === targetDate) {
        return;
      }

      setDailyUserSalesLoading(true);

      if (!currentUser || currentUser.role !== 'admin') {
        setDailyUserSales(null);
        setDailyUserSalesLoading(false);
        return;
      }

      const response = await apiClient.get(`/api/dashboard/daily-user-sales?date=${targetDate}`);
      const data = response.data;

      setDailyUserSales(data);
      setLastUpdateTime(new Date());
    } catch (error) {
      console.error('Error fetching daily user sales:', error);
      setDailyUserSales(null);
    } finally {
      setDailyUserSalesLoading(false);
    }
  };

  // Track if initial load has been done (exactly like Products.tsx)
  const initialLoadDone = useRef(false);

  // Handle URL parameters (like Products.tsx handles location.state)
  useEffect(() => {
    if (type && (type === 'sales' || type === 'products' || type === 'inventory' || type === 'customers' || type === 'system' || type === 'daily-users' || type === 'debts')) {
      // التحقق من الصلاحيات للوصول إلى التقارير المقيدة للمديرين
      const adminOnlyReports = ['system', 'debts', 'daily-users'];
      if (adminOnlyReports.includes(type) && (!currentUser || currentUser.role !== 'admin')) {
        navigate('/reports/sales', { replace: true });
        return;
      }

      // Set report type without triggering auto-fetch
      useReportsStore.setState({ selectedReportType: type as ReportType });
    }

    if (tabFromUrl === 'system') {
      if (!currentUser || currentUser.role !== 'admin') {
        navigate('/reports/sales', { replace: true });
        return;
      }

      useReportsStore.setState({ selectedReportType: 'system' });
      if (subtabFromUrl === 'system-logs') {
        setSystemSubTab('system-logs');
      } else if (subtabFromUrl === 'test-alerts') {
        setSystemSubTab('test-alerts');
      } else if (subtabFromUrl === 'connected-devices') {
        setSystemSubTab('connected-devices');
      } else if (subtabFromUrl === 'device-security') {
        setSystemSubTab('device-security');
      } else {
        setSystemSubTab('system-actions');
      }
    }
  }, [type, tabFromUrl, subtabFromUrl, currentUser, navigate]);

  // جلب البيانات عند تحميل الصفحة (exactly like Products.tsx)
  useEffect(() => {
    // Prevent duplicate initial loads
    if (initialLoadDone.current) {
      return;
    }

    console.log('🚀 [REPORTS] Component mounted, initializing data...');

    const fetchInitialData = async () => {
      try {
        // استخدام نفس النهج المحكم للتحميل الأولي (مثل Products.tsx)
        console.log(`🔄 [REPORTS] Initial load for report type: ${selectedReportType}`);
        await fetchDataForReportType(selectedReportType);
        console.log('✅ [REPORTS] Initial load completed');
      } catch (error) {
        console.error('❌ [REPORTS] Error during initialization:', error);
      }
    };

    fetchInitialData();

    // Mark initial load as done
    initialLoadDone.current = true;

    return () => {};
  }, []); // Empty dependency array - runs only once

  // Update local state when filters change (exactly like Products.tsx)
  useEffect(() => {
    console.log('📊 [REPORTS] Report type in store:', selectedReportType);
    // Only update local state, don't fetch data here
  }, [selectedReportType]);

  // تحضير تسميات الوقت المنسقة حسب الإعدادات (مثل Dashboard.tsx)
  useEffect(() => {
    const formatTimeLabels = async () => {
      if (selectedPeriod === 'day') {
        try {
          const settings = dateTimeSettings;
          if (!settings) return;

          const timeLabels: {[key: string]: string} = {};

          // تنسيق جميع الساعات من 0 إلى 23 حسب تنسيق الوقت المحدد فقط
          for (let hour = 0; hour <= 23; hour++) {
            const hourStr = `${hour.toString().padStart(2, '0')}:00`;
            try {
              // تطبيق تنسيق الوقت حسب الإعدادات فقط (بدون تحويل منطقة زمنية)
              let formattedTime = '';
              switch (settings.timeFormat) {
                case '24h':
                  formattedTime = `${hour.toString().padStart(2, '0')}${settings.timeSeparator}00`;
                  break;
                case '12h':
                  const hour12 = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
                  const ampm = hour >= 12 ? 'PM' : 'AM';
                  formattedTime = `${hour12}${settings.timeSeparator}00 ${ampm}`;
                  break;
                case '12h_ar':
                  const hour12Ar = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
                  const ampmAr = hour >= 12 ? 'م' : 'ص';
                  formattedTime = `${hour12Ar}${settings.timeSeparator}00 ${ampmAr}`;
                  break;
                default:
                  formattedTime = `${hour.toString().padStart(2, '0')}${settings.timeSeparator}00`;
              }

              timeLabels[hourStr] = formattedTime;
            } catch (error) {
              console.error(`Error formatting time for hour ${hour}:`, error);
              timeLabels[hourStr] = hourStr; // fallback إلى التنسيق الأصلي
            }
          }

          setFormattedTimeLabels(timeLabels);
        } catch (error) {
          console.error('Error getting datetime settings:', error);
          // fallback إلى تنسيق افتراضي
          const fallbackLabels: {[key: string]: string} = {};
          for (let hour = 0; hour <= 23; hour++) {
            const hourStr = `${hour.toString().padStart(2, '0')}:00`;
            fallbackLabels[hourStr] = hourStr;
          }
          setFormattedTimeLabels(fallbackLabels);
        }
      } else {
        setFormattedTimeLabels({});
      }
    };

    formatTimeLabels();
  }, [selectedPeriod, dateTimeSettings]);

  // دالة مساعدة لتنسيق التواريخ المختصرة في xaxis
  const formatChartDateShort = (dateStr: string, periodType: string): string => {
    if (!dateStr) return dateStr;

    try {
      switch (periodType) {
        case 'day':
          // للفترة اليومية، عرض اليوم والشهر فقط
          if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
            const [, month, day] = dateStr.split('-');
            return `${parseInt(day)}/${parseInt(month)}`;
          }
          return dateStr;

        case 'week':
          // للفترة الأسبوعية، استخدام التنسيق المختصر
          if (/^\d{4}-W\d{2}$/.test(dateStr)) {
            const [year, weekPart] = dateStr.split('-W');
            const weekNum = parseInt(weekPart);
            return `أ${weekNum}`;
          }
          return dateStr;

        case 'month':
          // للفترة الشهرية، عرض اختصار الشهر فقط
          if (/^\d{4}-\d{2}$/.test(dateStr)) {
            const [, month] = dateStr.split('-');
            const monthNames = ['ي', 'ف', 'م', 'أ', 'م', 'ي', 'ي', 'أ', 'س', 'أ', 'ن', 'د'];
            const monthIndex = parseInt(month) - 1;
            return monthNames[monthIndex];
          }
          return dateStr;

        case 'year':
          return dateStr;

        default:
          return dateStr;
      }
    } catch (error) {
      console.error('Error formatting short chart date:', error);
      return dateStr;
    }
  };

  // دالة مساعدة لتنسيق التواريخ الكاملة في tooltip
  const formatChartDateFull = (dateStr: string, periodType: string): string => {
    if (!dateStr || !dateTimeSettings) return dateStr;

    try {
      switch (periodType) {
        case 'day':
          // للفترة اليومية، تنسيق التاريخ الكامل حسب الإعدادات
          const date = new Date(dateStr);
          return formatDateWithSettings(date, dateTimeSettings);

        case 'week':
          // للفترة الأسبوعية، عرض الأسبوع والسنة
          if (/^\d{4}-W\d{2}$/.test(dateStr)) {
            const [year, weekPart] = dateStr.split('-W');
            const weekNum = parseInt(weekPart);
            return `الأسبوع ${weekNum} من ${year}`;
          }
          return dateStr;

        case 'month':
          // للفترة الشهرية، عرض الشهر والسنة كاملين
          if (/^\d{4}-\d{2}$/.test(dateStr)) {
            const [year, month] = dateStr.split('-');
            const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
            const monthIndex = parseInt(month) - 1;
            return `${monthNames[monthIndex]} ${year}`;
          }
          return dateStr;

        case 'year':
          return `سنة ${dateStr}`;

        default:
          return dateStr;
      }
    } catch (error) {
      console.error('Error formatting full chart date:', error);
      return dateStr;
    }
  };



  // تغيير نوع التقرير والانتقال إلى المسار المناسب (manual handling like Products.tsx)
  const handleReportTypeChange = async (newType: ReportType) => {
    // التحقق من الصلاحيات للوصول إلى التقارير المقيدة للمديرين
    const adminOnlyReports = ['system', 'debts', 'daily-users'];
    if (adminOnlyReports.includes(newType) && (!currentUser || currentUser.role !== 'admin')) {
      // عرض رسالة خطأ أو منع الوصول
      console.warn(`Access denied: ${newType} reports are only available for admin users`);
      return;
    }

    console.log('📄 [REPORTS] Report type changed to:', newType);
    // Set report type without triggering auto-fetch (like Products.tsx)
    useReportsStore.setState({ selectedReportType: newType });
    navigate(`/reports/${newType}`);

    // Fetch data manually for the new report type
    await fetchDataForReportType(newType);
  };

  // Manual data fetching function with controlled loading state (like Products.tsx)
  const fetchDataForReportType = async (reportType: ReportType) => {
    try {
      console.log(`🔄 [REPORTS] Fetching data for report type: ${reportType}`);

      // Set loading state once at the beginning and disable individual loading states
      useReportsStore.setState({ isLoading: true, error: null });
      useReportsStore.getState().setLoadingStateControl(true);

      if (reportType === 'sales') {
        // Fetch sales data sequentially without individual loading indicators
        console.log('📊 Fetching sales trends...');
        await fetchSalesTrends(selectedPeriod);

        console.log('📊 Fetching previous period total...');
        await fetchPreviousPeriodTotal(selectedPeriod);

        console.log('📊 Fetching dashboard stats...');
        await fetchDashboardStats();

        console.log('📊 Fetching profits data...');
        await fetchProfitsData(selectedPeriod);

      } else if (reportType === 'products') {
        await fetchProductCategories();
      } else if (reportType === 'inventory') {
        await fetchInventoryStatus();
      } else if (reportType === 'system') {
        await fetchSystemStats();
      } else if (reportType === 'daily-users') {
        if (currentUser && currentUser.role === 'admin') {
          await fetchDailyUserSales(selectedDate);
        }
      } else if (reportType === 'debts') {
        // Fetch debt data sequentially without individual loading indicators
        console.log('💰 Fetching debt summary...');
        await fetchDebtSummary();

        console.log('💰 Fetching debt aging...');
        await fetchDebtAging();

        console.log('💰 Fetching top debtors...');
        await fetchTopDebtors();

        console.log('💰 Fetching debt trends...');
        await fetchDebtTrends(selectedPeriod);

        console.log('💰 Fetching collection efficiency...');
        await fetchCollectionEfficiency(selectedPeriod);
      }

      // Re-enable individual loading states and set loading to false
      useReportsStore.getState().setLoadingStateControl(false);
      useReportsStore.setState({ isLoading: false });
      console.log(`✅ [REPORTS] Data fetched successfully for: ${reportType}`);
    } catch (error) {
      console.error(`❌ [REPORTS] Error fetching data for ${reportType}:`, error);
      useReportsStore.getState().setLoadingStateControl(false);
      useReportsStore.setState({ isLoading: false, error: `فشل في جلب بيانات ${reportType}` });
    }
  };

  // تغيير الفترة الزمنية (manual handling like Products.tsx)
  const handlePeriodChange = async (period: ReportPeriod) => {
    console.log('📅 [REPORTS] Period changed to:', period);

    // تحديث الفترة المحددة فقط بدون إعادة تحميل الصفحة
    setPeriod(period);

    // تحديث البيانات بناءً على الفترة الجديدة بشكل متتالي
    try {
      if (selectedReportType === 'debts') {
        // تحديث بيانات اتجاهات المديونية
        await fetchDebtTrends(period);
        // تحديث بيانات كفاءة التحصيل
        await fetchCollectionEfficiency(period);
      } else if (selectedReportType === 'sales') {
        // تحديث بيانات المبيعات
        await fetchSalesTrends(period);
        // تحديث إجمالي الفترة السابقة لحساب معدل النمو الصحيح
        await fetchPreviousPeriodTotal(period);
        // تحديث بيانات الأرباح
        await fetchProfitsData(period);
      }

      console.log(`✅ [REPORTS] Period data updated successfully for: ${period}`);
    } catch (error) {
      console.error(`❌ [REPORTS] Error updating period data:`, error);
    }

    // التمرير التلقائي إلى أزرار الفلترة بعد تحديث البيانات
    setTimeout(() => {
      if (periodButtonsRef.current) {
        periodButtonsRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }
    }, 100); // تأخير قصير للسماح بتحديث البيانات
  };



  // إعداد مخطط المبيعات المتقدم باستخدام ApexCharts
  const renderSalesChart = () => {
    // تحويل البيانات إلى التنسيق المطلوب
    const chartData = salesTrends.map(item => ({
      date: item.date,
      amount: item.amount
    }));

    // حساب إحصائيات المبيعات المتقدمة
    const totalSales = chartData.reduce((sum, item) => sum + (typeof item.amount === 'number' ? item.amount : 0), 0);

    // فلترة القيم الصحيحة فقط (أكبر من 0) لحساب الحد الأدنى والأقصى
    const validAmounts = chartData
      .map(item => typeof item.amount === 'number' ? item.amount : 0)
      .filter(amount => amount > 0);

    const maxSale = validAmounts.length > 0 ? Math.max(...validAmounts) : 0;
    const minSale = validAmounts.length > 0 ? Math.min(...validAmounts) : 0;
    const avgSale = validAmounts.length > 0 ? validAmounts.reduce((sum, amount) => sum + amount, 0) / validAmounts.length : 0;

    // حساب مؤشر الأداء المحسن
    const calculatePerformanceScore = () => {
      if (validAmounts.length === 0) return 0;

      // عوامل الأداء المختلفة
      const consistencyScore = validAmounts.length / chartData.length; // نسبة الأيام التي بها مبيعات

      // نسبة الاستقرار: كلما قل الانحراف المعياري، كان الأداء أفضل
      const variance = validAmounts.reduce((sum, amount) => {
        const diff = amount - avgSale;
        return sum + (diff * diff);
      }, 0) / validAmounts.length;
      const stdDev = Math.sqrt(variance);
      const stabilityScore = avgSale > 0 ? Math.max(0, 1 - (stdDev / avgSale)) : 0;

      // نسبة القوة: متوسط المبيعات مقارنة بأعلى قيمة
      const strengthScore = maxSale > 0 ? avgSale / maxSale : 0;

      // حساب النتيجة النهائية (متوسط مرجح)
      const finalScore = (consistencyScore * 0.5 + stabilityScore * 0.3 + strengthScore * 0.2) * 100;
      return Math.min(Math.max(finalScore, 0), 100);
    };

    const performanceScore = calculatePerformanceScore();

    // تسجيل البيانات للتشخيص
    console.log('Sales Statistics:', {
      chartDataLength: chartData.length,
      validAmountsLength: validAmounts.length,
      totalSales,
      maxSale,
      minSale,
      avgSale,
      performanceScore: performanceScore.toFixed(1),
      validAmounts: validAmounts.slice(0, 5) // أول 5 قيم للمراجعة
    });

    // حساب الانحراف المعياري للقيم الصحيحة فقط
    const variance = validAmounts.length > 0 ? validAmounts.reduce((sum, amount) => {
      const diff = amount - avgSale;
      return sum + (diff * diff);
    }, 0) / validAmounts.length : 0;
    const standardDeviation = Math.sqrt(variance);

    // حساب معدل النمو بناءً على مقارنة الفترة الحالية مع فترة سابقة محسوبة من البيانات الفعلية
    const calculateGrowthRate = () => {
      if (chartData.length === 0) return { rate: 0, currentTotal: 0, previousTotal: 0, hasData: false };

      // حساب إجمالي مبيعات الفترة الحالية
      const currentPeriodTotal = chartData.reduce((sum, item) =>
        sum + (typeof item.amount === 'number' ? item.amount : 0), 0);

      // استخدام البيانات الفعلية للفترة السابقة من قاعدة البيانات
      // هذه البيانات يتم جلبها من API endpoint جديد يحسب الفترة السابقة الفعلية
      const actualPreviousPeriodTotal = previousPeriodTotal; // البيانات الفعلية من قاعدة البيانات

      // تسجيل البيانات للتشخيص
      console.log('Growth Rate Calculation:', {
        chartDataLength: chartData.length,
        currentPeriodTotal,
        actualPreviousPeriodTotal,
        selectedPeriod,
        previousPeriodTotalFromStore: previousPeriodTotal,
        chartDataSample: chartData.slice(0, 3),
        timestamp: new Date().toISOString()
      });

      // إذا كانت مبيعات الفترة السابقة صفر، نعتبر النمو 100% إذا كان هناك مبيعات حالية
      if (actualPreviousPeriodTotal === 0) {
        const rate = currentPeriodTotal > 0 ? 100 : 0;
        return { rate, currentTotal: currentPeriodTotal, previousTotal: 0, hasData: true };
      }

      // حساب معدل النمو: ((الفترة الحالية - الفترة السابقة) / الفترة السابقة) × 100
      const rate = ((currentPeriodTotal - actualPreviousPeriodTotal) / actualPreviousPeriodTotal) * 100;

      return { rate, currentTotal: currentPeriodTotal, previousTotal: actualPreviousPeriodTotal, hasData: true };
    };

    const growthData = calculateGrowthRate();
    const growthRate = growthData.rate;

    // تحديد نوع المخطط بناءً على الفترة لعرض أفضل
    const chartType = selectedPeriod === 'day' ? 'line' :
                     selectedPeriod === 'week' ? 'area' :
                     selectedPeriod === 'month' ? 'bar' : 'line';

    // استخدام بيانات الفترة السابقة الحقيقية لخط الاتجاه
    const previousPeriodData = previousPeriodSales.map(item => {
      const amount = typeof item.amount === 'number' ? item.amount : 0;
      return Math.round(amount * 100) / 100;
    });

    // إعداد خيارات المخطط المتقدم
    const options: ApexOptions = {
      chart: {
        type: chartType as any,
        fontFamily: 'almarai, sans-serif',
        toolbar: {
          show: true,
          tools: {
            download: true,
            selection: false,
            zoom: true,
            zoomin: true,
            zoomout: true,
            pan: false,
            reset: true
          },
          export: {
            csv: {
              filename: 'تقرير_المبيعات',
              columnDelimiter: ',',
              headerCategory: 'التاريخ',
              headerValue: 'القيمة'
            },
            svg: {
              filename: 'مخطط_المبيعات'
            },
            png: {
              filename: 'مخطط_المبيعات'
            }
          }
        },
        zoom: {
          enabled: true,
          type: 'x',
          autoScaleYaxis: true
        },
        animations: {
          enabled: true,
          speed: 1000,
          animateGradually: {
            enabled: true,
            delay: 200
          },
          dynamicAnimation: {
            enabled: true,
            speed: 400
          }
        },
        background: 'transparent',
        locales: [{
          name: 'ar',
          options: {
            months: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            shortMonths: ['ي', 'ف', 'م', 'أ', 'م', 'ي', 'ي', 'أ', 'س', 'أ', 'ن', 'د'],
            days: ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
            shortDays: ['أحد', 'إثن', 'ثلا', 'أرب', 'خمي', 'جمع', 'سبت'],
            toolbar: {
              exportToSVG: "تحميل SVG",
              exportToPNG: "تحميل PNG",
              exportToCSV: "تحميل CSV",
              selection: "التحديد",
              selectionZoom: "تكبير التحديد",
              zoomIn: "تكبير",
              zoomOut: "تصغير",
              pan: "تحريك",
              reset: "إعادة تعيين التكبير"
            }
          }
        }],
        defaultLocale: 'ar'
      },
      colors: chartType === 'bar' ?
        (isDark ? ['#10B981', 'rgba(248, 113, 113, 0.7)'] : ['#059669', 'rgba(220, 38, 38, 0.7)']) :
        (isDark ? ['#3B82F6', 'rgba(248, 113, 113, 0.7)'] : ['#1D4ED8', 'rgba(220, 38, 38, 0.7)']),
      fill: {
        type: chartType === 'bar' ? 'solid' : 'gradient',
        gradient: {
          shade: isDark ? 'dark' : 'light',
          shadeIntensity: 0.8,
          opacityFrom: chartType === 'area' ? 0.7 : 0.9,
          opacityTo: chartType === 'area' ? 0.1 : 0.2,
          stops: [0, 90, 100],
          colorStops: [
            [
              {
                offset: 0,
                color: isDark ? '#3B82F6' : '#1D4ED8',
                opacity: chartType === 'area' ? 0.7 : 0.9
              },
              {
                offset: 100,
                color: isDark ? '#1E40AF' : '#1E3A8A',
                opacity: chartType === 'area' ? 0.1 : 0.2
              }
            ],
            [
              {
                offset: 0,
                color: isDark ? '#F87171' : '#DC2626',
                opacity: chartType === 'area' ? 0.5 : 0.7
              },
              {
                offset: 100,
                color: isDark ? '#DC2626' : '#B91C1C',
                opacity: chartType === 'area' ? 0.1 : 0.2
              }
            ]
          ]
        }
      },
      dataLabels: {
        enabled: chartType === 'bar',
        style: {
          fontSize: '12px',
          fontWeight: 'bold',
          colors: [isDark ? '#F9FAFB' : '#fff']
        },
        formatter: (value: number) => {
          return value >= 1000 ? `${(value / 1000).toFixed(1)}K` : value.toFixed(0);
        }
      },
      stroke: {
        curve: chartType === 'line' ? 'smooth' : 'straight',
        width: chartType === 'bar' ? 0 : [4, 3], // خط أكثر سمكاً للمبيعات الفعلية، أقل للفترة السابقة
        dashArray: [0, 8] // خط متصل للمبيعات الفعلية، خط متقطع للفترة السابقة
      },
      grid: {
        borderColor: isDark ? 'rgba(156, 163, 175, 0.3)' : 'rgba(107, 114, 128, 0.1)',
        row: {
          colors: ['transparent', 'transparent'],
          opacity: isDark ? 0.3 : 0.5
        },
        padding: {
          top: 0,
          right: 0,
          bottom: 0,
          left: 10
        },
        xaxis: {
          lines: {
            show: true
          }
        },
        yaxis: {
          lines: {
            show: true
          }
        }
      },
      markers: {
        size: [6, 5], // نقاط أكبر للمبيعات الفعلية
        colors: [
          isDark ? '#1F2937' : '#FFFFFF', // خلفية بيضاء/رمادية للمبيعات الفعلية
          isDark ? '#1F2937' : '#FFFFFF'  // خلفية بيضاء/رمادية للفترة السابقة
        ],
        strokeColors: [
          isDark ? '#3B82F6' : '#1D4ED8', // حدود زرقاء للمبيعات الفعلية
          isDark ? '#F87171' : '#DC2626'  // حدود حمراء للفترة السابقة
        ],
        strokeWidth: [3, 3], // حدود متساوية وواضحة
        shape: ['circle', 'circle'], // أشكال دائرية للجميع
        hover: {
          size: 9, // حجم أكبر عند التمرير
          sizeOffset: 4
        }
      },
      xaxis: {
        categories: chartData.map(item => item.date),
        labels: {
          style: {
            colors: isDark ? '#9CA3AF' : '#6B7280',
            fontSize: window.innerWidth < 768 ? '10px' : '12px'
          },
          formatter: (value: string) => {
            if (!value) return '';

            try {
              if (selectedPeriod === 'day') {
                // للفترة اليومية، استخدام التنسيق الموحد للوقت من الإعدادات
                return formattedTimeLabels[value] || value;
              } else if (selectedPeriod === 'week' || selectedPeriod === 'month') {
                // للفترات الأسبوعية والشهرية، عرض اليوم فقط
                if (value.includes('-')) {
                  const parts = value.split('-');
                  if (parts.length === 3) {
                    return parts[2]; // إرجاع اليوم فقط
                  }
                }
                return value;
              } else {
                // للفترة السنوية، عرض اختصار الشهر
                if (value.includes('-')) {
                  const parts = value.split('-');
                  if (parts.length === 2) {
                    const monthIndex = parseInt(parts[1]);
                    if (!isNaN(monthIndex) && monthIndex >= 1 && monthIndex <= 12) {
                      const months = ['ي', 'ف', 'م', 'أ', 'م', 'ي', 'ي', 'أ', 'س', 'أ', 'ن', 'د'];
                      return months[monthIndex - 1];
                    }
                  }
                }
                return value;
              }
            } catch (error) {
              console.error('Error formatting x-axis label:', error, value);
              return value;
            }
          }
        },
        axisBorder: {
          show: true,
          color: isDark ? 'rgba(156, 163, 175, 0.4)' : 'rgba(107, 114, 128, 0.3)'
        },
        axisTicks: {
          show: true,
          color: isDark ? 'rgba(156, 163, 175, 0.4)' : 'rgba(107, 114, 128, 0.3)'
        },
        tickAmount: selectedPeriod === 'day' ? 12 : undefined
      },
      yaxis: {
        labels: {
          style: {
            colors: isDark ? '#9CA3AF' : '#6B7280',
            fontSize: window.innerWidth < 768 ? '10px' : '12px'
          },
          formatter: (value: number) => {
            return value.toFixed(0);
          }
        }
      },
      legend: {
        show: true,
        position: 'bottom',
        horizontalAlign: 'center',
        floating: false,
        fontSize: '14px',
        fontFamily: 'almarai, sans-serif',
        fontWeight: 600,
        labels: {
          colors: isDark ? '#E5E7EB' : '#374151',
          useSeriesColors: true // استخدام ألوان السلاسل
        },
        markers: {
          size: 10,
          strokeWidth: 3,
          fillColors: [
            isDark ? '#3B82F6' : '#1D4ED8', // لون المبيعات الفعلية
            isDark ? '#F87171' : '#DC2626'  // لون الفترة السابقة
          ],
          shape: 'circle',
          offsetX: 0,
          offsetY: 0
        },
        itemMargin: {
          horizontal: 20,
          vertical: 8
        },
        offsetY: 15
      },
      tooltip: {
        enabled: true,
        shared: true,
        intersect: false,
        theme: isDark ? 'dark' : 'light',
        fillSeriesColor: false,
        style: {
          fontSize: '13px',
          fontFamily: 'almarai, sans-serif'
        },
        custom: function({ series, dataPointIndex }) {
          try {
            if (dataPointIndex < 0 || dataPointIndex >= chartData.length) {
              return '';
            }

            // الحصول على البيانات لكلا السلسلتين
            const currentValue = series[0] ? series[0][dataPointIndex] : 0;
            const previousValue = series[1] ? series[1][dataPointIndex] : 0;
            const label = chartData[dataPointIndex].date;

            // تنسيق التاريخ حسب الفترة
            let formattedDate = '';
            if (selectedPeriod === 'day') {
              // استخدام التنسيق الموحد للوقت من الإعدادات
              const formattedTime = formattedTimeLabels[label] || label;
              formattedDate = `الساعة ${formattedTime}`;
            } else if (selectedPeriod === 'week' || selectedPeriod === 'month') {
              if (label && label.includes('-')) {
                const parts = label.split('-');
                if (parts.length === 3) {
                  const year = parts[0];
                  const month = parseInt(parts[1]);
                  const day = parseInt(parts[2]);
                  if (!isNaN(month) && !isNaN(day) && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                    const arabicMonths = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                    formattedDate = `${day} ${arabicMonths[month - 1]} ${year}`;
                  }
                }
              } else {
                formattedDate = label || '';
              }
            } else if (selectedPeriod === 'year') {
              if (label && label.includes('-')) {
                const parts = label.split('-');
                if (parts.length === 2) {
                  const year = parts[0];
                  const month = parseInt(parts[1]);
                  if (!isNaN(month) && month >= 1 && month <= 12) {
                    const arabicMonths = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                    formattedDate = `${arabicMonths[month - 1]} ${year}`;
                  }
                }
              } else {
                formattedDate = label || '';
              }
            }

            // أسماء السلاسل
            const currentPeriodName = selectedPeriod === 'day' ? 'اليوم' :
                                    selectedPeriod === 'week' ? 'هذا الأسبوع' :
                                    selectedPeriod === 'month' ? 'هذا الشهر' : 'هذه السنة';

            const previousPeriodName = selectedPeriod === 'day' ? 'أمس' :
                                     selectedPeriod === 'week' ? 'الأسبوع السابق' :
                                     selectedPeriod === 'month' ? 'الشهر السابق' : 'السنة السابقة';

            return `
              <div style="
                background: ${isDark ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.96)'};
                border: 1px solid ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(229, 231, 235, 0.4)'};
                border-radius: 8px;
                padding: 14px 18px;
                box-shadow: 0 8px 25px ${isDark ? 'rgba(0, 0, 0, 0.5)' : 'rgba(0, 0, 0, 0.08)'},
                           0 4px 10px ${isDark ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.12)'};
                backdrop-filter: blur(12px);
                font-family: 'almarai', sans-serif;
                direction: rtl;
                min-width: 240px;
                border: 2px solid ${isDark ? '#3B82F6' : '#1D4ED8'};
              ">
                <div style="
                  color: ${isDark ? '#F9FAFB' : '#111827'};
                  font-size: 15px;
                  font-weight: 700;
                  margin-bottom: 10px;
                  text-align: center;
                  padding: 6px;
                  background: ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(243, 244, 246, 0.5)'};
                  border-radius: 6px;
                ">
                  ${formattedDate}
                </div>

                <div style="
                  color: ${isDark ? '#D1D5DB' : '#4B5563'};
                  font-size: 12px;
                  margin-bottom: 6px;
                  display: flex;
                  justify-content: space-between;
                ">
                  <span>${currentPeriodName}:</span>
                  <span style="font-weight: 600; color: ${isDark ? '#60A5FA' : '#3B82F6'};">${formatCurrency(currentValue)}</span>
                </div>
                ${previousValue > 0 ? `
                  <div style="
                    color: ${isDark ? '#D1D5DB' : '#4B5563'};
                    font-size: 12px;
                    margin-bottom: 6px;
                    display: flex;
                    justify-content: space-between;
                  ">
                    <span>${previousPeriodName}:</span>
                    <span style="font-weight: 600; color: ${isDark ? '#9CA3AF' : '#6B7280'};">${formatCurrency(previousValue)}</span>
                  </div>
                  <div style="
                    color: ${isDark ? '#D1D5DB' : '#4B5563'};
                    font-size: 12px;
                    margin-bottom: 8px;
                    display: flex;
                    justify-content: space-between;
                  ">
                    <span>نسبة التغيير:</span>
                    <span style="font-weight: 600; color: ${((currentValue - previousValue) / previousValue * 100) >= 0 ? (isDark ? '#34D399' : '#10B981') : (isDark ? '#F87171' : '#DC2626')};">
                      ${((currentValue - previousValue) / previousValue * 100) >= 0 ? '↗️' : '↘️'} ${Math.abs(((currentValue - previousValue) / previousValue * 100)).toFixed(1)}%
                    </span>
                  </div>
                ` : ''}
                <div style="
                  text-align: center;
                  padding: 8px;
                  background: ${isDark ? 'rgba(59, 130, 246, 0.1)' : 'rgba(29, 78, 216, 0.1)'};
                  border-radius: 6px;
                  border: 1px solid ${isDark ? '#3B82F6' : '#1D4ED8'};
                ">
                  <div style="
                    color: ${isDark ? '#3B82F6' : '#1D4ED8'};
                    font-size: 14px;
                    font-weight: 800;
                  ">
                    ${formatCurrency(currentValue)}
                  </div>
                </div>
              </div>
            `;
          } catch (error) {
            console.error('Error formatting tooltip:', error);
            return '';
          }
        }
      },
      responsive: [
        {
          breakpoint: 768,
          options: {
            chart: {
              height: 280
            },
            markers: {
              size: [5, 4], // أحجام مناسبة للجوال
              strokeWidth: [2, 2] // حدود متساوية للجوال
            },
            xaxis: {
              labels: {
                style: {
                  fontSize: '10px',
                  colors: isDark ? '#9CA3AF' : '#6B7280'
                },
                rotate: 0,
                offsetY: 0
              }
            },
            yaxis: {
              labels: {
                style: {
                  fontSize: '10px',
                  colors: isDark ? '#9CA3AF' : '#6B7280'
                },
                formatter: (value: number) => {
                  // تبسيط الأرقام على الجوال
                  if (value >= 1000) {
                    return `${(value / 1000).toFixed(1)}K`;
                  }
                  return value.toFixed(0);
                }
              }
            }
          }
        }
      ]
    };

    // إعداد سلاسل البيانات المتعددة
    const series = [
      {
        name: `المبيعات ${selectedPeriod === 'day' ? 'اليوم' :
                        selectedPeriod === 'week' ? 'هذا الأسبوع' :
                        selectedPeriod === 'month' ? 'هذا الشهر' : 'هذه السنة'}`,
        type: chartType,
        data: chartData.map(item => {
          const amount = typeof item.amount === 'number' ? item.amount : 0;
          return Math.round(amount * 100) / 100;
        })
      },
      {
        name: `${selectedPeriod === 'day' ? 'أمس' :
                selectedPeriod === 'week' ? 'الأسبوع السابق' :
                selectedPeriod === 'month' ? 'الشهر السابق' : 'السنة السابقة'}`,
        type: 'line',
        data: previousPeriodData
      }
    ];

    return (
      <div className="space-y-6">
        {/* أزرار الفلترة الزمنية */}
        <div ref={periodButtonsRef} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 flex items-center">
              <FaCalendarAlt className="ml-2 text-primary-600 dark:text-primary-400" />
              فلترة الفترة الزمنية
            </h3>
            <div className="flex flex-wrap gap-2">
              {(['day', 'week', 'month', 'year'] as ReportPeriod[]).map((period) => (
                <button
                  key={period}
                  onClick={() => handlePeriodChange(period)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 ${
                    selectedPeriod === period
                      ? 'bg-primary-600 dark:bg-primary-500 text-white shadow-md transform scale-105'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 hover:shadow-sm'
                  }`}
                >
                  {period === 'day' && <FaClock className="text-xs" />}
                  {period === 'week' && <FaCalendarWeek className="text-xs" />}
                  {period === 'month' && <FaCalendarAlt className="text-xs" />}
                  {period === 'year' && <FaCalendarCheck className="text-xs" />}
                  {period === 'day' ? 'يومي' :
                   period === 'week' ? 'أسبوعي' :
                   period === 'month' ? 'شهري' : 'سنوي'}
                </button>
              ))}
            </div>
          </div>
        </div>

        <div className="touch-card">
          {/* Chart Container */}
          <div className={`h-96 md:h-80 ${isDark ? 'dark-chart' : 'light-chart'}`}>
            <ReactApexChart
              type={chartType as any}
              height="100%"
              width="100%"
              options={options}
              series={series}
            />
          </div>

        {/* Chart Summary - إحصائيات متقدمة */}
        <div className="mt-6 space-y-6">
          {/* الصف الأول - الإحصائيات الأساسية المهمة مع الأرقام المختصرة */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
            {/* أعلى قيمة */}
            <CompactStatCard
              title="أعلى قيمة"
              amount={maxSale}
              icon={<FaArrowUp className="text-green-600 dark:text-green-400" />}
              showCurrency={true}
              compactThreshold={100}
              unitType="english"
              changeText="أعلى مبيعات في الفترة"
              enableAnimation={true}
              animationDuration={2.5}
              animationDelay={0.2}
            />

            {/* متوسط المبيعات */}
            <CompactStatCard
              title="متوسط المبيعات"
              amount={Math.round(avgSale)}
              icon={<FaChartBar className="text-blue-600 dark:text-blue-400" />}
              showCurrency={true}
              compactThreshold={100}
              unitType="english"
              changeText="متوسط قيمة المبيعات"
              enableAnimation={true}
              animationDuration={2.5}
              animationDelay={0.4}
            />

            {/* إجمالي المبيعات */}
            <CompactStatCard
              title="إجمالي المبيعات"
              amount={totalSales}
              icon={<FaShoppingCart className="text-indigo-600 dark:text-indigo-400" />}
              showCurrency={true}
              compactThreshold={100}
              unitType="english"
              changeText="مجموع مبيعات الفترة"
              enableAnimation={true}
              animationDuration={2.5}
              animationDelay={0.6}
            />

            {/* أقل قيمة */}
            <CompactStatCard
              title="أقل قيمة"
              amount={minSale > 0 ? minSale : 0}
              icon={<FaArrowDown className="text-orange-600 dark:text-orange-400" />}
              showCurrency={true}
              compactThreshold={100}
              unitType="english"
              changeText={minSale > 0 ? 'أدنى مبيعات في الفترة' : 'لا توجد مبيعات في الفترة'}
              enableAnimation={true}
              animationDuration={2.5}
              animationDelay={0.8}
            />
          </div>

          {/* الصف الثاني - الإحصائيات المتقدمة الفريدة */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
            {/* بطاقة معدل النمو مع أرقام مختصرة */}
            <GrowthRateCard
              growthRate={growthRate}
              currentTotal={growthData.currentTotal}
              previousTotal={growthData.previousTotal}
              hasData={growthData.hasData}
              isLoading={isLoading}
              selectedPeriod={selectedPeriod}
            />

            {/* بطاقة الأرباح */}
            {profitsLoading ? (
              <div className="bg-white dark:bg-gray-800 p-5 lg:p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300">
                <div className="flex items-center justify-center">
                  <div className="w-8 h-8 border-2 border-green-600 border-t-transparent rounded-full animate-spin"></div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mr-2">جاري التحميل...</p>
                </div>
              </div>
            ) : (
              <CompactStatCard
                title={(() => {
                  const periodText = selectedPeriod === 'day' ? 'اليوم' :
                                   selectedPeriod === 'week' ? 'الأسبوع' :
                                   selectedPeriod === 'month' ? 'الشهر' :
                                   'السنة';
                  return currentUser?.role === 'admin'
                    ? `أرباح ${periodText} (جميع المستخدمين)`
                    : `أرباح ${periodText}`;
                })()}
                amount={profitsData}
                icon={<FaCoins className="text-green-600 dark:text-green-400" />}
                showCurrency={true}
                compactThreshold={1000}
                unitType="english"
                changeText={(() => {
                  const periodText = selectedPeriod === 'day' ? 'اليوم' :
                                   selectedPeriod === 'week' ? 'الأسبوع' :
                                   selectedPeriod === 'month' ? 'الشهر' :
                                   'السنة';
                  return currentUser?.role === 'admin'
                    ? `إجمالي أرباح جميع المستخدمين لـ${periodText}`
                    : `أرباحك لـ${periodText} الحالي`;
                })()}
                enableAnimation={true}
                animationDuration={2.5}
                animationDelay={1.2}
              />
            )}

            {/* الانحراف المعياري */}
            <CompactStatCard
              title="الانحراف المعياري"
              amount={Math.round(standardDeviation)}
              icon={<FaSync className="text-blue-600 dark:text-blue-400" />}
              showCurrency={false}
              compactThreshold={1000}
              unitType="english"
              changeText="مقياس تشتت البيانات"
              enableAnimation={true}
              animationDuration={2.5}
              animationDelay={1.4}
            />

            {/* عدد النقاط */}
            <CompactStatCard
              title="عدد النقاط"
              amount={chartData.length}
              icon={<FaCalendarAlt className="text-purple-600 dark:text-purple-400" />}
              showCurrency={false}
              compactThreshold={100}
              unitType="english"
              changeText="عدد نقاط البيانات"
              enableAnimation={true}
              animationDuration={2.5}
              animationDelay={1.6}
            />
          </div>

          {/* مؤشر الأداء - تصميم محسن */}
          <div className="touch-card stats-card">
            <div className="flex justify-between items-start mb-2">
              <div className="flex-1">
                <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">
                  مؤشر الأداء
                </p>

                {/* النسبة المئوية */}
                <div className="mb-2">
                  <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {performanceScore.toFixed(1)}%
                  </span>
                </div>

                <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                  {(() => {
                    if (performanceScore >= 70) return 'أداء ممتاز - استمرارية وثبات في المبيعات';
                    if (performanceScore >= 40) return 'أداء متوسط - يحتاج تحسين';
                    return 'أداء ضعيف - يتطلب مراجعة الاستراتيجية';
                  })()}
                </p>
              </div>

              {/* الأيقونة بنفس تصميم باقي البطاقات */}
              <div className="p-4 rounded-full bg-primary-50 dark:bg-primary-900/30 text-xl">
                <FaChartBar className="text-primary-600 dark:text-primary-400" />
              </div>
            </div>

            {/* الفاصل الخفيف */}
            <div className="stats-card-divider"></div>

            {/* شريط التقدم المحسن */}
            <div className="w-full h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden mb-4">
              <div
                className={`h-full rounded-full transition-all duration-1000 ${
                  performanceScore >= 70 ? 'bg-green-500' :
                  performanceScore >= 40 ? 'bg-yellow-500' : 'bg-red-500'
                }`}
                style={{ width: `${performanceScore}%` }}
              ></div>
            </div>

            {/* مؤشرات الحالة مع تباعد محسن */}
            <div className="flex justify-between items-center text-xs font-medium">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span className="text-red-600 dark:text-red-400">0-39%</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span className="text-yellow-600 dark:text-yellow-400">40-69%</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-green-600 dark:text-green-400">70-100%</span>
              </div>
            </div>

            {/* معلومات إضافية عن حساب الأداء */}
            <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
              <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                الاستمرارية: {((validAmounts.length / chartData.length) * 100).toFixed(0)}% •
                الاستقرار: {avgSale > 0 ? (Math.max(0, 1 - (Math.sqrt(validAmounts.reduce((sum, amount) => sum + Math.pow(amount - avgSale, 2), 0) / validAmounts.length) / avgSale)) * 100).toFixed(0) : 0}% •
                القوة: {maxSale > 0 ? ((avgSale / maxSale) * 100).toFixed(0) : 0}%
              </p>
            </div>
          </div>
          </div>
        </div>
      </div>
    );
  };

  // إعداد مخطط فئات المنتجات باستخدام ApexCharts
  const renderProductsChart = () => {
    // تحويل البيانات إلى التنسيق المطلوب
    const chartData = productCategories.map(item => item.value);
    const chartLabels = productCategories.map(item => item.name);

    // إعداد خيارات المخطط
    const options: ApexOptions = {
      chart: {
        type: 'donut',
        fontFamily: 'almarai, sans-serif',
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          speed: 500,
          animateGradually: {
            enabled: true,
            delay: 150
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350
          }
        },
        background: 'transparent'
      },
      theme: {
        mode: isDark ? 'dark' : 'light',
        palette: 'palette1'
      },
      colors: isDark
        ? ['#34D399', '#FBBF24', '#F87171', '#60A5FA', '#A78BFA', '#F472B6', '#38BDF8', '#4ADE80']
        : ['#10B981', '#D97706', '#DC2626', '#2563EB', '#7C3AED', '#DB2777', '#0284C7', '#16A34A'],
      labels: chartLabels,
      dataLabels: {
        enabled: true,
        formatter: (val: number) => {
          return `${val.toFixed(1)}%`;
        },
        style: {
          fontSize: '13px',
          fontFamily: 'almarai, sans-serif',
          fontWeight: '700',
          colors: ['#FFFFFF']
        },
        dropShadow: {
          enabled: true,
          top: 1,
          left: 1,
          blur: 2,
          color: '#000000',
          opacity: 0.6
        }
      },
      stroke: {
        show: true,
        curve: 'smooth',
        lineCap: 'round',
        width: 3,
        colors: [isDark ? '#1F2937' : '#FFFFFF']
      },
      fill: {
        type: 'solid'
      },
      plotOptions: {
        pie: {
          donut: {
            size: '75%',
            labels: {
              show: true,
              name: {
                show: true,
                fontSize: '14px',
                fontFamily: 'almarai, sans-serif',
                fontWeight: 600,
                color: isDark ? '#9CA3AF' : '#6B7280',
                offsetY: -25,
                formatter: function () {
                  return 'النسبة المئوية';
                }
              },
              value: {
                show: true,
                fontSize: '32px',
                fontFamily: 'almarai, sans-serif',
                fontWeight: 800,
                color: isDark ? '#60A5FA' : '#3B82F6',
                offsetY: 20,
                formatter: function (val: any) {
                  return `${val}%`;
                }
              },
              total: {
                show: true,
                showAlways: true,
                label: 'توزيع الفئات',
                fontSize: '28px',
                fontFamily: 'almarai, sans-serif',
                fontWeight: 800,
                color: isDark ? '#60A5FA' : '#2563EB',
                formatter: function () {
                  // عرض النسبة الكاملة مع نص توضيحي
                  return '100%';
                }
              }
            }
          },
          expandOnClick: true
        }
      },
      legend: {
        show: false
      },
      tooltip: {
        theme: isDark ? 'dark' : 'light',
        style: {
          fontSize: '13px',
          fontFamily: 'almarai, sans-serif'
        },
        custom: function({ series, seriesIndex, w }) {
          const categoryName = w.config.labels[seriesIndex];
          const percentage = series[seriesIndex];
          const categoryData = productCategories[seriesIndex];
          const count = categoryData?.count || 0;

          return `
            <div style="
              background: ${isDark ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.96)'};
              border: 1px solid ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(229, 231, 235, 0.4)'};
              border-radius: 8px;
              padding: 14px 18px;
              box-shadow: 0 8px 25px ${isDark ? 'rgba(0, 0, 0, 0.5)' : 'rgba(0, 0, 0, 0.08)'},
                         0 4px 10px ${isDark ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.12)'};
              backdrop-filter: blur(12px);
              font-family: 'almarai', sans-serif;
              direction: rtl;
              min-width: 200px;
              border: 2px solid ${(isDark
                ? ['#34D399', '#FBBF24', '#F87171', '#A78BFA']
                : ['#10B981', '#D97706', '#DC2626', '#7C3AED'])[seriesIndex % 4]};
            ">
              <div style="
                color: ${isDark ? '#F9FAFB' : '#111827'};
                font-size: 15px;
                font-weight: 700;
                margin-bottom: 10px;
                text-align: center;
                padding: 6px;
                background: ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(243, 244, 246, 0.5)'};
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
              ">
                <div style="
                  width: 10px;
                  height: 10px;
                  border-radius: 50%;
                  background: ${(isDark
                    ? ['#34D399', '#FBBF24', '#F87171', '#A78BFA']
                    : ['#10B981', '#D97706', '#DC2626', '#7C3AED'])[seriesIndex % 4]};
                  margin-left: 8px;
                "></div>
                ${categoryName}
              </div>
              <div style="
                color: ${isDark ? '#D1D5DB' : '#4B5563'};
                font-size: 12px;
                margin-bottom: 6px;
                display: flex;
                justify-content: space-between;
              ">
                <span>عدد المنتجات:</span>
                <span style="font-weight: 600;">${count} منتج</span>
              </div>
              <div style="
                text-align: center;
                padding: 8px;
                background: ${(isDark
                  ? ['rgba(52, 211, 153, 0.1)', 'rgba(251, 191, 36, 0.1)', 'rgba(248, 113, 113, 0.1)', 'rgba(167, 139, 250, 0.1)']
                  : ['rgba(16, 185, 129, 0.1)', 'rgba(217, 119, 6, 0.1)', 'rgba(220, 38, 38, 0.1)', 'rgba(124, 58, 237, 0.1)'])[seriesIndex % 4]};
                border-radius: 6px;
                border: 1px solid ${(isDark
                  ? ['#34D399', '#FBBF24', '#F87171', '#A78BFA']
                  : ['#10B981', '#D97706', '#DC2626', '#7C3AED'])[seriesIndex % 4]};
              ">
                <div style="
                  color: ${(isDark
                    ? ['#34D399', '#FBBF24', '#F87171', '#A78BFA']
                    : ['#10B981', '#D97706', '#DC2626', '#7C3AED'])[seriesIndex % 4]};
                  font-size: 16px;
                  font-weight: 800;
                ">
                  ${parseFloat(percentage).toFixed(1)}%
                </div>
              </div>
            </div>
          `;
        }
      },
      responsive: [
        {
          breakpoint: 768,
          options: {
            chart: {
              height: 300
            },
            legend: {
              position: 'bottom',
              fontSize: '12px'
            },
            dataLabels: {
              enabled: false
            }
          }
        }
      ]
    };

    // إعداد سلسلة البيانات
    const series = chartData;

    // حساب إحصائيات الفئات
    const totalCategories = productCategories.length;
    const totalProducts = productCategories.reduce((sum, item) => sum + (item.count || 0), 0);
    const largestCategory = productCategories.reduce((max, item) =>
      (item.count || 0) > (max.count || 0) ? item : max, productCategories[0] || { name: '', count: 0 });

    return (
      <div className="touch-card">
        {/* Chart Container */}
        <div className="h-80 md:h-96">
          <ReactApexChart
            type="donut"
            height="100%"
            width="100%"
            options={options}
            series={series}
          />
        </div>

        {/* Custom Legend */}
        <div className="mt-6 flex flex-wrap justify-center gap-4">
          {productCategories.map((category, index) => (
            <div key={index} className="flex items-center gap-3 px-4 py-3 bg-gray-50 dark:bg-gray-700/50 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200">
              <div
                className="w-4 h-4 rounded-full shadow-sm"
                style={{
                  backgroundColor: (isDark
                    ? ['#34D399', '#FBBF24', '#F87171', '#60A5FA', '#A78BFA', '#F472B6', '#38BDF8', '#4ADE80']
                    : ['#10B981', '#D97706', '#DC2626', '#2563EB', '#7C3AED', '#DB2777', '#0284C7', '#16A34A'])[index % 8]
                }}
              ></div>
              <div className="text-center">
                <span className="text-sm font-semibold text-gray-900 dark:text-gray-100 block">
                  {category.name}
                </span>
                <span className="text-xs text-gray-600 dark:text-gray-400">
                  {category.value.toFixed(1)}% • {category.count || 0} منتج
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Chart Summary */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* إجمالي الفئات */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">إجمالي الفئات</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{totalCategories}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center">
                <FaChartPie className="text-primary-600 dark:text-primary-400 text-xl" />
              </div>
            </div>
          </div>

          {/* إجمالي المنتجات */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">إجمالي المنتجات</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{totalProducts.toLocaleString()}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-success-100 dark:bg-success-900/30 flex items-center justify-center">
                <FaShoppingCart className="text-success-600 dark:text-success-400 text-xl" />
              </div>
            </div>
          </div>

          {/* أكبر فئة */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">أكبر فئة</p>
                <p className="text-lg font-bold text-secondary-900 dark:text-secondary-100">{largestCategory.name || 'لا توجد'}</p>
                <p className="text-xs text-secondary-500 dark:text-secondary-400">{largestCategory.count || 0} منتج</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-warning-100 dark:bg-warning-900/30 flex items-center justify-center">
                <FaArrowUp className="text-warning-600 dark:text-warning-400 text-xl" />
              </div>
            </div>
          </div>

          {/* منتجات غير نشطة */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">منتجات غير نشطة</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{inactiveProducts}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-danger-100 dark:bg-danger-900/30 flex items-center justify-center">
                <FaChartBar className="text-danger-600 dark:text-danger-400 text-xl" />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // إعداد مخطط حالة المخزون باستخدام ApexCharts
  const renderInventoryChart = () => {
    // تحويل البيانات إلى التنسيق المطلوب
    const chartData = inventoryStatus.map(item => ({
      x: item.name,
      y: item.quantity
    }));

    // إعداد خيارات المخطط
    const options: ApexOptions = {
      chart: {
        type: 'bar',
        fontFamily: 'almarai, sans-serif',
        toolbar: {
          show: false
        },
        zoom: {
          enabled: false
        },
        animations: {
          enabled: true,
          speed: 800
        },
        background: 'transparent'
      },
      theme: {
        mode: isDark ? 'dark' : 'light',
        palette: 'palette1'
      },
      colors: isDark ?
        ['#34D399', '#FBBF24', '#F87171'] :
        ['#10B981', '#D97706', '#DC2626'],
      dataLabels: {
        enabled: true,
        formatter: (val: number) => {
          return val > 0 ? val.toString() : '';
        },
        style: {
          fontSize: '13px',
          fontFamily: 'almarai, sans-serif',
          fontWeight: '700',
          colors: [isDark ? '#FFFFFF' : '#FFFFFF']
        },
        offsetY: -25,
        dropShadow: {
          enabled: true,
          top: 1,
          left: 1,
          blur: 2,
          color: isDark ? '#000000' : '#000000',
          opacity: 0.3
        }
      },
      grid: {
        borderColor: isDark ? '#374151' : '#E5E7EB',
        strokeDashArray: 3,
        xaxis: {
          lines: {
            show: false
          }
        }
      },
      xaxis: {
        type: 'category',
        labels: {
          style: {
            colors: isDark ? '#9CA3AF' : '#6B7280',
            fontSize: '12px'
          }
        },
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        labels: {
          style: {
            colors: isDark ? '#9CA3AF' : '#6B7280',
            fontSize: '12px'
          }
        }
      },
      tooltip: {
        theme: isDark ? 'dark' : 'light',
        style: {
          fontSize: '13px',
          fontFamily: 'almarai, sans-serif'
        },
        custom: function({ series, dataPointIndex }) {
          try {
            if (dataPointIndex < 0 || dataPointIndex >= inventoryStatus.length) {
              return '';
            }

            const statusData = inventoryStatus[dataPointIndex];
            const quantity = series[0][dataPointIndex];
            const statusName = statusData.name;
            const totalQuantity = statusData.total_quantity || 0;
            const value = statusData.value || 0;

            // تحديد اللون حسب الحالة
            let statusColor = '';
            if (statusName === 'متوفر') {
              statusColor = isDark ? '#34D399' : '#10B981';
            } else if (statusName === 'منخفض') {
              statusColor = isDark ? '#FBBF24' : '#D97706';
            } else if (statusName === 'نفذ') {
              statusColor = isDark ? '#F87171' : '#DC2626';
            }

            return `
              <div style="
                background: ${isDark ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.96)'};
                border: 1px solid ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(229, 231, 235, 0.4)'};
                border-radius: 8px;
                padding: 14px 18px;
                box-shadow: 0 8px 25px ${isDark ? 'rgba(0, 0, 0, 0.5)' : 'rgba(0, 0, 0, 0.08)'},
                           0 4px 10px ${isDark ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.12)'};
                backdrop-filter: blur(12px);
                font-family: 'almarai', sans-serif;
                direction: rtl;
                min-width: 200px;
                border: 2px solid ${statusColor};
              ">
                <div style="
                  color: ${isDark ? '#F9FAFB' : '#111827'};
                  font-size: 15px;
                  font-weight: 700;
                  margin-bottom: 10px;
                  text-align: center;
                  padding: 6px;
                  background: ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(243, 244, 246, 0.5)'};
                  border-radius: 6px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                ">
                  <div style="
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                    background: ${statusColor};
                    margin-left: 8px;
                  "></div>
                  ${statusName}
                </div>
                <div style="
                  color: ${isDark ? '#D1D5DB' : '#4B5563'};
                  font-size: 12px;
                  margin-bottom: 6px;
                  display: flex;
                  justify-content: space-between;
                ">
                  <span>عدد المنتجات:</span>
                  <span style="font-weight: 600;">${quantity} منتج</span>
                </div>
                <div style="
                  color: ${isDark ? '#D1D5DB' : '#4B5563'};
                  font-size: 12px;
                  margin-bottom: 6px;
                  display: flex;
                  justify-content: space-between;
                ">
                  <span>إجمالي الكمية:</span>
                  <span style="font-weight: 600;">${totalQuantity.toLocaleString()} وحدة</span>
                </div>
                <div style="
                  color: ${isDark ? '#D1D5DB' : '#4B5563'};
                  font-size: 12px;
                  margin-bottom: 8px;
                  display: flex;
                  justify-content: space-between;
                ">
                  <span>القيمة:</span>
                  <span style="font-weight: 600;">${formatCurrency(value)}</span>
                </div>
                <div style="
                  text-align: center;
                  padding: 8px;
                  background: ${statusColor}20;
                  border-radius: 6px;
                  border: 1px solid ${statusColor};
                ">
                  <div style="
                    color: ${statusColor};
                    font-size: 14px;
                    font-weight: 800;
                  ">
                    ${quantity} منتج
                  </div>
                </div>
              </div>
            `;
          } catch (error) {
            return '';
          }
        }
      },
      plotOptions: {
        bar: {
          borderRadius: 8,
          columnWidth: '65%',
          dataLabels: {
            position: 'top'
          },
          distributed: true
        }
      },
      legend: {
        show: true,
        position: 'bottom',
        horizontalAlign: 'center',
        fontSize: '12px',
        fontFamily: 'almarai, sans-serif',
        fontWeight: 500,
        labels: {
          colors: isDark ? '#E5E7EB' : '#374151'
        },
        markers: {
          size: 8,
          shape: 'square'
        },
        itemMargin: {
          horizontal: 15,
          vertical: 5
        }
      },
      responsive: [
        {
          breakpoint: 768,
          options: {
            chart: {
              height: 280
            },
            plotOptions: {
              bar: {
                columnWidth: '80%'
              }
            },
            xaxis: {
              labels: {
                style: {
                  fontSize: '10px'
                }
              }
            }
          }
        }
      ]
    };

    // إعداد سلسلة البيانات
    const series = [{
      name: 'الكمية',
      data: chartData
    }];

    // حساب إحصائيات المخزون
    const totalQuantity = chartData.reduce((sum, item) => sum + (typeof item.y === 'number' ? item.y : 0), 0);
    const maxQuantity = Math.max(...chartData.map(item => typeof item.y === 'number' ? item.y : 0));
    const avgQuantity = chartData.length > 0 ? totalQuantity / chartData.length : 0;

    return (
      <div className="touch-card">
        {/* Chart Container */}
        <div className="h-80 md:h-64">
          <ReactApexChart
            type="bar"
            height="100%"
            width="100%"
            options={options}
            series={series}
          />
        </div>

        {/* Chart Summary */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* أعلى كمية */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">أعلى كمية</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{maxQuantity.toLocaleString()}</p>
                <p className="text-xs text-secondary-500 dark:text-secondary-400">وحدة</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-success-100 dark:bg-success-900/30 flex items-center justify-center">
                <FaArrowUp className="text-success-600 dark:text-success-400 text-xl" />
              </div>
            </div>
          </div>

          {/* متوسط الكمية */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">متوسط الكمية</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{Math.round(avgQuantity).toLocaleString()}</p>
                <p className="text-xs text-secondary-500 dark:text-secondary-400">وحدة</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center">
                <FaChartBar className="text-primary-600 dark:text-primary-400 text-xl" />
              </div>
            </div>
          </div>

          {/* إجمالي المخزون */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">إجمالي المخزون</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{totalQuantity.toLocaleString()}</p>
                <p className="text-xs text-secondary-500 dark:text-secondary-400">وحدة</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-warning-100 dark:bg-warning-900/30 flex items-center justify-center">
                <FaHdd className="text-warning-600 dark:text-warning-400 text-xl" />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // إعداد عرض إحصائيات النظام
  const renderSystemStats = () => {
    // التحقق من صلاحيات المدير
    if (!currentUser || currentUser.role !== 'admin') {
      return (
        <div className="text-center py-12 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
          <div className="bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300 p-4 rounded-full mb-3 inline-block">
            <FaServer className="text-3xl" />
          </div>
          <h3 className="text-xl font-bold mb-2 text-gray-900 dark:text-gray-100">غير مصرح</h3>
          <p className="text-gray-600 dark:text-gray-400">تقارير النظام متاحة للمديرين فقط</p>
          <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">يرجى تسجيل الدخول بحساب مدير للوصول إلى هذه الصفحة</p>
        </div>
      );
    }

    return (
      <div>
        {/* عنوان القسم */}
        <div className="flex items-center mb-6">
          <div className="bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300 p-2 rounded-lg ml-3">
            <FaServer className="text-xl" />
          </div>
          <h2 className="font-bold text-xl text-secondary-900 dark:text-secondary-100">معلومات النظام</h2>
        </div>

        {/* التبويبات الفرعية للنظام */}
        <div className="touch-card bg-white dark:bg-gray-800 rounded-xl p-4 mb-6">
          <div className="flex border-b border-gray-200 dark:border-gray-700 mb-4 overflow-x-auto pb-1 custom-scrollbar-thin">
            <button
              onClick={() => setSystemSubTab('system-actions')}
              className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
                systemSubTab === 'system-actions'
                  ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <FaCog className="ml-2" />
              <span>إجراءات النظام</span>
            </button>
            <button
              onClick={() => setSystemSubTab('system-logs')}
              className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
                systemSubTab === 'system-logs'
                  ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <FaBug className="ml-2" />
              <span>مراقبة النظام</span>
            </button>
            <button
              onClick={() => setSystemSubTab('test-alerts')}
              className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
                systemSubTab === 'test-alerts'
                  ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <FaFlask className="ml-2" />
              <span>اختبار التنبيهات</span>
            </button>
            <button
              onClick={() => setSystemSubTab('connected-devices')}
              className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
                systemSubTab === 'connected-devices'
                  ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <FaNetworkWired className="ml-2" />
              <span>الأجهزة المتصلة</span>
            </button>
            <button
              onClick={() => setSystemSubTab('device-security')}
              className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
                systemSubTab === 'device-security'
                  ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <FaUserShield className="ml-2" />
              <span>أمان الأجهزة</span>
            </button>
          </div>
        </div>

        {/* عرض المحتوى بناءً على التبويبة المحددة */}
        {systemSubTab === 'system-actions' && (
          <>
            {/* System Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
          {/* إجمالي المستخدمين */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">إجمالي المستخدمين</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{systemStats.totalUsers}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center">
                <FaUsers className="text-primary-600 dark:text-primary-400 text-xl" />
              </div>
            </div>
          </div>

          {/* المستخدمين النشطين */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">المستخدمين النشطين</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{systemStats.activeUsers}</p>
                <p className="text-xs text-secondary-500 dark:text-secondary-400">آخر 30 يوم</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-success-100 dark:bg-success-900/30 flex items-center justify-center">
                <FaUserCheck className="text-success-600 dark:text-success-400 text-xl" />
              </div>
            </div>
          </div>

          {/* نسبة النشاط */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">نسبة النشاط</p>
                <p className="text-2xl font-bold text-success-600 dark:text-success-400">
                  {systemStats.totalUsers > 0
                    ? Math.round((systemStats.activeUsers / systemStats.totalUsers) * 100)
                    : 0}%
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-info-100 dark:bg-info-900/30 flex items-center justify-center">
                <FaArrowUp className="text-info-600 dark:text-info-400 text-xl" />
              </div>
            </div>
          </div>

          {/* آخر تسجيل دخول */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">آخر تسجيل دخول</p>
                <p className="text-lg font-bold text-secondary-900 dark:text-secondary-100">{systemStats.lastLogin}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-warning-100 dark:bg-warning-900/30 flex items-center justify-center">
                <FaUserCheck className="text-warning-600 dark:text-warning-400 text-xl" />
              </div>
            </div>
          </div>

          {/* وقت تشغيل النظام */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">وقت تشغيل النظام</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{systemStats.systemUptime}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                <FaClock className="text-purple-600 dark:text-purple-400 text-xl" />
              </div>
            </div>
          </div>

          {/* حجم قاعدة البيانات */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">حجم قاعدة البيانات</p>
                <p className="text-2xl font-bold text-secondary-900 dark:text-secondary-100">{systemStats.databaseSize}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-secondary-100 dark:bg-secondary-900/30 flex items-center justify-center">
                <FaHdd className="text-secondary-600 dark:text-secondary-400 text-xl" />
              </div>
            </div>
          </div>

          {/* آخر نسخة احتياطية */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">آخر نسخة احتياطية</p>
                <p className="text-lg font-bold text-secondary-900 dark:text-secondary-100">{systemStats.lastBackup}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-danger-100 dark:bg-danger-900/30 flex items-center justify-center">
                <FaDatabase className="text-danger-600 dark:text-danger-400 text-xl" />
              </div>
            </div>
          </div>

          {/* إصدار النظام */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">إصدار النظام</p>
                <p className="text-lg font-bold text-secondary-900 dark:text-secondary-100">SmartPOS v1.0.0</p>
                <p className="text-xs text-secondary-500 dark:text-secondary-400">React + FastAPI</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                <FaCog className="text-blue-600 dark:text-blue-400 text-xl" />
              </div>
            </div>
          </div>

          {/* حالة الخادم */}
          <div className="touch-card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1">حالة الخادم</p>
                <p className="text-lg font-bold text-success-600 dark:text-success-400">متصل</p>
                <p className="text-xs text-secondary-500 dark:text-secondary-400">localhost:8002</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-success-100 dark:bg-success-900/30 flex items-center justify-center">
                <FaServer className="text-success-600 dark:text-success-400 text-xl" />
              </div>
            </div>
          </div>
        </div>

        {/* إجراءات النظام وجدول النسخ الاحتياطية */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* قسم الإجراءات */}
          <div className="touch-card">
            <div className="flex justify-between items-center mb-5">
              <div className="flex items-center">
                <div className="bg-secondary-100 dark:bg-secondary-900/30 text-secondary-600 dark:text-secondary-300 p-2 rounded-lg ml-3">
                  <FaCog className="text-xl" />
                </div>
                <h3 className="font-bold text-lg text-secondary-900 dark:text-secondary-100">إجراءات النظام</h3>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
              {/* زر إنشاء نسخة احتياطية */}
              <button
                onClick={openCreateBackupModal}
                className="group relative bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-medium py-2.5 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-60 flex items-center justify-center"
                disabled={isLoading}
              >
                <div className="flex items-center">
                  <div className="bg-white/20 p-1.5 rounded-lg ml-3">
                    <FaDatabase className="text-base" />
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-sm">إنشاء نسخة احتياطية</div>
                    <div className="text-xs text-green-100">حفظ البيانات بأمان</div>
                  </div>
                </div>
                {isLoading && (
                  <div className="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  </div>
                )}
              </button>

              {/* زر تحديث النظام */}
              <button
                onClick={openSystemUpdateModal}
                className="group relative bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-medium py-2.5 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-60 flex items-center justify-center"
                disabled={isLoading}
              >
                <div className="flex items-center">
                  <div className="bg-white/20 p-1.5 rounded-lg ml-3">
                    <FaSync className="text-base" />
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-sm">تحديث النظام</div>
                    <div className="text-xs text-blue-100">فحص التحديثات المتاحة</div>
                  </div>
                </div>
                {isLoading && (
                  <div className="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  </div>
                )}
              </button>

              {/* زر مسح التخزين المؤقت */}
              <button
                onClick={openClearCacheModal}
                className="group relative bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-medium py-2.5 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-60 flex items-center justify-center"
                disabled={isLoading}
              >
                <div className="flex items-center">
                  <div className="bg-white/20 p-1.5 rounded-lg ml-3">
                    <FaTrash className="text-base" />
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-sm">مسح التخزين المؤقت</div>
                    <div className="text-xs text-orange-100">تنظيف البيانات المؤقتة</div>
                  </div>
                </div>
                {isLoading && (
                  <div className="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  </div>
                )}
              </button>
            </div>

            {/* مكون تشخيص التوثيق */}
            <AuthDebug />
          </div>

          {/* قسم النسخ الاحتياطية مع التبويبات الفرعية */}
          <div className="touch-card">
            <div className="flex justify-between items-center mb-5">
              <div className="flex items-center">
                <div className="bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300 p-2 rounded-lg ml-3">
                  <FaDatabase className="text-xl" />
                </div>
                <h3 className="font-bold text-lg text-secondary-900 dark:text-secondary-100">النسخ الاحتياطية</h3>
              </div>
            </div>

            {/* التبويبات الفرعية للنسخ الاحتياطية */}
            <div className="flex border-b border-gray-200 dark:border-gray-700 mb-4 overflow-x-auto pb-1 custom-scrollbar-thin">
              <button
                onClick={() => setBackupSubTab('local')}
                className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
                  backupSubTab === 'local'
                    ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <FaHdd className="ml-2" />
                <span>النسخ المحلية</span>
              </button>
              <button
                onClick={() => setBackupSubTab('google-drive')}
                className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
                  backupSubTab === 'google-drive'
                    ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <FaCloud className="ml-2" />
                <span>نسخ Google Drive</span>
              </button>
            </div>

            {/* عرض المحتوى بناءً على التبويبة المحددة */}
            {backupSubTab === 'local' && (
              <LocalBackups
                onError={(error) => {
                  setSuccessModal({
                    isOpen: true,
                    title: 'خطأ في النسخ المحلية',
                    message: error,
                    details: null,
                    autoClose: false
                  });
                }}
                onSuccess={(message) => {
                  setSuccessModal({
                    isOpen: true,
                    title: 'نجح العملية',
                    message: message,
                    details: null,
                    autoClose: true
                  });
                }}
                onShowAllBackups={() => setAllBackupsModal(true)}
                onRestoreBackup={(backupName) => openRestoreModal(backupName)}
                onDeleteBackup={(backupName) => openDeleteModal(backupName)}
              />
            )}

            {/* عرض تبويب Google Drive */}
            {backupSubTab === 'google-drive' && (
              <GoogleDriveBackups
                onError={(error) => {
                  setSuccessModal({
                    isOpen: true,
                    title: 'خطأ في Google Drive',
                    message: error,
                    details: null,
                    autoClose: false
                  });
                }}
                onSuccess={(message) => {
                  setSuccessModal({
                    isOpen: true,
                    title: 'نجح العملية',
                    message: message,
                    details: null,
                    autoClose: true
                  });
                }}
              />
            )}
          </div>

        </div>
          </>
        )}

        {/* عرض مراقبة النظام */}
        {systemSubTab === 'system-logs' && (
          <div key="system-logs-container">
            {systemLogsComponent}
          </div>
        )}

        {/* عرض اختبار التنبيهات */}
        {systemSubTab === 'test-alerts' && (
          <div key="test-alerts-container">
            <div className="p-4 text-center text-gray-500">
              مكون اختبار التنبيهات غير متاح حالياً
            </div>
          </div>
        )}

        {/* عرض الأجهزة المتصلة */}
        {systemSubTab === 'connected-devices' && (
          <div key="connected-devices-container">
            <ConnectedDevices />
          </div>
        )}

        {/* عرض أمان الأجهزة */}
        {systemSubTab === 'device-security' && (
          <div key="device-security-container">
            <DeviceSecurityManager />
          </div>
        )}
      </div>
    );
  };



  // تحديث البيانات (manual handling like Products.tsx)
  const handleRefresh = async () => {
    console.log('🔄 [REPORTS] Manual refresh triggered for:', selectedReportType);
    await fetchDataForReportType(selectedReportType);
  };

  // فتح نافذة تأكيد إنشاء النسخة الاحتياطية
  const openCreateBackupModal = () => {
    setConfirmModal({
      isOpen: true,
      type: 'backup',
      title: 'تأكيد إنشاء نسخة احتياطية',
      message: 'هل تريد إنشاء نسخة احتياطية من قاعدة البيانات الحالية؟',
      description: 'سيتم إنشاء نسخة كاملة من جميع البيانات وحفظها في مجلد النسخ الاحتياطية.',
      isLoading: false
    });
  };

  // تأكيد إنشاء النسخة الاحتياطية
  const confirmCreateBackup = async () => {
    setConfirmModal(prev => ({ ...prev, isLoading: true }));

    try {
      const response = await fetch('/api/dashboard/create-backup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // إغلاق نافذة التأكيد
        setConfirmModal(prev => ({ ...prev, isOpen: false, isLoading: false }));

        // إعداد تفاصيل النجاح
        const successDetails: any = {
          backup_name: data.backup_name,
          size: data.size,
          created_at: data.created_at
        };

        // إضافة معلومات Google Drive إذا كانت متوفرة
        if (data.google_drive) {
          successDetails.google_drive_status = data.google_drive.success ? 'نجح' : 'فشل';
          successDetails.google_drive_message = data.google_drive.message;
          if (data.google_drive.success && data.google_drive.file_name) {
            successDetails.google_drive_file = data.google_drive.file_name;
          }
        }

        // عرض نافذة النجاح
        setSuccessModal({
          isOpen: true,
          title: 'تم الإنشاء بنجاح',
          message: data.message || 'تم إنشاء النسخة الاحتياطية بنجاح',
          details: successDetails,
          autoClose: true
        });

        // تحديث إحصائيات النظام
        fetchSystemStats();
      } else {
        throw new Error(data.message || 'خطأ غير معروف');
      }
    } catch (error) {
      console.error('Error creating backup:', error);
      setConfirmModal(prev => ({ ...prev, isLoading: false }));

      // عرض رسالة خطأ
      setSuccessModal({
        isOpen: true,
        title: 'فشل في الإنشاء',
        message: `فشل في إنشاء النسخة الاحتياطية: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`,
        details: null,
        autoClose: false
      });
    }
  };

  // فتح نافذة تأكيد تحديث النظام
  const openSystemUpdateModal = () => {
    setConfirmModal({
      isOpen: true,
      type: 'update',
      title: 'تأكيد تحديث النظام',
      message: 'هل تريد تحديث النظام إلى أحدث إصدار؟',
      description: 'سيتم فحص التحديثات المتاحة وتطبيقها على النظام.',
      isLoading: false
    });
  };

  // تأكيد تحديث النظام
  const confirmSystemUpdate = async () => {
    setConfirmModal(prev => ({ ...prev, isLoading: true }));

    try {
      // محاكاة عملية التحديث
      await new Promise(resolve => setTimeout(resolve, 2000));

      // إغلاق نافذة التأكيد
      setConfirmModal(prev => ({ ...prev, isOpen: false, isLoading: false }));

      // عرض نافذة النجاح
      const currentDateTime = await getCurrentFormattedDateTime();
      setSuccessModal({
        isOpen: true,
        title: 'تم التحديث بنجاح',
        message: 'تم تحديث النظام بنجاح',
        details: {
          backup_name: 'تحديث النظام',
          size: 'مكتمل',
          created_at: currentDateTime
        },
        autoClose: true
      });
    } catch (error) {
      console.error('Error updating system:', error);
      setConfirmModal(prev => ({ ...prev, isLoading: false }));

      // عرض رسالة خطأ
      setSuccessModal({
        isOpen: true,
        title: 'فشل في التحديث',
        message: 'فشل في تحديث النظام',
        details: null,
        autoClose: false
      });
    }
  };

  // فتح نافذة تأكيد مسح التخزين المؤقت
  const openClearCacheModal = () => {
    setConfirmModal({
      isOpen: true,
      type: 'clear-cache',
      title: 'تأكيد مسح التخزين المؤقت',
      message: 'هل تريد مسح جميع بيانات التخزين المؤقت؟',
      description: 'سيتم مسح جميع البيانات المحفوظة محلياً وإعادة تحميل الصفحة.',
      isLoading: false
    });
  };

  // تأكيد مسح التخزين المؤقت
  const confirmClearCache = async () => {
    setConfirmModal(prev => ({ ...prev, isLoading: true }));

    try {
      // محاكاة عملية المسح
      await new Promise(resolve => setTimeout(resolve, 1000));

      // مسح localStorage
      localStorage.clear();
      // مسح sessionStorage
      sessionStorage.clear();

      // إغلاق نافذة التأكيد
      setConfirmModal(prev => ({ ...prev, isOpen: false, isLoading: false }));

      // عرض نافذة النجاح مع إعادة تحميل
      const currentDateTime = await getCurrentFormattedDateTime();
      setSuccessModal({
        isOpen: true,
        title: 'تم المسح بنجاح',
        message: 'تم مسح التخزين المؤقت بنجاح',
        details: {
          backup_name: 'مسح التخزين المؤقت',
          size: 'مكتمل',
          created_at: currentDateTime
        },
        autoClose: true
      });

      // إعادة تحميل الصفحة بعد ثانيتين
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } catch (error) {
      console.error('Error clearing cache:', error);
      setConfirmModal(prev => ({ ...prev, isLoading: false }));

      // عرض رسالة خطأ
      setSuccessModal({
        isOpen: true,
        title: 'فشل في المسح',
        message: 'فشل في مسح التخزين المؤقت',
        details: null,
        autoClose: false
      });
    }
  };

  // وظيفة موحدة للتعامل مع تأكيد النوافذ
  const handleConfirmAction = () => {
    switch (confirmModal.type) {
      case 'backup':
        confirmCreateBackup();
        break;
      case 'update':
        confirmSystemUpdate();
        break;
      case 'clear-cache':
        confirmClearCache();
        break;
      default:
        break;
    }
  };

  // دالة عرض تقرير مبيعات المستخدمين اليومية
  const renderDailyUserSales = () => {


    // التحقق من صلاحيات المدير
    if (!currentUser || currentUser.role !== 'admin') {
      return (
        <div className="text-center py-12 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
          <div className="bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300 p-4 rounded-full mb-3 inline-block">
            <FaUsers className="text-3xl" />
          </div>
          <h3 className="text-xl font-bold mb-2 text-gray-900 dark:text-gray-100">غير مصرح</h3>
          <p className="text-gray-600 dark:text-gray-400">تقارير مبيعات المستخدمين متاحة للمديرين فقط</p>
          <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">يرجى تسجيل الدخول بحساب مدير للوصول إلى هذه الصفحة</p>
        </div>
      );
    }

    if (dailyUserSalesLoading) {
      return (
        <div className="flex justify-center items-center h-64 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 dark:border-primary-400"></div>
        </div>
      );
    }

    if (!dailyUserSales) {
      return (
        <div className="space-y-6">
          {/* شريط التحكم */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-gray-50 dark:bg-gray-700/30 p-4 rounded-lg">
            <div className="flex items-center gap-3">
              <FaCalendarDay className="text-primary-600 dark:text-primary-400" />
              <span className="font-medium text-gray-700 dark:text-gray-300">تاريخ التقرير:</span>
              <div className="w-48">
                <DatePicker
                  name="reportDate"
                  value={selectedDate}
                  onChange={(date) => {
                    setSelectedDate(date);
                    fetchDailyUserSales(date);
                  }}
                  placeholder="اختر التاريخ"
                  className="text-sm"
                />
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                <LastUpdateTime updateTime={lastUpdateTime} />
              </div>
              <button
                onClick={() => fetchDailyUserSales(selectedDate, true)}
                disabled={dailyUserSalesLoading}
                className="flex items-center gap-2 px-3 py-1.5 bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-md disabled:cursor-not-allowed"
                title="تحديث البيانات"
              >
                <FaSync className={`text-xs ${dailyUserSalesLoading ? 'animate-spin' : ''}`} />
                تحديث
              </button>
            </div>
          </div>

          {/* رسالة لا توجد بيانات */}
          <div className="text-center py-12 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
            <div className="bg-orange-100 dark:bg-orange-900/50 text-orange-700 dark:text-orange-300 p-4 rounded-full mb-3 inline-block">
              <FaUsers className="text-3xl" />
            </div>
            <h3 className="text-xl font-bold mb-2 text-gray-900 dark:text-gray-100">لا توجد بيانات مبيعات</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              لا توجد بيانات مبيعات للتاريخ المحدد: {selectedDate}
            </p>
            <div className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
              <p>• تأكد من أن التاريخ المحدد صحيح</p>
              <p>• تأكد من وجود مبيعات في هذا التاريخ</p>
              <p>• جرب تاريخ آخر أو اليوم الحالي</p>
            </div>
          </div>
        </div>
      );
    }

    // إعداد بيانات المخطط مع ألوان مختلفة لكل مستخدم
    const chartData = dailyUserSales.top_users.map((user: any, index: number) => {
      const totalReceived = parseFloat(user.total_received) || 0;
      const productsTotal = parseFloat(user.products_total) || 0;
      const discounts = parseFloat(user.total_discounts) || 0;
      const taxes = parseFloat(user.total_taxes) || 0;

      // استخدام الأرباح المحسوبة من الخادم (نفس طريقة لوحة التحكم)
      const profits = parseFloat(user.total_profits) || 0;

      return {
        name: user.full_name || user.username,
        value: totalReceived,
        sales_count: parseInt(user.total_sales) || 0,
        products_total: productsTotal,
        discounts: discounts,
        taxes: taxes,
        debts: parseFloat(user.total_debts) || 0,
        profits: profits,
        color: index
      };
    });

    // ألوان متدرجة للمستخدمين حسب الأداء
    const userColors = isDark
      ? ['#60A5FA', '#34D399', '#FBBF24', '#F87171', '#A78BFA', '#F472B6', '#38BDF8', '#4ADE80']
      : ['#2563EB', '#059669', '#D97706', '#DC2626', '#7C3AED', '#DB2777', '#0284C7', '#16A34A'];



    const chartOptions: ApexOptions = {
      chart: {
        type: 'bar',
        fontFamily: 'almarai, sans-serif',
        toolbar: {
          show: true,
          tools: {
            download: true,
            selection: false,
            zoom: false,
            zoomin: false,
            zoomout: false,
            pan: false,
            reset: false
          }
        },
        locales: [{
          name: 'ar',
          options: {
            months: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            shortMonths: ['ي', 'ف', 'م', 'أ', 'م', 'ي', 'ي', 'أ', 'س', 'أ', 'ن', 'د'],
            days: ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
            shortDays: ['أحد', 'إثن', 'ثلا', 'أرب', 'خمي', 'جمع', 'سبت'],
            toolbar: {
              exportToSVG: "تحميل SVG",
              exportToPNG: "تحميل PNG",
              exportToCSV: "تحميل CSV",
              selection: "التحديد",
              selectionZoom: "تكبير التحديد",
              zoomIn: "تكبير",
              zoomOut: "تصغير",
              pan: "تحريك",
              reset: "إعادة تعيين التكبير"
            }
          }
        }],
        defaultLocale: 'ar',
        animations: {
          enabled: true,
          speed: 800,
          animateGradually: {
            enabled: true,
            delay: 150
          }
        },
        background: 'transparent'
      },
      theme: {
        mode: isDark ? 'dark' : 'light',
        palette: 'palette1'
      },
      colors: userColors,
      plotOptions: {
        bar: {
          horizontal: true,
          borderRadius: 8,
          columnWidth: '75%',
          dataLabels: {
            position: 'top'
          },
          distributed: true
        }
      },
      dataLabels: {
        enabled: false
      },
      xaxis: {
        categories: chartData.map((item: any) => item.name),
        labels: {
          style: {
            colors: isDark ? '#9CA3AF' : '#6B7280',
            fontSize: '12px',
            fontFamily: 'almarai, sans-serif'
          },
          formatter: function (val: any) {
            return val.length > 15 ? val.substring(0, 15) + '...' : val;
          }
        },
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        labels: {
          style: {
            colors: isDark ? '#9CA3AF' : '#6B7280',
            fontSize: '12px',
            fontFamily: 'almarai, sans-serif'
          },
          formatter: function (val: any) {
            const numVal = parseFloat(val);
            return isNaN(numVal) ? '' : formatCurrency(numVal);
          }
        }
      },
      grid: {
        borderColor: isDark ? '#374151' : '#E5E7EB',
        strokeDashArray: 3,
        yaxis: {
          lines: {
            show: true
          }
        },
        xaxis: {
          lines: {
            show: false
          }
        }
      },
      tooltip: {
        theme: isDark ? 'dark' : 'light',
        style: {
          fontSize: '13px',
          fontFamily: 'almarai, sans-serif'
        },
        custom: function({ dataPointIndex }) {
          try {
            if (dataPointIndex < 0 || dataPointIndex >= chartData.length) {
              return '';
            }

            const userData = chartData[dataPointIndex];
            const userColor = userColors[dataPointIndex % userColors.length];

            return `
              <div style="
                background: ${isDark ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.96)'};
                border: 1px solid ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(229, 231, 235, 0.4)'};
                border-radius: 8px;
                padding: 14px 18px;
                box-shadow: 0 8px 25px ${isDark ? 'rgba(0, 0, 0, 0.5)' : 'rgba(0, 0, 0, 0.08)'},
                           0 4px 10px ${isDark ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.12)'};
                backdrop-filter: blur(12px);
                font-family: 'almarai', sans-serif;
                direction: rtl;
                min-width: 240px;
                border: 2px solid ${userColor};
              ">
                <div style="
                  color: ${isDark ? '#F9FAFB' : '#111827'};
                  font-size: 15px;
                  font-weight: 700;
                  margin-bottom: 10px;
                  text-align: center;
                  padding: 6px;
                  background: ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(243, 244, 246, 0.5)'};
                  border-radius: 6px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                ">
                  <div style="
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                    background: ${userColor};
                    margin-left: 8px;
                  "></div>
                  ${userData.name}
                </div>
                <div style="
                  color: ${isDark ? '#D1D5DB' : '#4B5563'};
                  font-size: 12px;
                  margin-bottom: 6px;
                  display: flex;
                  justify-content: space-between;
                ">
                  <span>عدد المبيعات:</span>
                  <span style="font-weight: 600;">${userData.sales_count} عملية</span>
                </div>
                <div style="
                  color: ${isDark ? '#D1D5DB' : '#4B5563'};
                  font-size: 12px;
                  margin-bottom: 6px;
                  display: flex;
                  justify-content: space-between;
                ">
                  <span>سعر المنتجات:</span>
                  <span style="font-weight: 600;">${formatCurrency(userData.products_total)}</span>
                </div>
                <div style="
                  color: ${isDark ? '#D1D5DB' : '#4B5563'};
                  font-size: 12px;
                  margin-bottom: 6px;
                  display: flex;
                  justify-content: space-between;
                ">
                  <span>الخصومات:</span>
                  <span style="font-weight: 600; color: ${isDark ? '#FBBF24' : '#D97706'};">-${formatCurrency(userData.discounts)}</span>
                </div>
                <div style="
                  color: ${isDark ? '#D1D5DB' : '#4B5563'};
                  font-size: 12px;
                  margin-bottom: 6px;
                  display: flex;
                  justify-content: space-between;
                ">
                  <span>الضرائب:</span>
                  <span style="font-weight: 600; color: ${isDark ? '#60A5FA' : '#2563EB'};">+${formatCurrency(userData.taxes)}</span>
                </div>
                <div style="
                  color: ${isDark ? '#D1D5DB' : '#4B5563'};
                  font-size: 12px;
                  margin-bottom: 6px;
                  display: flex;
                  justify-content: space-between;
                ">
                  <span>الديون:</span>
                  <span style="font-weight: 600; color: ${isDark ? '#F87171' : '#DC2626'};">${formatCurrency(userData.debts)}</span>
                </div>
                <div style="
                  color: ${isDark ? '#D1D5DB' : '#4B5563'};
                  font-size: 12px;
                  margin-bottom: 8px;
                  display: flex;
                  justify-content: space-between;
                ">
                  <span>الأرباح:</span>
                  <span style="font-weight: 600; color: ${isDark ? '#34D399' : '#10B981'};">${formatCurrency(userData.profits)}</span>
                </div>
                <div style="
                  text-align: center;
                  padding: 8px;
                  background: ${userColor}20;
                  border-radius: 6px;
                  border: 1px solid ${userColor};
                ">
                  <div style="
                    color: ${userColor};
                    font-size: 14px;
                    font-weight: 800;
                  ">
                    ${formatCurrency(userData.value)}
                  </div>
                </div>
              </div>
            `;
          } catch (error) {
            return '';
          }
        }
      },
      legend: {
        show: false
      },
      responsive: [
        {
          breakpoint: 768,
          options: {
            chart: {
              height: 300
            },
            legend: {
              show: false
            }
          }
        }
      ]
    };

    const series = [{
      name: 'المبلغ المستلم',
      data: chartData.map((item: any) => parseFloat(item.value) || 0)
    }];

    return (
      <div className="space-y-6">
        {/* شريط التحكم */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-gray-50 dark:bg-gray-700/30 p-4 rounded-lg">
          <div className="flex items-center gap-3">
            <FaCalendarDay className="text-primary-600 dark:text-primary-400" />
            <span className="font-medium text-gray-700 dark:text-gray-300">تاريخ التقرير:</span>
            <div className="w-48">
              <DatePicker
                name="reportDate"
                value={selectedDate}
                onChange={(date) => {
                  setSelectedDate(date);
                  fetchDailyUserSales(date);
                }}
                placeholder="اختر التاريخ"
                className="text-sm"
              />
            </div>
          </div>
          <div className="flex items-center gap-3">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              <LastUpdateTime updateTime={lastUpdateTime} />
            </div>
            <button
              onClick={() => fetchDailyUserSales(selectedDate, true)}
              disabled={dailyUserSalesLoading}
              className="flex items-center gap-2 px-3 py-1.5 bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-md disabled:cursor-not-allowed"
              title="تحديث البيانات"
            >
              <FaSync className={`text-xs ${dailyUserSalesLoading ? 'animate-spin' : ''}`} />
              تحديث
            </button>
          </div>
        </div>

        {/* ملخص الإحصائيات مع الأرقام المختصرة - تخطيط محسن */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
          {/* عدد المبيعات */}
          <CompactStatCard
            title="عدد المبيعات"
            amount={dailyUserSales.summary.total_sales_count}
            icon={<FaShoppingCart className="text-blue-600 dark:text-blue-400" />}
            showCurrency={false}
            compactThreshold={100}
            unitType="english"
            className="p-6"
            changeText="إجمالي عدد عمليات البيع المنجزة"
            enableAnimation={true}
            animationDuration={2.5}
            animationDelay={0.2}
          />

          {/* سعر المنتجات */}
          <CompactStatCard
            title="سعر المنتجات"
            amount={dailyUserSales.summary.total_products_amount}
            icon={<FaShoppingCart className="text-gray-600 dark:text-gray-400" />}
            showCurrency={true}
            compactThreshold={100}
            unitType="english"
            className="p-6"
            changeText="قيمة المنتجات المباعة قبل الخصم والضريبة"
            enableAnimation={true}
            animationDuration={2.5}
            animationDelay={0.4}
          />

          {/* إجمالي الخصومات */}
          <CompactStatCard
            title="إجمالي الخصومات"
            amount={dailyUserSales.summary.total_discounts_amount}
            icon={<FaArrowDown className="text-orange-600 dark:text-orange-400" />}
            showCurrency={true}
            compactThreshold={100}
            unitType="english"
            className="p-6"
            changeText="مجموع الخصومات المطبقة على المبيعات"
            enableAnimation={true}
            animationDuration={2.5}
            animationDelay={0.6}
          />

          {/* إجمالي الضرائب */}
          <CompactStatCard
            title="إجمالي الضرائب"
            amount={dailyUserSales.summary.total_taxes_amount}
            icon={<FaArrowUp className="text-blue-600 dark:text-blue-400" />}
            showCurrency={true}
            compactThreshold={100}
            unitType="english"
            className="p-6"
            changeText="مجموع الضرائب المضافة على المبيعات"
            enableAnimation={true}
            animationDuration={2.5}
            animationDelay={0.8}
          />

        </div>

        {/* الصف الثاني من الإحصائيات */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mt-6">
          {/* المبلغ المستلم */}
          <CompactStatCard
            title="المبلغ المستلم"
            amount={dailyUserSales.summary.total_received_amount}
            icon={<FaArrowUp className="text-success-600 dark:text-success-400" />}
            showCurrency={true}
            compactThreshold={100}
            unitType="english"
            className="p-6"
            changeText="المبلغ الفعلي المحصل من العملاء"
            enableAnimation={true}
            animationDuration={2.5}
            animationDelay={1.0}
          />

          {/* الديون اليوم */}
          <CompactStatCard
            title="الديون اليوم"
            amount={Math.abs(dailyUserSales.summary.total_debts_amount || 0)}
            icon={<FaExclamationTriangle className="text-warning-600 dark:text-warning-400" />}
            showCurrency={true}
            compactThreshold={100}
            unitType="english"
            className="p-6"
            changeText="المبلغ المستحق غير المدفوع من العملاء"
            enableAnimation={true}
            animationDuration={2.5}
            animationDelay={1.2}
          />

          {/* أفضل المستخدم */}
          <div className="touch-card stats-card">
            <div className="flex justify-between items-start mb-2">
              <div className="flex-1">
                <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">
                  أفضل المستخدم
                </p>

                {/* اسم المستخدم كرقم رئيسي */}
                <div className="mb-2">
                  <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {(() => {
                      if (!dailyUserSales?.top_users || dailyUserSales.top_users.length === 0) return 'لا يوجد مستخدمين';
                      const topUser = dailyUserSales.top_users[0];
                      return topUser.full_name || topUser.username || 'غير محدد';
                    })()}
                  </span>
                </div>

                {/* السعر كنص صغير */}
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                  {(() => {
                    if (!dailyUserSales?.top_users || dailyUserSales.top_users.length === 0) return '';
                    const topUser = dailyUserSales.top_users[0];
                    const amount = parseFloat(topUser.total_received) || 0;
                    return `${amount.toLocaleString('ar-LY', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} د.ل`;
                  })()}
                </p>
              </div>

              {/* الأيقونة بنفس تصميم باقي البطاقات */}
              <div className="p-4 rounded-full bg-primary-50 dark:bg-primary-900/30 text-xl">
                <FaTrophy className="text-yellow-600 dark:text-yellow-400" />
              </div>
            </div>

            {/* الفاصل الخفيف */}
            <div className="stats-card-divider"></div>

            {/* الوصف التوضيحي - بنفس تصميم باقي البطاقات */}
            <div className="flex items-center text-sm text-secondary-500 dark:text-secondary-400">
              <span>
                {(() => {
                  if (!dailyUserSales?.top_users || dailyUserSales.top_users.length === 0) return 'لا توجد بيانات مبيعات';
                  const topUser = dailyUserSales.top_users[0];
                  const salesCount = topUser.total_sales || 0;
                  return `المستخدم الأعلى مبيعاً بـ ${salesCount} عملية بيع`;
                })()}
              </span>
            </div>
          </div>

          {/* أرباح اليوم */}
          <CompactStatCard
            title="أرباح اليوم"
            amount={dailyUserSales.summary.total_profits_amount || 0}
            icon={<FaChartLine className="text-success-600 dark:text-success-400" />}
            showCurrency={true}
            compactThreshold={100}
            unitType="english"
            className="p-6"
            changeText="صافي الربح المحقق من المبيعات"
            enableAnimation={true}
            animationDuration={2.5}
            animationDelay={1.4}
          />
        </div>

        {/* مخطط أفضل المستخدمين */}
        {chartData.length > 0 && (
          <div className="touch-card">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 flex items-center">
                <FaChartBar className="ml-2 text-primary-600 dark:text-primary-400" />
                أفضل المستخدمين مبيعاً
              </h3>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                أعلى {chartData.length} مستخدمين
              </div>
            </div>

            {/* Chart Container */}
            <div className="h-80 md:h-96">
              <ReactApexChart
                type="bar"
                height="100%"
                width="100%"
                options={chartOptions}
                series={series}
              />
            </div>

            {/* Chart Legend - عرض القيم أسفل المخطط */}
            <div className="mt-4 flex flex-wrap justify-center gap-4">
              {chartData.map((item: any, index: number) => (
                <div key={index} className="flex items-center gap-2 px-3 py-2 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <div
                    className="w-3 h-3 rounded-sm"
                    style={{ backgroundColor: userColors[index % userColors.length] }}
                  ></div>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {item.name}: <span className="font-bold text-gray-900 dark:text-gray-100">{formatCurrency(item.value)}</span>
                  </span>
                </div>
              ))}
            </div>

            {/* Chart Summary */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* أفضل مستخدم */}


            </div>
          </div>
        )}

        {/* جدول تفاصيل المستخدمين */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 flex items-center">
              <FaUsers className="ml-2 text-primary-600 dark:text-primary-400" />
              تفاصيل مبيعات المستخدمين
            </h3>
          </div>
          <div className="overflow-x-auto custom-scrollbar">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    المستخدم
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    عدد المبيعات
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    سعر المنتجات
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الخصومات
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الضرائب
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    المبلغ المستلم
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الديون
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الأرباح
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {dailyUserSales.users.map((user: any) => (
                  <tr key={user.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium">
                          {user.full_name ? user.full_name.charAt(0) : user.username.charAt(0)}
                        </div>
                        <div className="mr-3">
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {user.full_name || user.username}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            @{user.username}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      {user.total_sales}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                      {formatCurrency(user.products_total)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-orange-600 dark:text-orange-400">
                      -{formatCurrency(user.total_discounts)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400">
                      +{formatCurrency(user.total_taxes)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-success-600 dark:text-success-400">
                      {formatCurrency(user.total_received)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-danger-600 dark:text-danger-400">
                      {formatCurrency(Math.abs(user.total_debts || 0))}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-green-600 dark:text-green-400">
                      {formatCurrency(user.total_profits || 0)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  };

  // دالة عرض تقارير المديونية
  const renderDebtsReports = () => {
    // التحقق من صلاحيات المدير
    if (!currentUser || currentUser.role !== 'admin') {
      return (
        <div className="text-center py-12 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
          <div className="bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300 p-4 rounded-full mb-3 inline-block">
            <FaMoneyBillWave className="text-3xl" />
          </div>
          <h3 className="text-xl font-bold mb-2 text-gray-900 dark:text-gray-100">غير مصرح</h3>
          <p className="text-gray-600 dark:text-gray-400">تقارير المديونية متاحة للمديرين فقط</p>
          <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">يرجى تسجيل الدخول بحساب مدير للوصول إلى هذه الصفحة</p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {/* شريط الأدوات */}
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">
            تقارير المديونية
          </h2>
          <div className="flex space-x-2 space-x-reverse">
            <button
              onClick={() => window.print()}
              className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium min-w-[140px] focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl gap-2"
            >
              <FaPrint className="ml-2" />
              طباعة
            </button>
            <button
              onClick={() => {
                // تصدير البيانات كـ CSV
                const csvData = [
                  ['نوع الإحصائية', 'القيمة'],
                  ['إجمالي المديونية', formatCurrency(debtSummary.totalAmount)],
                  ['المبلغ المحصل', formatCurrency(debtSummary.paidAmount)],
                  ['المبلغ المتبقي', formatCurrency(debtSummary.remainingAmount)],
                  ['معدل التحصيل', `${debtSummary.collectionRate}%`],
                  ['عدد العملاء المدينين', debtSummary.uniqueDebtors.toString()],
                  ['متوسط قيمة الدين', formatCurrency(debtSummary.averageDebtAmount)],
                  ['متوسط عمر الديون', `${debtSummary.averageDebtAge} يوم`]
                ];

                const csvContent = csvData.map(row => row.join(',')).join('\n');
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `تقرير_المديونية_${new Date().toISOString().split('T')[0]}.csv`;
                link.click();
              }}
              className="bg-success-600 hover:bg-success-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-success-600 hover:border-success-700 flex items-center justify-center text-sm font-medium min-w-[140px] focus:outline-none focus:ring-4 focus:ring-success-500/20 shadow-lg hover:shadow-xl gap-2"
            >
              <FaDownload className="ml-2" />
              تصدير CSV
            </button>
          </div>
        </div>
        {/* ملخص المديونية */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <CompactStatCard
            title="إجمالي المديونية"
            amount={debtSummary.totalAmount}
            icon={<FaMoneyBillWave className="text-orange-600 dark:text-orange-400" />}
            showCurrency={true}
            compactThreshold={100}
            unitType="english"
            changeText={`${debtSummary.totalDebts} دين`}
            enableAnimation={true}
            animationDuration={2.5}
            animationDelay={0.2}
          />

          <CompactStatCard
            title="المبلغ المحصل"
            amount={debtSummary.paidAmount}
            icon={<FaArrowUp className="text-success-600 dark:text-success-400" />}
            showCurrency={true}
            compactThreshold={100}
            unitType="english"
            changeText={`معدل التحصيل: ${debtSummary.collectionRate.toFixed(1)}%`}
            enableAnimation={true}
            animationDuration={2.5}
            animationDelay={0.4}
          />

          <CompactStatCard
            title="المبلغ المتبقي"
            amount={debtSummary.remainingAmount}
            icon={<FaArrowDown className="text-danger-600 dark:text-danger-400" />}
            showCurrency={true}
            compactThreshold={100}
            unitType="english"
            changeText="ديون غير مسددة"
            enableAnimation={true}
            animationDuration={2.5}
            animationDelay={0.6}
          />

          <CompactStatCard
            title="الديون المتأخرة"
            amount={debtSummary.overdueAmount}
            icon={<FaExclamationTriangle className="text-warning-600 dark:text-warning-400" />}
            showCurrency={true}
            compactThreshold={100}
            unitType="english"
            changeText={`${debtSummary.overdueDebts} دين متأخر`}
            enableAnimation={true}
            animationDuration={2.5}
            animationDelay={0.8}
          />
        </div>

        {/* إحصائيات إضافية */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <CompactStatCard
            title="عدد العملاء المدينين"
            amount={debtSummary.uniqueDebtors}
            icon={<FaUsers className="text-blue-600 dark:text-blue-400" />}
            showCurrency={false}
            compactThreshold={10}
            unitType="english"
            changeText="عميل مدين"
            enableAnimation={true}
            animationDuration={2.5}
            animationDelay={1.2}
          />

          <CompactStatCard
            title="متوسط قيمة الدين"
            amount={debtSummary.averageDebtAmount}
            icon={<FaChartPie className="text-purple-600 dark:text-purple-400" />}
            showCurrency={true}
            compactThreshold={100}
            unitType="english"
            changeText={`متوسط عمر: ${debtSummary.averageDebtAge.toFixed(1)} يوم`}
            enableAnimation={true}
            animationDuration={2.5}
            animationDelay={1.4}
          />

          <CompactStatCard
            title="أكبر دين"
            amount={debtSummary.maxDebt}
            icon={<FaArrowUp className="text-red-600 dark:text-red-400" />}
            showCurrency={true}
            compactThreshold={100}
            unitType="english"
            changeText="أعلى مبلغ مستحق"
            enableAnimation={true}
            animationDuration={2.5}
            animationDelay={1.6}
          />

          <CompactStatCard
            title="أصغر دين"
            amount={debtSummary.minDebt}
            icon={<FaArrowDown className="text-green-600 dark:text-green-400" />}
            showCurrency={true}
            compactThreshold={100}
            unitType="english"
            changeText="أقل مبلغ مستحق"
            enableAnimation={true}
            animationDuration={2.5}
            animationDelay={1.8}
          />
        </div>

        {/* تقرير أعمار الديون */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
            <FaClock className="ml-2 text-primary-600 dark:text-primary-400" />
            تقرير أعمار الديون
          </h3>
          {/* تخطيط البطاقات والمخطط */}
          {debtAging.length > 0 && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              {/* البطاقات الإحصائية - عمودين وصفين على اليمين */}
              <div className="lg:col-span-1 order-2 lg:order-1">
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700 h-full">
                  <div className="grid grid-cols-2 gap-3 h-full">
                    {debtAging.map((aging, index) => (
                      <div key={index} className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 flex flex-col justify-center">
                        <div className="text-center">
                          <div
                            className="p-2 rounded-lg text-lg mx-auto mb-2 w-fit"
                            style={{
                              backgroundColor: (isDark
                                ? ['rgba(52, 211, 153, 0.2)', 'rgba(251, 191, 36, 0.2)', 'rgba(248, 113, 113, 0.2)', 'rgba(167, 139, 250, 0.2)']
                                : ['rgba(16, 185, 129, 0.1)', 'rgba(217, 119, 6, 0.1)', 'rgba(220, 38, 38, 0.1)', 'rgba(124, 58, 237, 0.1)'])[index % 4],
                              color: (isDark
                                ? ['#34D399', '#FBBF24', '#F87171', '#A78BFA']
                                : ['#10B981', '#D97706', '#DC2626', '#7C3AED'])[index % 4]
                            }}
                          >
                            {index === 0 ? <FaCalendarCheck /> : index === 1 ? <FaClock /> : index === 2 ? <FaExclamationTriangle /> : <FaExclamationCircle />}
                          </div>
                          <p className="text-xs text-secondary-500 dark:text-secondary-400 font-medium mb-1">
                            {aging.range}
                          </p>
                          <p className="text-lg font-bold text-secondary-900 dark:text-secondary-100 mb-1">
                            {aging.percentage.toFixed(1)}%
                          </p>
                          <p className="text-sm font-semibold text-secondary-700 dark:text-secondary-300 mb-1">
                            {formatCurrency(aging.amount)}
                          </p>
                          <p className="text-xs text-secondary-500 dark:text-secondary-400">
                            {aging.count} دين
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* المخطط الدائري - على اليسار */}
              <div className="lg:col-span-2 order-1 lg:order-2">
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700 h-full">
                  <h4 className="text-md font-semibold text-secondary-900 dark:text-secondary-100 mb-4 text-center flex items-center justify-center">
                    <FaClock className="ml-2 text-primary-600 dark:text-primary-400" />
                    توزيع أعمار الديون
                  </h4>
                  <div className="h-80 md:h-96 lg:h-[400px]">
                <ReactApexChart
                  options={{
                    chart: {
                      type: 'donut',
                      fontFamily: 'almarai, sans-serif',
                      toolbar: {
                        show: false
                      },
                      animations: {
                        enabled: true,
                        speed: 500,
                        animateGradually: {
                          enabled: true,
                          delay: 150
                        },
                        dynamicAnimation: {
                          enabled: true,
                          speed: 350
                        }
                      },
                      background: 'transparent'
                    },
                    theme: {
                      mode: isDark ? 'dark' : 'light',
                      palette: 'palette1'
                    },
                    colors: isDark
                      ? ['#34D399', '#FBBF24', '#F87171', '#A78BFA']
                      : ['#10B981', '#D97706', '#DC2626', '#7C3AED'],
                    labels: debtAging.map(aging => aging.range),
                    dataLabels: {
                      enabled: true,
                      formatter: (val: number) => {
                        return `${val.toFixed(1)}%`;
                      },
                      style: {
                        fontSize: '13px',
                        fontFamily: 'almarai, sans-serif',
                        fontWeight: '700',
                        colors: ['#FFFFFF']
                      },
                      dropShadow: {
                        enabled: true,
                        top: 1,
                        left: 1,
                        blur: 2,
                        color: '#000000',
                        opacity: 0.6
                      }
                    },
                    stroke: {
                      show: true,
                      curve: 'smooth',
                      lineCap: 'round',
                      width: 3,
                      colors: [isDark ? '#1F2937' : '#FFFFFF']
                    },
                    fill: {
                      type: 'solid'
                    },
                    plotOptions: {
                      pie: {
                        donut: {
                          size: '75%',
                          labels: {
                            show: true,
                            name: {
                              show: true,
                              fontSize: '14px',
                              fontFamily: 'almarai, sans-serif',
                              fontWeight: 600,
                              color: isDark ? '#9CA3AF' : '#6B7280',
                              offsetY: -25,
                              formatter: function () {
                                return 'النسبة المئوية';
                              }
                            },
                            value: {
                              show: true,
                              fontSize: '32px',
                              fontFamily: 'almarai, sans-serif',
                              fontWeight: 800,
                              color: isDark ? '#60A5FA' : '#3B82F6',
                              offsetY: 20,
                              formatter: function (val: any) {
                                return `${val}%`;
                              }
                            },
                            total: {
                              show: true,
                              showAlways: true,
                              label: 'توزيع الأعمار',
                              fontSize: '28px',
                              fontFamily: 'almarai, sans-serif',
                              fontWeight: 800,
                              color: isDark ? '#60A5FA' : '#2563EB',
                              formatter: function () {
                                return '100%';
                              }
                            }
                          }
                        },
                        expandOnClick: true
                      }
                    },
                    legend: {
                      show: false
                    },
                    tooltip: {
                      theme: isDark ? 'dark' : 'light',
                      style: {
                        fontSize: '13px',
                        fontFamily: 'almarai, sans-serif'
                      },
                      custom: function({ series, seriesIndex, w }) {
                        const agingData = debtAging[seriesIndex];
                        const percentage = series[seriesIndex]; // الآن هذا هو النسبة المئوية
                        const rangeName = w.config.labels[seriesIndex];
                        const count = agingData?.count || 0;
                        const amount = agingData?.amount || 0; // المبلغ من البيانات الأصلية

                        const displayPercentage = percentage;

                        return `
                          <div style="
                            background: ${isDark ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.96)'};
                            border: 1px solid ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(229, 231, 235, 0.4)'};
                            border-radius: 8px;
                            padding: 14px 18px;
                            box-shadow: 0 8px 25px ${isDark ? 'rgba(0, 0, 0, 0.5)' : 'rgba(0, 0, 0, 0.08)'},
                                       0 4px 10px ${isDark ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.12)'};
                            backdrop-filter: blur(12px);
                            font-family: 'almarai', sans-serif;
                            direction: rtl;
                            min-width: 200px;
                            border: 2px solid ${(isDark
                              ? ['#34D399', '#FBBF24', '#F87171', '#A78BFA']
                              : ['#10B981', '#D97706', '#DC2626', '#7C3AED'])[seriesIndex % 4]};
                          ">
                            <div style="
                              color: ${isDark ? '#F9FAFB' : '#111827'};
                              font-size: 15px;
                              font-weight: 700;
                              margin-bottom: 8px;
                              display: flex;
                              align-items: center;
                            ">
                              <div style="
                                width: 10px;
                                height: 10px;
                                border-radius: 50%;
                                background: ${(isDark
                                  ? ['#34D399', '#FBBF24', '#F87171', '#A78BFA']
                                  : ['#10B981', '#D97706', '#DC2626', '#7C3AED'])[seriesIndex % 4]};
                                margin-left: 8px;
                              "></div>
                              ${rangeName}
                            </div>
                            <div style="
                              color: ${isDark ? '#D1D5DB' : '#4B5563'};
                              font-size: 12px;
                              margin-bottom: 6px;
                              display: flex;
                              justify-content: space-between;
                            ">
                              <span>عدد الديون:</span>
                              <span style="font-weight: 600;">${count} دين</span>
                            </div>
                            <div style="
                              color: ${isDark ? '#D1D5DB' : '#4B5563'};
                              font-size: 12px;
                              margin-bottom: 6px;
                              display: flex;
                              justify-content: space-between;
                            ">
                              <span>المبلغ:</span>
                              <span style="font-weight: 600;">${formatCurrency(amount)}</span>
                            </div>
                            <div style="
                              color: ${(isDark
                                ? ['#34D399', '#FBBF24', '#F87171', '#A78BFA']
                                : ['#10B981', '#D97706', '#DC2626', '#7C3AED'])[seriesIndex % 4]};
                              font-size: 16px;
                              font-weight: 800;
                              text-align: center;
                              margin-top: 8px;
                              padding: 6px;
                              background: ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(243, 244, 246, 0.5)'};
                              border-radius: 6px;
                            ">
                              ${displayPercentage.toFixed(1)}%
                            </div>
                          </div>
                        `;
                      }
                    },
                    responsive: [
                      {
                        breakpoint: 768,
                        options: {
                          chart: {
                            height: 300
                          },
                          legend: {
                            position: 'bottom',
                            fontSize: '12px'
                          },
                          dataLabels: {
                            enabled: false
                          }
                        }
                      }
                    ]
                  }}
                  series={debtAging.map(aging => aging.percentage)}
                  type="donut"
                  height="100%"
                />
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* أكبر المدينين */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 flex items-center">
              <FaUsers className="ml-2 text-primary-600 dark:text-primary-400" />
              أكبر المدينين
            </h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    العميل
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    إجمالي الدين
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    المبلغ المتبقي
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    عدد الديون
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    مستوى المخاطر
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {topDebtors.map((debtor) => (
                  <tr key={debtor.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {debtor.name}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      {formatCurrency(debtor.totalDebt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-danger-600 dark:text-danger-400">
                      {formatCurrency(debtor.remainingDebt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                      {debtor.debtCount}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        debtor.riskLevel === 'high'
                          ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                          : debtor.riskLevel === 'medium'
                          ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                          : 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                      }`}>
                        {debtor.riskLevel === 'high' ? 'عالي' : debtor.riskLevel === 'medium' ? 'متوسط' : 'منخفض'}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* أزرار الفلترة الزمنية */}
        <div ref={periodButtonsRef} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 flex items-center">
              <FaCalendarAlt className="ml-2 text-primary-600 dark:text-primary-400" />
              فلترة الفترة الزمنية
            </h3>
            <div className="flex flex-wrap gap-2">
              {(['day', 'week', 'month', 'year'] as ReportPeriod[]).map((period) => (
                <button
                  key={period}
                  onClick={() => handlePeriodChange(period)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    selectedPeriod === period
                      ? 'bg-primary-600 text-white shadow-md'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  {period === 'day' ? <FaCalendarDay className="text-xs" /> :
                   period === 'week' ? <FaCalendarWeek className="text-xs" /> :
                   period === 'month' ? <FaCalendar className="text-xs" /> : <FaCalendarAlt className="text-xs" />}
                  {period === 'day' ? 'يوم' :
                   period === 'week' ? 'أسبوع' :
                   period === 'month' ? 'شهر' : 'سنة'}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* مخطط اتجاهات المديونية */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
            <FaChartLine className="ml-2 text-primary-600 dark:text-primary-400" />
            اتجاهات المديونية
          </h3>
          {debtTrends.length > 0 ? (
            <div className="h-80">
              <ReactApexChart
                options={{
                  chart: {
                    type: 'line',
                    fontFamily: 'almarai, sans-serif',
                    toolbar: {
                      show: true,
                      tools: {
                        download: true,
                        selection: false,
                        zoom: true,
                        zoomin: true,
                        zoomout: true,
                        pan: false,
                        reset: true
                      }
                    },
                    locales: [{
                      name: 'ar',
                      options: {
                        months: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                        shortMonths: ['ي', 'ف', 'م', 'أ', 'م', 'ي', 'ي', 'أ', 'س', 'أ', 'ن', 'د'],
                        days: ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
                        shortDays: ['أحد', 'إثن', 'ثلا', 'أرب', 'خمي', 'جمع', 'سبت'],
                        toolbar: {
                          exportToSVG: "تحميل SVG",
                          exportToPNG: "تحميل PNG",
                          exportToCSV: "تحميل CSV",
                          selection: "التحديد",
                          selectionZoom: "تكبير التحديد",
                          zoomIn: "تكبير",
                          zoomOut: "تصغير",
                          pan: "تحريك",
                          reset: "إعادة تعيين التكبير"
                        }
                      }
                    }],
                    defaultLocale: 'ar',
                    animations: {
                      enabled: true,
                      speed: 800,
                      animateGradually: {
                        enabled: true,
                        delay: 150
                      }
                    },
                    background: 'transparent'
                  },
                  theme: {
                    mode: isDark ? 'dark' : 'light',
                    palette: 'palette1'
                  },
                  colors: isDark ? ['#FBBF24', '#34D399'] : ['#D97706', '#10B981'],
                  stroke: {
                    width: 4,
                    curve: 'smooth',
                    lineCap: 'round'
                  },
                  markers: {
                    size: 6,
                    strokeWidth: 3,
                    strokeColors: isDark ? '#1F2937' : '#FFFFFF',
                    hover: {
                      size: 8
                    }
                  },
                  xaxis: {
                    categories: debtTrends.map(item => formatChartDateShort(item.date, selectedPeriod)),
                    labels: {
                      style: {
                        colors: isDark ? '#9CA3AF' : '#6B7280',
                        fontSize: '12px',
                        fontFamily: 'almarai, sans-serif'
                      }
                    },
                    axisBorder: {
                      show: false
                    },
                    axisTicks: {
                      show: false
                    }
                  },
                  yaxis: {
                    labels: {
                      style: {
                        colors: isDark ? '#9CA3AF' : '#6B7280',
                        fontSize: '12px',
                        fontFamily: 'almarai, sans-serif'
                      },
                      formatter: (value: number) => formatCurrency(value)
                    }
                  },
                  tooltip: {
                    theme: isDark ? 'dark' : 'light',
                    style: {
                      fontSize: '13px',
                      fontFamily: 'almarai, sans-serif'
                    },
                    custom: function({ series, seriesIndex, dataPointIndex, w }) {
                      const trendData = debtTrends[dataPointIndex];
                      if (!trendData) return '';

                      const seriesName = w.config.series[seriesIndex].name;
                      const value = series[seriesIndex][dataPointIndex];
                      const date = trendData.date || '';

                      // معلومات إضافية من البيانات - التأكد من وجودها
                      const newDebts = trendData.newDebts || 0;
                      const paidDebts = trendData.paidDebts || 0;
                      const newAmount = trendData.newAmount || 0;
                      const paidAmount = trendData.paidAmount || 0;
                      const netChange = (newAmount - paidAmount);

                      // تحديد نوع النشاط في هذا اليوم
                      const hasNewDebts = newDebts > 0;
                      const hasPayments = paidDebts > 0;
                      const hasActivity = hasNewDebts || hasPayments;

                      // رسالة حالة النشاط
                      let activityStatus = '';
                      if (!hasActivity) {
                        activityStatus = 'لا توجد أنشطة';
                      } else if (!hasPayments) {
                        activityStatus = 'لا توجد مدفوعات';
                      } else if (!hasNewDebts) {
                        activityStatus = 'لا توجد ديون جديدة';
                      } else {
                        activityStatus = 'نشاط كامل';
                      }

                      return `
                        <div style="
                          background: ${isDark ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.96)'};
                          border: 1px solid ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(229, 231, 235, 0.4)'};
                          border-radius: 8px;
                          padding: 14px 18px;
                          box-shadow: 0 8px 25px ${isDark ? 'rgba(0, 0, 0, 0.5)' : 'rgba(0, 0, 0, 0.08)'},
                                     0 4px 10px ${isDark ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.12)'};
                          backdrop-filter: blur(12px);
                          font-family: 'almarai', sans-serif;
                          direction: rtl;
                          min-width: 220px;
                          border: 2px solid ${seriesIndex === 0 ? (isDark ? '#FBBF24' : '#D97706') : (isDark ? '#34D399' : '#10B981')};
                        ">
                          <div style="
                            color: ${isDark ? '#F9FAFB' : '#111827'};
                            font-size: 15px;
                            font-weight: 700;
                            margin-bottom: 10px;
                            text-align: center;
                            padding: 6px;
                            background: ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(243, 244, 246, 0.5)'};
                            border-radius: 6px;
                          ">
                            ${formatChartDateFull(date, selectedPeriod)}
                          </div>
                          <div style="
                            color: ${seriesIndex === 0 ? (isDark ? '#FBBF24' : '#D97706') : (isDark ? '#34D399' : '#10B981')};
                            font-size: 13px;
                            font-weight: 600;
                            margin-bottom: 8px;
                            display: flex;
                            align-items: center;
                          ">
                            <div style="
                              width: 8px;
                              height: 8px;
                              border-radius: 50%;
                              background: ${seriesIndex === 0 ? (isDark ? '#FBBF24' : '#D97706') : (isDark ? '#34D399' : '#10B981')};
                              margin-left: 6px;
                            "></div>
                            ${seriesName}
                          </div>
                          <div style="
                            color: ${isDark ? '#D1D5DB' : '#4B5563'};
                            font-size: 12px;
                            margin-bottom: 6px;
                            display: flex;
                            justify-content: space-between;
                          ">
                            <span>المبلغ:</span>
                            <span style="font-weight: 600;">${formatCurrency(value)}</span>
                          </div>
                          <div style="
                            color: ${isDark ? '#D1D5DB' : '#4B5563'};
                            font-size: 12px;
                            margin-bottom: 6px;
                            display: flex;
                            justify-content: space-between;
                          ">
                            <span>ديون جديدة:</span>
                            <span style="font-weight: 600;">${newDebts} (${formatCurrency(newAmount)})</span>
                          </div>
                          <div style="
                            color: ${isDark ? '#D1D5DB' : '#4B5563'};
                            font-size: 12px;
                            margin-bottom: 6px;
                            display: flex;
                            justify-content: space-between;
                          ">
                            <span>مدفوعات:</span>
                            <span style="font-weight: 600;">${paidDebts} (${formatCurrency(paidAmount)})</span>
                          </div>
                          <div style="
                            color: ${isDark ? '#D1D5DB' : '#4B5563'};
                            font-size: 12px;
                            margin-bottom: 6px;
                            display: flex;
                            justify-content: space-between;
                          ">
                            <span>حالة النشاط:</span>
                            <span style="font-weight: 600; color: ${!hasActivity ? (isDark ? '#9CA3AF' : '#6B7280') : hasActivity && hasNewDebts && hasPayments ? (isDark ? '#34D399' : '#10B981') : (isDark ? '#FBBF24' : '#D97706')};">
                              ${activityStatus}
                            </span>
                          </div>
                          <div style="
                            color: ${isDark ? '#D1D5DB' : '#4B5563'};
                            font-size: 12px;
                            margin-bottom: 8px;
                            display: flex;
                            justify-content: space-between;
                          ">
                            <span>صافي التغيير:</span>
                            <span style="font-weight: 600; color: ${netChange >= 0 ? (isDark ? '#F87171' : '#DC2626') : (isDark ? '#34D399' : '#10B981')};">
                              ${formatCurrency(Math.abs(netChange))} ${netChange >= 0 ? '↑' : '↓'}
                            </span>
                          </div>
                          <div style="
                            text-align: center;
                            padding: 8px;
                            background: ${seriesIndex === 0 ? (isDark ? 'rgba(251, 191, 36, 0.1)' : 'rgba(217, 119, 6, 0.1)') : (isDark ? 'rgba(52, 211, 153, 0.1)' : 'rgba(16, 185, 129, 0.1)')};
                            border-radius: 6px;
                            border: 1px solid ${seriesIndex === 0 ? (isDark ? '#FBBF24' : '#D97706') : (isDark ? '#34D399' : '#10B981')};
                          ">
                            <div style="
                              color: ${seriesIndex === 0 ? (isDark ? '#FBBF24' : '#D97706') : (isDark ? '#34D399' : '#10B981')};
                              font-size: 14px;
                              font-weight: 800;
                            ">
                              ${formatCurrency(value)}
                            </div>
                          </div>
                        </div>
                      `;
                    }
                  },
                  legend: {
                    show: true,
                    position: 'top',
                    horizontalAlign: 'center',
                    fontSize: '13px',
                    fontFamily: 'almarai, sans-serif',
                    fontWeight: 600,
                    labels: {
                      colors: isDark ? '#E5E7EB' : '#374151'
                    },
                    markers: {
                      size: 8,
                      shape: 'circle'
                    },
                    itemMargin: {
                      horizontal: 20,
                      vertical: 5
                    }
                  },
                  grid: {
                    borderColor: isDark ? '#374151' : '#E5E7EB',
                    strokeDashArray: 3,
                    xaxis: {
                      lines: {
                        show: false
                      }
                    }
                  }
                }}
                series={[
                  {
                    name: 'ديون جديدة',
                    data: debtTrends.map(item => item.newAmount)
                  },
                  {
                    name: 'مدفوعات',
                    data: debtTrends.map(item => item.paidAmount)
                  }
                ]}
                type="line"
                height="100%"
              />
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              لا توجد بيانات اتجاهات متاحة
            </div>
          )}
        </div>



        {/* كفاءة التحصيل */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
            <FaChartBar className="ml-2 text-primary-600 dark:text-primary-400" />
            كفاءة التحصيل
          </h3>
          {collectionEfficiency.length > 0 ? (
            <div className="h-80">
              <ReactApexChart
                options={{
                  chart: {
                    type: 'bar',
                    fontFamily: 'almarai, sans-serif',
                    toolbar: {
                      show: true,
                      tools: {
                        download: true,
                        selection: false,
                        zoom: false,
                        zoomin: false,
                        zoomout: false,
                        pan: false,
                        reset: false
                      }
                    },
                    locales: [{
                      name: 'ar',
                      options: {
                        months: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                        shortMonths: ['ي', 'ف', 'م', 'أ', 'م', 'ي', 'ي', 'أ', 'س', 'أ', 'ن', 'د'],
                        days: ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
                        shortDays: ['أحد', 'إثن', 'ثلا', 'أرب', 'خمي', 'جمع', 'سبت'],
                        toolbar: {
                          exportToSVG: "تحميل SVG",
                          exportToPNG: "تحميل PNG",
                          exportToCSV: "تحميل CSV",
                          selection: "التحديد",
                          selectionZoom: "تكبير التحديد",
                          zoomIn: "تكبير",
                          zoomOut: "تصغير",
                          pan: "تحريك",
                          reset: "إعادة تعيين التكبير"
                        }
                      }
                    }],
                    defaultLocale: 'ar',
                    animations: {
                      enabled: true,
                      speed: 800
                    },
                    background: 'transparent'
                  },
                  theme: {
                    mode: isDark ? 'dark' : 'light',
                    palette: 'palette1'
                  },
                  colors: isDark ? ['#60A5FA', '#34D399'] : ['#3B82F6', '#10B981'],
                  plotOptions: {
                    bar: {
                      horizontal: false,
                      columnWidth: '65%',
                      borderRadius: 8,
                      dataLabels: {
                        position: 'top'
                      }
                    }
                  },
                  dataLabels: {
                    enabled: true,
                    formatter: (val: number) => {
                      return val > 0 ? formatCurrency(val) : '';
                    },
                    style: {
                      fontSize: '12px',
                      fontFamily: 'almarai, sans-serif',
                      fontWeight: '800',
                      colors: [isDark ? '#FFFFFF' : '#1F2937']
                    },
                    offsetY: -25,
                    dropShadow: {
                      enabled: true,
                      top: 1,
                      left: 1,
                      blur: 3,
                      color: isDark ? '#000000' : '#FFFFFF',
                      opacity: isDark ? 0.6 : 0.8
                    }
                  },
                  stroke: {
                    show: true,
                    width: 2,
                    colors: ['transparent']
                  },
                  xaxis: {
                    categories: collectionEfficiency.map(item => formatChartDateShort(item.period, selectedPeriod)),
                    labels: {
                      style: {
                        colors: isDark ? '#9CA3AF' : '#6B7280',
                        fontSize: '12px',
                        fontFamily: 'almarai, sans-serif'
                      }
                    },
                    axisBorder: {
                      show: false
                    },
                    axisTicks: {
                      show: false
                    }
                  },
                  yaxis: {
                    labels: {
                      style: {
                        colors: isDark ? '#9CA3AF' : '#6B7280',
                        fontSize: '12px',
                        fontFamily: 'almarai, sans-serif'
                      },
                      formatter: (value: number) => formatCurrency(value)
                    }
                  },
                  fill: {
                    opacity: 1
                  },
                  tooltip: {
                    theme: isDark ? 'dark' : 'light',
                    style: {
                      fontSize: '13px',
                      fontFamily: 'almarai, sans-serif'
                    },
                    shared: true,
                    intersect: false,
                    followCursor: true,
                    custom: function({ dataPointIndex }) {
                      const efficiencyData = collectionEfficiency[dataPointIndex];
                      if (!efficiencyData) return '';

                      // عرض معلومات شاملة لكلا السلسلتين
                      const period = efficiencyData.period || '';
                      const efficiency = efficiencyData.efficiency || 0;
                      const targetCollection = efficiencyData.targetCollection || 0;
                      const actualCollection = efficiencyData.actualCollection || 0;
                      const variance = efficiencyData.variance || 0;

                      // تحديد حالة الأداء
                      let performanceStatus = '';
                      let performanceColor = '';
                      if (efficiency >= 100) {
                        performanceStatus = 'ممتاز';
                        performanceColor = isDark ? '#34D399' : '#10B981';
                      } else if (efficiency >= 75) {
                        performanceStatus = 'جيد';
                        performanceColor = isDark ? '#FBBF24' : '#D97706';
                      } else if (efficiency >= 50) {
                        performanceStatus = 'متوسط';
                        performanceColor = isDark ? '#FB923C' : '#EA580C';
                      } else if (efficiency > 0) {
                        performanceStatus = 'ضعيف';
                        performanceColor = isDark ? '#F87171' : '#DC2626';
                      } else {
                        performanceStatus = 'لا توجد مدفوعات';
                        performanceColor = isDark ? '#9CA3AF' : '#6B7280';
                      }

                      return `
                        <div style="
                          background: ${isDark ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.96)'};
                          border: 1px solid ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(229, 231, 235, 0.4)'};
                          border-radius: 8px;
                          padding: 14px 18px;
                          box-shadow: 0 8px 25px ${isDark ? 'rgba(0, 0, 0, 0.5)' : 'rgba(0, 0, 0, 0.08)'},
                                     0 4px 10px ${isDark ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.12)'};
                          backdrop-filter: blur(12px);
                          font-family: 'almarai', sans-serif;
                          direction: rtl;
                          min-width: 280px;
                          border: 2px solid ${performanceColor};
                        ">
                          <div style="
                            color: ${isDark ? '#F9FAFB' : '#111827'};
                            font-size: 15px;
                            font-weight: 700;
                            margin-bottom: 12px;
                            text-align: center;
                            padding: 8px;
                            background: ${isDark ? 'rgba(55, 65, 81, 0.3)' : 'rgba(243, 244, 246, 0.5)'};
                            border-radius: 6px;
                          ">
                            ${formatChartDateFull(period, selectedPeriod)}
                          </div>

                          <!-- المستهدف -->
                          <div style="
                            color: ${isDark ? '#60A5FA' : '#3B82F6'};
                            font-size: 13px;
                            font-weight: 600;
                            margin-bottom: 8px;
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          ">
                            <div style="display: flex; align-items: center;">
                              <div style="
                                width: 8px;
                                height: 8px;
                                border-radius: 50%;
                                background: ${isDark ? '#60A5FA' : '#3B82F6'};
                                margin-left: 6px;
                              "></div>
                              المستهدف
                            </div>
                            <span style="font-weight: 700;">${formatCurrency(targetCollection)}</span>
                          </div>

                          <!-- المحصل فعلياً -->
                          <div style="
                            color: ${isDark ? '#34D399' : '#10B981'};
                            font-size: 13px;
                            font-weight: 600;
                            margin-bottom: 8px;
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          ">
                            <div style="display: flex; align-items: center;">
                              <div style="
                                width: 8px;
                                height: 8px;
                                border-radius: 50%;
                                background: ${isDark ? '#34D399' : '#10B981'};
                                margin-left: 6px;
                              "></div>
                              المحصل فعلياً
                            </div>
                            <span style="font-weight: 700;">${formatCurrency(actualCollection)}</span>
                          </div>

                          <!-- خط فاصل -->
                          <div style="
                            height: 1px;
                            background: ${isDark ? 'rgba(55, 65, 81, 0.5)' : 'rgba(229, 231, 235, 0.5)'};
                            margin: 10px 0;
                          "></div>

                          <!-- الانحراف -->
                          <div style="
                            color: ${isDark ? '#D1D5DB' : '#4B5563'};
                            font-size: 12px;
                            margin-bottom: 8px;
                            display: flex;
                            justify-content: space-between;
                          ">
                            <span>الانحراف:</span>
                            <span style="font-weight: 600; color: ${variance >= 0 ? (isDark ? '#34D399' : '#10B981') : (isDark ? '#F87171' : '#DC2626')};">
                              ${formatCurrency(Math.abs(variance))} ${variance >= 0 ? '↑' : '↓'}
                            </span>
                          </div>
                          <div style="
                            color: ${isDark ? '#D1D5DB' : '#4B5563'};
                            font-size: 12px;
                            margin-bottom: 8px;
                            display: flex;
                            justify-content: space-between;
                          ">
                            <span>تقييم الأداء:</span>
                            <span style="font-weight: 600; color: ${performanceColor};">
                              ${performanceStatus}
                            </span>
                          </div>
                          <div style="
                            text-align: center;
                            padding: 8px;
                            background: ${efficiency >= 100 ? (isDark ? 'rgba(52, 211, 153, 0.1)' : 'rgba(16, 185, 129, 0.1)') : efficiency >= 75 ? (isDark ? 'rgba(251, 191, 36, 0.1)' : 'rgba(217, 119, 6, 0.1)') : (isDark ? 'rgba(248, 113, 113, 0.1)' : 'rgba(220, 38, 38, 0.1)')};
                            border-radius: 6px;
                            border: 1px solid ${efficiency >= 100 ? (isDark ? '#34D399' : '#10B981') : efficiency >= 75 ? (isDark ? '#FBBF24' : '#D97706') : (isDark ? '#F87171' : '#DC2626')};
                          ">
                            <div style="
                              color: ${efficiency >= 100 ? (isDark ? '#34D399' : '#10B981') : efficiency >= 75 ? (isDark ? '#FBBF24' : '#D97706') : (isDark ? '#F87171' : '#DC2626')};
                              font-size: 14px;
                              font-weight: 800;
                            ">
                              كفاءة التحصيل: ${efficiency.toFixed(1)}%
                            </div>
                          </div>
                        </div>
                      `;
                    }
                  },
                  legend: {
                    show: true,
                    position: 'top',
                    horizontalAlign: 'center',
                    fontSize: '13px',
                    fontFamily: 'almarai, sans-serif',
                    fontWeight: 600,
                    labels: {
                      colors: isDark ? '#E5E7EB' : '#374151'
                    },
                    markers: {
                      size: 8,
                      shape: 'square'
                    },
                    itemMargin: {
                      horizontal: 20,
                      vertical: 5
                    }
                  },
                  grid: {
                    borderColor: isDark ? '#374151' : '#E5E7EB',
                    strokeDashArray: 3,
                    xaxis: {
                      lines: {
                        show: false
                      }
                    }
                  }
                }}
                series={[
                  {
                    name: 'المستهدف',
                    data: collectionEfficiency.map(item => item.targetCollection)
                  },
                  {
                    name: 'المحصل فعلياً',
                    data: collectionEfficiency.map(item => item.actualCollection)
                  }
                ]}
                type="bar"
                height="100%"
              />
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              لا توجد بيانات كفاءة التحصيل متاحة
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="touch-container">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl mb-6 overflow-hidden card-subtle-border">
        <div className="bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <button
                onClick={() => navigate('/')}
                className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-xl p-3 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 ease-in-out shadow-lg hover:shadow-xl flex-shrink-0 border-2 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                title="العودة للرئيسية"
              >
                <FaArrowLeft className="text-sm" />
              </button>
              <div className="mr-3 sm:mr-4 min-w-0 flex-1">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FaChartLine className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">التقارير</span>
                </h1>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block">
                  تقارير وإحصائيات شاملة للنظام
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2 sm:gap-3 flex-wrap lg:flex-nowrap">
              <button
                onClick={handleRefresh}
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-3 rounded-xl hover:bg-white/50 dark:hover:bg-gray-700/50 transition-all duration-200 ease-in-out backdrop-blur-sm border-2 border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-500 shadow-lg hover:shadow-xl"
                title="تحديث البيانات"
              >
                <FaSync className="text-sm" />
              </button>
              <button
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-3 rounded-xl hover:bg-white/50 dark:hover:bg-gray-700/50 transition-all duration-200 ease-in-out backdrop-blur-sm border-2 border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-500 shadow-lg hover:shadow-xl"
                title="طباعة التقرير"
              >
                <FaPrint className="text-sm" />
              </button>
              <button
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-3 rounded-xl hover:bg-white/50 dark:hover:bg-gray-700/50 transition-all duration-200 ease-in-out backdrop-blur-sm border-2 border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-500 shadow-lg hover:shadow-xl"
                title="تصدير التقرير"
              >
                <FaDownload className="text-sm" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Date and User Info Bar */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-6 card-subtle-border">
        <div className="text-secondary-600 dark:text-secondary-300 text-sm text-center">
          <span className="font-medium">
            <FormattedDate date={new Date()} className="font-medium" />
          </span>
          <span className="mx-2">|</span>
          <span>{currentUser?.role === 'admin' ? 'جميع المستخدمين' : 'مبيعاتك فقط'}</span>
        </div>
      </div>

      {/* Report Type Tabs */}
      <div className="touch-card bg-white dark:bg-gray-800 rounded-xl p-4 mb-6">
        <div className="flex border-b border-gray-200 dark:border-gray-700 mb-4 overflow-x-auto pb-1 custom-scrollbar-thin">
          <button
            onClick={() => handleReportTypeChange('sales')}
            className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
              selectedReportType === 'sales'
                ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            <FaChartLine className="ml-2" />
            <span>المبيعات</span>
          </button>
          <button
            onClick={() => handleReportTypeChange('products')}
            className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
              selectedReportType === 'products'
                ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            <FaChartPie className="ml-2" />
            <span>المنتجات</span>
          </button>
          <button
            onClick={() => handleReportTypeChange('inventory')}
            className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
              selectedReportType === 'inventory'
                ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            <FaChartBar className="ml-2" />
            <span>المخزون</span>
          </button>
          {/* تبويبة مبيعات المستخدمين - للمديرين فقط */}
          {currentUser?.role === 'admin' && (
            <button
              onClick={() => handleReportTypeChange('daily-users')}
              className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
                selectedReportType === 'daily-users'
                  ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <FaUsers className="ml-2" />
              <span>مبيعات المستخدمين</span>
            </button>
          )}
          {/* تبويبة المديونية - للمديرين فقط */}
          {currentUser?.role === 'admin' && (
            <button
              onClick={() => handleReportTypeChange('debts')}
              className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
                selectedReportType === 'debts'
                  ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <FaMoneyBillWave className="ml-2" />
              <span>المديونية</span>
            </button>
          )}
          {/* تبويبة النظام - للمديرين فقط */}
          {currentUser?.role === 'admin' && (
            <button
              onClick={() => handleReportTypeChange('system')}
              className={`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
                selectedReportType === 'system-actions' || selectedReportType === 'system'
                  ? 'border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <FaServer className="ml-2" />
              <span>النظام</span>
            </button>
          )}
        </div>





        {isLoading ? (
          <div className="flex justify-center items-center h-64 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 dark:border-primary-400"></div>
          </div>
        ) : (
          <>
            {selectedReportType === 'sales' && renderSalesChart()}
            {selectedReportType === 'products' && renderProductsChart()}
            {selectedReportType === 'inventory' && renderInventoryChart()}
            {selectedReportType === 'daily-users' && renderDailyUserSales()}
            {selectedReportType === 'debts' && renderDebtsReports()}
            {selectedReportType === 'system' && renderSystemStats()}
            {selectedReportType === 'customers' && (
              <div className="text-center py-12 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
                <div className="bg-secondary-100 dark:bg-secondary-900/50 text-secondary-700 dark:text-secondary-300 p-4 rounded-full mb-3 inline-block">
                  <FaChartBar className="text-3xl" />
                </div>
                <h3 className="text-xl font-bold mb-2 text-secondary-900 dark:text-secondary-100">تقرير العملاء قيد التطوير</h3>
                <p className="text-secondary-600 dark:text-secondary-400">سيتم إضافة هذه الميزة قريباً</p>
              </div>
            )}
          </>
        )}
      </div>

      {/* النوافذ المخصصة */}

      {/* نافذة تأكيد الحذف */}
      <DeleteConfirmModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, backupName: '', isLoading: false })}
        onConfirm={confirmDeleteBackup}
        title="تأكيد حذف النسخة الاحتياطية"
        message="هل أنت متأكد من حذف النسخة الاحتياطية التالية؟"
        itemName={deleteModal.backupName}
        isLoading={deleteModal.isLoading}
      />

      {/* نافذة تأكيد الاستعادة */}
      <RestoreConfirmModal
        isOpen={restoreModal.isOpen}
        onClose={() => setRestoreModal({ isOpen: false, backupInfo: null, isLoading: false })}
        onConfirm={confirmRestoreBackup}
        backupInfo={restoreModal.backupInfo}
        isLoading={restoreModal.isLoading}
      />

      {/* نافذة النجاح */}
      <SuccessModal
        isOpen={successModal.isOpen}
        onClose={() => setSuccessModal({ isOpen: false, title: '', message: '', details: null, autoClose: false })}
        title={successModal.title}
        message={successModal.message}
        details={successModal.details}
        autoClose={successModal.autoClose}
        autoCloseDelay={3000}
      />

      {/* نافذة التأكيد العامة */}
      <ConfirmModal
        isOpen={confirmModal.isOpen}
        onClose={() => setConfirmModal({ isOpen: false, type: 'backup', title: '', message: '', description: '', isLoading: false })}
        onConfirm={handleConfirmAction}
        title={confirmModal.title}
        message={confirmModal.message}
        description={confirmModal.description}
        type={confirmModal.type}
        isLoading={confirmModal.isLoading}
      />

      {/* نافذة عرض جميع النسخ الاحتياطية */}
      <AllBackupsModal
        isOpen={allBackupsModal}
        onClose={() => setAllBackupsModal(false)}
        onRestore={openRestoreModal}
        onDelete={openDeleteModal}
      />



    </div>
  );
};

export default Reports;
