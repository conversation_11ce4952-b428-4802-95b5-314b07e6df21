import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import {
  FaPlus,
  FaMoneyBillWave,
  FaUser,
  FaCalendarAlt,
  FaCheck,
  FaTimes,
  FaEdit,
  FaTrash,
  FaSearch,
  FaCreditCard,
  FaArrowLeft,
  FaSync,
  FaFilter,
  FaChevronLeft,
  FaChevronRight,
  FaChevronUp,
  FaChevronDown,
  FaFileInvoiceDollar,
  FaCheckCircle,
  FaInfoCircle,
  FaClock
} from 'react-icons/fa';
import { useAuthStore } from '../stores/authStore';
import Modal from '../components/Modal';
import DeleteConfirmModal from '../components/DeleteConfirmModal';
import SuccessModal from '../components/SuccessModal';
import { NumberInput, TextArea, SelectInput, DatePicker } from '../components/inputs';
import { formatDateTime } from '../services/dateTimeService';
import FormattedCurrency from '../components/FormattedCurrency';
import { FormattedDateTime } from '../components/FormattedDateTime';
import { numberFormattingService } from '../services/numberFormattingService';

interface Customer {
  id: number;
  name: string;
  phone?: string;
  email?: string;
}

interface Debt {
  id: number;
  customer_id: number;
  sale_id?: number;
  amount: number;
  remaining_amount: number;
  description?: string;
  is_paid: boolean;
  payment_status: string; // 'unpaid', 'partial', 'paid'
  created_at: string;
  updated_at?: string;
  customer?: Customer;
  payments: Payment[];
  sale?: {
    id: number;
    total_amount: number;
    tax_amount: number;
    discount_amount: number;
    amount_paid: number;
    payment_status: string;
  };
}

interface Payment {
  id: number;
  debt_id: number;
  amount: number;
  payment_method: string;
  notes?: string;
  created_at: string;
}

interface DebtFormData {
  customer_id: number;
  amount: number;
  description: string;
}

interface PaymentFormData {
  amount: number;
  payment_method: string;
  notes: string;
}

interface MultiplePaymentFormData {
  total_amount: number;
  payment_method: string;
  notes: string;
}

// دالة مساعدة لتنسيق الأرقام بشكل صحيح
const formatNumberSafely = (num: number, decimalPlaces: number = 2): number => {
  if (isNaN(num) || !isFinite(num)) {
    return 0;
  }

  // التعامل مع الأرقام العلمية والأرقام الطويلة
  const multiplier = Math.pow(10, decimalPlaces);
  return Math.round(num * multiplier) / multiplier;
};

const Debts: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const [searchParams] = useSearchParams();
  const customerId = searchParams.get('customer_id');

  // Hook للتنسيق المتزامن
  const [formatSettings, setFormatSettings] = useState<any>(null);

  useEffect(() => {
    const loadSettings = async () => {
      try {
        const settings = await numberFormattingService.getCurrentSettings();
        setFormatSettings(settings);
      } catch (error) {
        console.error('Error loading format settings:', error);
      }
    };
    loadSettings();
  }, []);

  // دالة تنسيق متزامنة
  const formatCurrency = (amount: number): string => {
    if (!formatSettings) {
      return `${amount.toFixed(2)} د.ل`; // fallback
    }

    const fixedAmount = formatSettings.showDecimals ?
      amount.toFixed(formatSettings.decimalPlaces) :
      Math.round(amount).toString();

    // تطبيق الفواصل
    let formattedNumber = fixedAmount;
    if (formatSettings.separatorType !== 'none') {
      const parts = fixedAmount.split('.');
      const integerPart = parts[0];
      const decimalPart = parts[1];
      const separator = formatSettings.separatorType === 'comma' ? ',' : ' ';
      const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, separator);
      formattedNumber = decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger;
    }

    // إضافة رمز العملة
    if (formatSettings.symbolPosition === 'before') {
      return `${formatSettings.currencySymbol} ${formattedNumber}`;
    } else {
      return `${formattedNumber} ${formatSettings.currencySymbol}`;
    }
  };

  const [debts, setDebts] = useState<Debt[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [paymentStatus, setPaymentStatus] = useState('all'); // 'all', 'paid', 'unpaid'
  const [selectedCustomerId, setSelectedCustomerId] = useState<number | null>(
    customerId ? parseInt(customerId) : null
  );

  // Modals
  const [showDebtModal, setShowDebtModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showEditPaymentModal, setShowEditPaymentModal] = useState(false);
  const [showMultiplePaymentModal, setShowMultiplePaymentModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [expandedDebt, setExpandedDebt] = useState<number | null>(null);

  // Form data
  const [selectedDebt, setSelectedDebt] = useState<Debt | null>(null);
  const [selectedPayment, setSelectedPayment] = useState<any | null>(null);
  const [paymentToDelete, setPaymentToDelete] = useState<any | null>(null);
  const [debtToDelete, setDebtToDelete] = useState<Debt | null>(null);
  const [debtFormData, setDebtFormData] = useState<DebtFormData>({
    customer_id: 0,
    amount: 0,
    description: ''
  });
  const [paymentFormData, setPaymentFormData] = useState<PaymentFormData>({
    amount: 0,
    payment_method: 'cash',
    notes: ''
  });
  const [multiplePaymentFormData, setMultiplePaymentFormData] = useState<MultiplePaymentFormData>({
    total_amount: 0,
    payment_method: 'cash',
    notes: ''
  });
  const [formErrors, setFormErrors] = useState<any>({});

  // New state variables for unified design
  const [showFilters, setShowFilters] = useState(false);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // New filter states
  const [debtType, setDebtType] = useState<string>('all'); // 'all', 'invoice', 'standalone'
  const [minAmount, setMinAmount] = useState<string>('');
  const [maxAmount, setMaxAmount] = useState<string>('');
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');

  // Filter statistics
  const [filterStats, setFilterStats] = useState({
    totalFiltered: 0,
    unpaidFiltered: 0,
    partialFiltered: 0,
    paidFiltered: 0,
    totalAmountFiltered: 0,
    unpaidAmountFiltered: 0,
    paidAmountFiltered: 0,
    isFiltered: false
  });


  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    pages: 1
  });
  const [stats, setStats] = useState({
    totalDebts: 0,
    unpaidDebts: 0,
    totalAmount: 0,
    totalInvoiceAmount: 0,
    totalPaidAmount: 0,
    unpaidAmount: 0
  });

  useEffect(() => {
    // Check if user is logged in
    if (!user) {
      window.location.href = '/login';
      return;
    }
    fetchCustomers();
    fetchDebts();
    fetchStats();
  }, [user]); // Removed selectedCustomerId and showUnpaidOnly from dependencies

  // Update stats when selectedCustomerId changes
  useEffect(() => {
    if (user) {
      fetchStats();
    }
  }, [selectedCustomerId, user]);

  // Remove auto-apply for amount filters - they should only apply when user clicks "Apply Filters"
  // useEffect(() => {
  //   const timer = setTimeout(() => {
  //     if (minAmount || maxAmount) {
  //       setCurrentPage(1);
  //       fetchDebts(1, itemsPerPage);
  //     }
  //   }, 800); // 800ms debounce

  //   return () => clearTimeout(timer);
  // }, [minAmount, maxAmount]);

  // Helper function to make authenticated requests with token refresh
  const makeAuthenticatedRequest = async (url: string, options: RequestInit = {}) => {
    const { token, refreshToken } = useAuthStore.getState();

    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    // If token expired, try to refresh
    if (response.status === 401 && refreshToken) {
      try {
        const apiUrl = window.location.hostname === 'localhost' ? 'http://localhost:8002' : `http://${window.location.hostname}:8002`;
        const refreshResponse = await fetch(`${apiUrl}/api/auth/refresh`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ refresh_token: refreshToken }),
        });

        if (refreshResponse.ok) {
          const data = await refreshResponse.json();
          // Update tokens in store
          useAuthStore.setState({
            token: data.access_token,
            refreshToken: data.refresh_token || refreshToken,
            isAuthenticated: true,
            error: null
          });

          // Retry original request with new token
          return fetch(url, {
            ...options,
            headers: {
              'Authorization': `Bearer ${data.access_token}`,
              'Content-Type': 'application/json',
              ...options.headers,
            },
          });
        } else {
          // Refresh failed, redirect to login
          useAuthStore.getState().logout();
          window.location.href = '/login';
          throw new Error('Authentication failed');
        }
      } catch (error) {
        useAuthStore.getState().logout();
        window.location.href = '/login';
        throw error;
      }
    }

    return response;
  };

  const fetchStats = async () => {
    try {
      const apiUrl = window.location.hostname === 'localhost' ? 'http://localhost:8002' : `http://${window.location.hostname}:8002`;
      let url = `${apiUrl}/api/debts/stats`;

      // Apply customer filter to stats only when viewing a specific customer's debts
      if (selectedCustomerId) {
        url += `?customer_id=${selectedCustomerId}`;
      }

      const response = await makeAuthenticatedRequest(url);

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      // Handle error silently
      setStats({
        totalDebts: 0,
        unpaidDebts: 0,
        totalAmount: 0,
        totalInvoiceAmount: 0,
        totalPaidAmount: 0,
        unpaidAmount: 0
      });
    }
  };

  const fetchCustomers = async () => {
    try {
      const apiUrl = window.location.hostname === 'localhost' ? 'http://localhost:8002' : `http://${window.location.hostname}:8002`;
      const response = await makeAuthenticatedRequest(`${apiUrl}/api/customers?active_only=true`);

      if (response.ok) {
        const data = await response.json();
        setCustomers(data);
      }
    } catch (error) {
      // Handle error silently
    }
  };

  const fetchDebts = async (page = currentPage, limit = itemsPerPage) => {
    try {
      setLoading(true);
      const apiUrl = window.location.hostname === 'localhost' ? 'http://localhost:8002' : `http://${window.location.hostname}:8002`;
      let url = `${apiUrl}/api/debts?skip=${(page - 1) * limit}&limit=${limit}`;

      // Add payment status filter
      if (paymentStatus !== 'all') {
        url += `&payment_status=${paymentStatus}`;
      }

      if (selectedCustomerId) {
        url += `&customer_id=${selectedCustomerId}`;
      }

      if (searchTerm) {
        url += `&search=${encodeURIComponent(searchTerm)}`;
      }

      // Add debt type filter
      if (debtType === 'invoice') {
        url += `&invoice_only=true`;
      } else if (debtType === 'standalone') {
        url += `&standalone_only=true`;
      }

      // Add amount range filter
      if (minAmount) {
        url += `&min_amount=${encodeURIComponent(minAmount)}`;
      }
      if (maxAmount) {
        url += `&max_amount=${encodeURIComponent(maxAmount)}`;
      }

      // Add date range filter
      if (startDate) {
        url += `&start_date=${encodeURIComponent(startDate)}`;
      }
      if (endDate) {
        url += `&end_date=${encodeURIComponent(endDate)}`;
      }

      const response = await makeAuthenticatedRequest(url);

      if (response.ok) {
        const data = await response.json();

        // Check if response has pagination info
        if (data && typeof data === 'object' && data.items && data.total !== undefined) {
          // Backend returns paginated response with { items: [], total: number, page: number, limit: number }
          const paginationInfo = {
            total: data.total,
            page: data.page || page,
            limit: data.limit || limit,
            pages: Math.ceil(data.total / (data.limit || limit))
          };
          setDebts(data.items);
          setPagination(paginationInfo);
        } else if (response.headers.get('x-total-count')) {
          // Backend supports pagination via headers
          const total = parseInt(response.headers.get('x-total-count') || '0');
          const paginationInfo = {
            total: total,
            page: page,
            limit: limit,
            pages: Math.ceil(total / limit)
          };
          setDebts(Array.isArray(data) ? data : []);
          setPagination(paginationInfo);
        } else {
          // No pagination info - treat as all data
          const allDebts = Array.isArray(data) ? data : [];
          const paginationInfo = {
            total: allDebts.length,
            page: 1,
            limit: allDebts.length,
            pages: 1
          };
          setDebts(allDebts);
          setPagination(paginationInfo);
        }
        setCurrentPage(page);
      }
    } catch (error) {
      // Handle error silently
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setCurrentPage(1); // Reset to first page when searching
    fetchDebts(1, itemsPerPage);
  };

  const handleApplyFilters = async () => {
    setCurrentPage(1); // Reset to first page when applying filters

    // Calculate filter statistics
    await calculateFilterStats();

    fetchDebts(1, itemsPerPage);
  };

  const calculateFilterStats = async () => {
    try {
      const apiUrl = window.location.hostname === 'localhost' ? 'http://localhost:8002' : `http://${window.location.hostname}:8002`;
      let url = `${apiUrl}/api/debts/filter-stats?`;

      // Add all current filters to get statistics
      const params = new URLSearchParams();

      if (paymentStatus !== 'all') {
        params.append('payment_status', paymentStatus);
      }
      if (selectedCustomerId) {
        params.append('customer_id', selectedCustomerId.toString());
      }
      if (searchTerm) {
        params.append('search', searchTerm);
      }
      if (debtType === 'invoice') {
        params.append('invoice_only', 'true');
      } else if (debtType === 'standalone') {
        params.append('standalone_only', 'true');
      }
      if (minAmount) {
        params.append('min_amount', minAmount);
      }
      if (maxAmount) {
        params.append('max_amount', maxAmount);
      }
      if (startDate) {
        params.append('start_date', startDate);
      }
      if (endDate) {
        params.append('end_date', endDate);
      }

      url += params.toString();

      const response = await makeAuthenticatedRequest(url);

      if (response.ok) {
        const stats = await response.json();
        setFilterStats({
          ...stats,
          isFiltered: params.toString().length > 0
        });
      }
    } catch (error) {
      console.error('Error calculating filter stats:', error);
    }
  };

  const handleResetFilters = async () => {
    // Reset all filter states
    setSearchTerm('');
    setSelectedCustomerId(null);
    setPaymentStatus('all');
    setDebtType('all');
    setMinAmount('');
    setMaxAmount('');
    setStartDate('');
    setEndDate('');
    setItemsPerPage(10);
    setCurrentPage(1);

    // Reset filter statistics
    setFilterStats({
      totalFiltered: 0,
      unpaidFiltered: 0,
      partialFiltered: 0,
      paidFiltered: 0,
      totalAmountFiltered: 0,
      unpaidAmountFiltered: 0,
      paidAmountFiltered: 0,
      isFiltered: false
    });

    // Fetch data with reset filters directly
    try {
      setLoading(true);
      const apiUrl = window.location.hostname === 'localhost' ? 'http://localhost:8002' : `http://${window.location.hostname}:8002`;
      let url = `${apiUrl}/api/debts?skip=0&limit=10`;

      const response = await makeAuthenticatedRequest(url);

      if (response.ok) {
        const data = await response.json();

        // Check if response has pagination info
        if (data && typeof data === 'object' && data.items && data.total !== undefined) {
          // Backend returns paginated response with { items: [], total: number, page: number, limit: number }
          const paginationInfo = {
            total: data.total,
            page: data.page || 1,
            limit: data.limit || 10,
            pages: Math.ceil(data.total / (data.limit || 10))
          };
          setDebts(data.items);
          setPagination(paginationInfo);
        } else if (response.headers.get('x-total-count')) {
          // Backend supports pagination via headers
          const total = parseInt(response.headers.get('x-total-count') || '0');
          const paginationInfo = {
            total: total,
            page: 1,
            limit: 10,
            pages: Math.ceil(total / 10)
          };
          setDebts(Array.isArray(data) ? data : []);
          setPagination(paginationInfo);
        } else {
          // No pagination info - treat as all data
          const allDebts = Array.isArray(data) ? data : [];
          const paginationInfo = {
            total: allDebts.length,
            page: 1,
            limit: allDebts.length,
            pages: 1
          };
          setDebts(allDebts);
          setPagination(paginationInfo);
        }
        setCurrentPage(1);
      }
    } catch (error) {
      // Handle error silently
    } finally {
      setLoading(false);
    }
  };

  const handleAddDebt = () => {
    setDebtFormData({
      customer_id: selectedCustomerId || 0,
      amount: 0,
      description: ''
    });
    setFormErrors({});
    setShowDebtModal(true);
  };



  const handleAddPayment = (debt: Debt) => {
    setSelectedDebt(debt);
    setSelectedPayment(null);

    // تنسيق المبلغ بشكل صحيح
    const decimalPlaces = formatSettings?.decimalPlaces || 2;
    const formattedAmount = formatNumberSafely(debt.remaining_amount, decimalPlaces);

    setPaymentFormData({
      amount: formattedAmount,
      payment_method: 'cash',
      notes: ''
    });
    setFormErrors({});
    setShowPaymentModal(true);
  };

  const handleEditPayment = (debt: Debt, payment: any) => {
    setSelectedDebt(debt);
    setSelectedPayment(payment);

    // تنسيق المبلغ بشكل صحيح
    const decimalPlaces = formatSettings?.decimalPlaces || 2;
    const formattedAmount = formatNumberSafely(payment.amount, decimalPlaces);

    setPaymentFormData({
      amount: formattedAmount,
      payment_method: payment.payment_method,
      notes: payment.notes || ''
    });
    setFormErrors({});
    setShowEditPaymentModal(true);
  };

  const toggleDebtExpansion = (debtId: number) => {
    setExpandedDebt(expandedDebt === debtId ? null : debtId);
  };

  const handleDeletePayment = (debt: Debt, payment: any) => {
    setSelectedDebt(debt);
    setPaymentToDelete(payment);
    setShowDeleteModal(true);
  };

  const handleDeleteDebt = (debt: Debt) => {
    setSelectedDebt(null);
    setPaymentToDelete(null);
    setDebtToDelete(debt);
    setShowDeleteModal(true);
  };

  const validateDebtForm = (): boolean => {
    const errors: any = {};

    if (!debtFormData.customer_id) {
      errors.customer_id = 'يجب اختيار العميل';
    }

    if (!debtFormData.amount || debtFormData.amount <= 0) {
      errors.amount = 'يجب إدخال مبلغ صحيح';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validatePaymentForm = (): boolean => {
    const errors: any = {};

    if (!paymentFormData.amount || paymentFormData.amount <= 0) {
      errors.amount = 'يجب إدخال مبلغ صحيح';
    }

    if (selectedDebt && paymentFormData.amount > selectedDebt.remaining_amount) {
      errors.amount = 'المبلغ أكبر من المبلغ المتبقي';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validateMultiplePaymentForm = (): boolean => {
    const errors: any = {};

    if (!multiplePaymentFormData.total_amount || multiplePaymentFormData.total_amount <= 0) {
      errors.total_amount = 'يجب إدخال مبلغ صحيح';
    }

    if (multiplePaymentFormData.total_amount > stats.unpaidAmount) {
      errors.total_amount = `المبلغ أكبر من إجمالي المبلغ المتبقي (${formatCurrency(stats.unpaidAmount)})`;
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleMultiplePayment = () => {
    if (!selectedCustomerId) {
      return;
    }

    setMultiplePaymentFormData({
      total_amount: stats.unpaidAmount,
      payment_method: 'cash',
      notes: ''
    });
    setFormErrors({});
    setShowMultiplePaymentModal(true);
  };

  const handleMultiplePaymentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateMultiplePaymentForm() || !selectedCustomerId) {
      return;
    }

    try {
      const apiUrl = window.location.hostname === 'localhost' ? 'http://localhost:8002' : `http://${window.location.hostname}:8002`;
      const url = `${apiUrl}/api/debts/multiple-payment`;

      const response = await makeAuthenticatedRequest(url, {
        method: 'POST',
        body: JSON.stringify({
          customer_id: selectedCustomerId,
          ...multiplePaymentFormData
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setShowMultiplePaymentModal(false);
        setSuccessMessage(`تم دفع ${formatCurrency(result.total_amount_paid)} بنجاح. تم تحديث ${result.debts_updated} مديونية، منها ${result.debts_fully_paid} تم سدادها بالكامل.`);
        setShowSuccessModal(true);
        fetchDebts();
        fetchStats();
      } else {
        const errorData = await response.json();
        setFormErrors({ general: errorData.detail || 'حدث خطأ أثناء معالجة الدفع' });
      }
    } catch (error) {
      setFormErrors({ general: 'حدث خطأ أثناء معالجة الدفع' });
    }
  };

  const handleDebtSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateDebtForm()) {
      return;
    }

    try {
      const apiUrl = window.location.hostname === 'localhost' ? 'http://localhost:8002' : `http://${window.location.hostname}:8002`;
      const url = `${apiUrl}/api/debts`;

      const response = await makeAuthenticatedRequest(url, {
        method: 'POST',
        body: JSON.stringify(debtFormData),
      });

      if (response.ok) {
        setShowDebtModal(false);
        setSuccessMessage('تم إضافة المديونية بنجاح');
        setShowSuccessModal(true);
        fetchDebts();
        fetchStats();
      } else {
        // Handle error silently
      }
    } catch (error) {
      // Handle error silently
    }
  };

  const handlePaymentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validatePaymentForm() || !selectedDebt) {
      return;
    }

    try {
      const apiUrl = window.location.hostname === 'localhost' ? 'http://localhost:8002' : `http://${window.location.hostname}:8002`;
      const url = selectedPayment
        ? `${apiUrl}/api/debts/${selectedDebt.id}/payments/${selectedPayment.id}`
        : `${apiUrl}/api/debts/${selectedDebt.id}/payments`;

      const method = selectedPayment ? 'PUT' : 'POST';
      const body = selectedPayment
        ? JSON.stringify(paymentFormData)
        : JSON.stringify({
            debt_id: selectedDebt.id,
            ...paymentFormData
          });

      const response = await makeAuthenticatedRequest(url, {
        method,
        body,
      });

      if (response.ok) {
        setShowPaymentModal(false);
        setShowEditPaymentModal(false);
        setSuccessMessage(selectedPayment ? 'تم تحديث الدفعة بنجاح' : 'تم تسجيل الدفعة بنجاح');
        setShowSuccessModal(true);
        fetchDebts();
        fetchStats();
      } else {
        // Handle error silently
      }
    } catch (error) {
      // Handle error silently
    }
  };

  const confirmDelete = async () => {
    if (!debtToDelete && !paymentToDelete) return;

    try {
      const apiUrl = window.location.hostname === 'localhost' ? 'http://localhost:8002' : `http://${window.location.hostname}:8002`;

      let url: string;
      let successMessage: string;

      if (paymentToDelete && selectedDebt) {
        // Delete payment
        url = `${apiUrl}/api/debts/${selectedDebt.id}/payments/${paymentToDelete.id}`;
        successMessage = 'تم حذف الدفعة بنجاح';
      } else if (debtToDelete) {
        // Delete debt
        url = `${apiUrl}/api/debts/${debtToDelete.id}`;
        successMessage = 'تم حذف المديونية بنجاح';
      } else {
        return;
      }

      const response = await makeAuthenticatedRequest(url, {
        method: 'DELETE',
      });

      if (response.ok) {
        setShowDeleteModal(false);
        setSuccessMessage(successMessage);
        setShowSuccessModal(true);
        fetchDebts();
        fetchStats();

        // Reset states
        setPaymentToDelete(null);
        setDebtToDelete(null);
        setSelectedDebt(null);
      } else {
        // Handle error silently
      }
    } catch (error) {
      // Handle error silently
    }
  };

  // Remove client-side filtering since we're doing server-side filtering
  const filteredDebts = debts;

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl mb-6 overflow-hidden card-subtle-border">
        <div className="bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <button
                onClick={() => navigate('/')}
                className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-xl p-3 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 ease-in-out shadow-lg hover:shadow-xl flex-shrink-0 border-2 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                title="العودة للرئيسية"
              >
                <FaArrowLeft className="text-sm" />
              </button>
              <div className="mr-3 sm:mr-4 min-w-0 flex-1">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FaMoneyBillWave className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">إدارة المديونية</span>
                </h1>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block">
                  إدارة وتتبع مديونيات العملاء
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2 sm:gap-3 flex-wrap lg:flex-nowrap">
              <button
                onClick={() => fetchDebts(currentPage, itemsPerPage)}
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-3 rounded-xl hover:bg-white/50 dark:hover:bg-gray-700/50 transition-all duration-200 ease-in-out backdrop-blur-sm border-2 border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-500 shadow-lg hover:shadow-xl"
                title="تحديث البيانات"
              >
                <FaSync className={`text-sm ${loading ? 'animate-spin' : ''}`} />
              </button>
              <button
                onClick={handleAddDebt}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium min-w-[140px] focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl"
              >
                <FaPlus className="ml-2 text-sm" />
                <span className="hidden sm:inline lg:inline">إضافة مديونية جديدة</span>
                <span className="sm:hidden lg:hidden">إضافة</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Bar */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-6 card-subtle-border">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {/* Total Debts */}
          <div className="flex items-center gap-3 p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-100 dark:border-primary-800/30">
            <div className="bg-primary-100 dark:bg-primary-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaMoneyBillWave className="text-primary-600 dark:text-primary-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-primary-700 dark:text-primary-300 mb-1">إجمالي المديونيات</div>
              <div className="text-xl font-bold text-primary-600 dark:text-primary-400">{stats.totalDebts}</div>
            </div>
          </div>

          {/* Unpaid Debts */}
          <div className="flex items-center gap-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-100 dark:border-red-800/30">
            <div className="bg-red-100 dark:bg-red-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaTimes className="text-red-600 dark:text-red-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-red-700 dark:text-red-300 mb-1">غير مدفوعة</div>
              <div className="text-xl font-bold text-red-600 dark:text-red-400">{stats.unpaidDebts}</div>
            </div>
          </div>

          {/* Total Invoice Amount */}
          <div className="flex items-center gap-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-100 dark:border-purple-800/30">
            <div className="bg-purple-100 dark:bg-purple-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaFileInvoiceDollar className="text-purple-600 dark:text-purple-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-purple-700 dark:text-purple-300 mb-1">إجمالي سعر الفواتير</div>
              <div className="text-lg font-bold text-purple-600 dark:text-purple-400 truncate">
                <FormattedCurrency amount={stats.totalInvoiceAmount || 0} />
              </div>
              <div className="text-xs text-purple-500 dark:text-purple-400">قيمة الفواتير</div>
            </div>
          </div>

          {/* Total Paid Amount */}
          <div className="flex items-center gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-100 dark:border-green-800/30">
            <div className="bg-green-100 dark:bg-green-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaMoneyBillWave className="text-green-600 dark:text-green-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-green-700 dark:text-green-300 mb-1">إجمالي المبلغ المدفوع</div>
              <div className="text-lg font-bold text-green-600 dark:text-green-400 truncate">
                <FormattedCurrency amount={stats.totalPaidAmount || 0} />
              </div>
              <div className="text-xs text-green-500 dark:text-green-400">المبلغ المحصل</div>
            </div>
          </div>

          {/* Unpaid Amount */}
          <div className="flex items-center gap-3 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-100 dark:border-orange-800/30">
            <div className="bg-orange-100 dark:bg-orange-900/40 p-2.5 rounded-lg flex-shrink-0">
              <FaMoneyBillWave className="text-orange-600 dark:text-orange-400 text-lg" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-orange-700 dark:text-orange-300 mb-1">المبلغ غير المدفوع</div>
              <div className="text-lg font-bold text-orange-600 dark:text-orange-400 truncate">
                <FormattedCurrency amount={stats.unpaidAmount} />
              </div>
              <div className="text-xs text-orange-500 dark:text-orange-400">المبلغ المستحق</div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 mb-6 card-subtle-border">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" />
              <input
                type="text"
                placeholder="البحث بالعميل أو الوصف..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
              />
            </div>
          </div>
          <div className="flex gap-2 flex-wrap">
            {/* Multiple Payment Button - Only show when viewing a specific customer's debts */}
            {selectedCustomerId && stats.unpaidAmount > 0 && (
              <button
                onClick={handleMultiplePayment}
                className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800 hover:bg-green-200 dark:hover:bg-green-900/50 px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center"
                title={`دفع متعدد للديون - إجمالي المبلغ المتبقي: ${formatCurrency(stats.unpaidAmount)}`}
              >
                <FaMoneyBillWave className="ml-2" />
                دفع متعدد
              </button>
            )}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center ${
                showFilters
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              <FaFilter className="ml-2" />
              فلاتر
            </button>
            <button
              onClick={handleSearch}
              className="bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800 hover:bg-primary-200 dark:hover:bg-primary-900/50 px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center"
            >
              <FaSearch className="ml-2" />
              بحث
            </button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 p-6 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {/* Customer Filter */}
              <div className="col-span-1">
                <SelectInput
                  label="العميل"
                  name="customer"
                  value={selectedCustomerId?.toString() || ''}
                  onChange={(value: string) => setSelectedCustomerId(value ? parseInt(value) : null)}
                  options={[
                    { value: '', label: 'جميع العملاء' },
                    ...customers.map(customer => ({
                      value: customer.id.toString(),
                      label: customer.name
                    }))
                  ]}
                  placeholder="اختر العميل..."
                />
              </div>

              {/* Payment Status Filter */}
              <div className="col-span-1">
                <SelectInput
                  label="حالة الدفع"
                  name="paymentStatus"
                  value={paymentStatus}
                  onChange={(value: string) => setPaymentStatus(value)}
                  options={[
                    { value: 'all', label: 'جميع الحالات' },
                    { value: 'unpaid', label: 'غير مدفوعة' },
                    { value: 'partial', label: 'مدفوعة جزئياً' },
                    { value: 'paid', label: 'مدفوعة بالكامل' }
                  ]}
                  placeholder="اختر حالة الدفع..."
                />
              </div>

              {/* Debt Type Filter */}
              <div className="col-span-1">
                <SelectInput
                  label="نوع الدين"
                  name="debtType"
                  value={debtType}
                  onChange={(value: string) => setDebtType(value)}
                  options={[
                    { value: 'all', label: 'جميع الأنواع' },
                    { value: 'invoice', label: 'ديون الفواتير' },
                    { value: 'standalone', label: 'ديون مباشرة' }
                  ]}
                  placeholder="اختر نوع الدين..."
                />
              </div>

              {/* Items Per Page Filter */}
              <div className="col-span-1">
                <SelectInput
                  label="عدد العناصر"
                  name="itemsPerPage"
                  value={itemsPerPage.toString()}
                  onChange={(value: string) => {
                    const newItemsPerPage = parseInt(value, 10);
                    setItemsPerPage(newItemsPerPage);
                    setCurrentPage(1); // Reset to first page when changing items per page
                    // Apply change immediately
                    setTimeout(() => {
                      fetchDebts(1, newItemsPerPage);
                    }, 0);
                  }}
                  options={[
                    { value: '10', label: '10' },
                    { value: '20', label: '20' },
                    { value: '30', label: '30' },
                    { value: '50', label: '50' }
                  ]}
                  placeholder="اختر عدد العناصر..."
                />
              </div>

              {/* Amount Range Filters */}
              <div className="col-span-1">
                <NumberInput
                  label="الحد الأدنى للمبلغ المتبقي"
                  name="minAmount"
                  value={minAmount}
                  onChange={(value: string) => setMinAmount(value)}
                  placeholder="0.00"
                  min={0}
                  step="0.01"
                  dir="ltr"
                  currency="د.ل"
                />
              </div>

              <div className="col-span-1">
                <NumberInput
                  label="الحد الأقصى للمبلغ المتبقي"
                  name="maxAmount"
                  value={maxAmount}
                  onChange={(value: string) => setMaxAmount(value)}
                  placeholder="0.00"
                  min={0}
                  step="0.01"
                  dir="ltr"
                  currency="د.ل"
                />
              </div>

              {/* Date Range Filters */}
              <div className="col-span-1">
                <DatePicker
                  label="من تاريخ"
                  name="startDate"
                  value={startDate}
                  onChange={(value) => setStartDate(value)}
                  placeholder="اختر التاريخ"
                />
              </div>

              <div className="col-span-1">
                <DatePicker
                  label="إلى تاريخ"
                  name="endDate"
                  value={endDate}
                  onChange={(value) => setEndDate(value)}
                  placeholder="اختر التاريخ"
                />
              </div>

              {/* Action Buttons */}
              <div className="col-span-full flex flex-col sm:flex-row gap-4 justify-center sm:justify-start">
                <button
                  type="button"
                  onClick={handleResetFilters}
                  className="bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium min-w-[140px] flex-1 sm:flex-initial"
                >
                  إعادة تعيين
                </button>
                <button
                  type="button"
                  onClick={handleApplyFilters}
                  className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium min-w-[140px] flex-1 sm:flex-initial focus:outline-none focus:ring-4 focus:ring-primary-500/20"
                >
                  تطبيق الفلاتر
                </button>
              </div>
            </div>

            {/* Filter Statistics Bar */}
            {filterStats.isFiltered && (
              <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-7 gap-4 text-sm">
                  <div className="text-center">
                    <div className="font-semibold text-blue-600 dark:text-blue-400">
                      {filterStats.totalFiltered}
                    </div>
                    <div className="text-gray-600 dark:text-gray-400">إجمالي النتائج</div>
                  </div>

                  <div className="text-center">
                    <div className="font-semibold text-red-600 dark:text-red-400">
                      {filterStats.unpaidFiltered}
                    </div>
                    <div className="text-gray-600 dark:text-gray-400">غير مدفوعة</div>
                  </div>

                  <div className="text-center">
                    <div className="font-semibold text-yellow-600 dark:text-yellow-400">
                      {filterStats.partialFiltered}
                    </div>
                    <div className="text-gray-600 dark:text-gray-400">جزئية</div>
                  </div>

                  <div className="text-center">
                    <div className="font-semibold text-green-600 dark:text-green-400">
                      {filterStats.paidFiltered}
                    </div>
                    <div className="text-gray-600 dark:text-gray-400">مدفوعة</div>
                  </div>

                  <div className="text-center">
                    <div className="font-semibold text-purple-600 dark:text-purple-400">
                      <FormattedCurrency amount={filterStats.totalAmountFiltered} />
                    </div>
                    <div className="text-gray-600 dark:text-gray-400">إجمالي المبلغ</div>
                  </div>

                  <div className="text-center">
                    <div className="font-semibold text-red-600 dark:text-red-400">
                      <FormattedCurrency amount={filterStats.unpaidAmountFiltered} />
                    </div>
                    <div className="text-gray-600 dark:text-gray-400">المبلغ المتبقي</div>
                  </div>

                  <div className="text-center">
                    <div className="font-semibold text-green-600 dark:text-green-400">
                      <FormattedCurrency amount={filterStats.paidAmountFiltered} />
                    </div>
                    <div className="text-gray-600 dark:text-gray-400">المبلغ المدفوع</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Debts List */}
      <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden table-subtle-border">
        {filteredDebts.length === 0 ? (
          <div className="text-center py-12">
            <FaMoneyBillWave className="mx-auto text-4xl text-gray-400 mb-4" />
            <p className="text-gray-500 dark:text-gray-400">لا توجد مديونيات</p>
          </div>
        ) : (
          <div className="overflow-x-auto custom-scrollbar-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    العميل
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    مبلغ الفاتورة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    المبلغ المدفوع
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    المبلغ المتبقي
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الوصف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    التاريخ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredDebts.map((debt) => (
                  <React.Fragment key={debt.id}>
                    <tr className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center">
                            <FaUser className="text-primary-600 dark:text-primary-400" />
                          </div>
                        </div>
                        <div className="mr-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {debt.customer?.name || 'عميل غير معروف'}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            #{debt.id}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {debt.sale ? (
                        <div className="flex items-center">
                          <FaFileInvoiceDollar className="text-blue-500 ml-2" />
                          <div className="flex flex-col">
                            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              <FormattedCurrency amount={debt.sale.total_amount + debt.sale.tax_amount - debt.sale.discount_amount} />
                            </span>
                            <span className="text-xs text-blue-600 dark:text-blue-400">
                              فاتورة #{debt.sale.id}
                            </span>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <FaMoneyBillWave className="text-gray-400 ml-2" />
                          <div className="flex flex-col">
                            <span className="text-sm text-gray-500 dark:text-gray-400">
                              -
                            </span>
                            <span className="text-xs text-gray-400 dark:text-gray-500">
                              دين مباشر
                            </span>
                          </div>
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <FaMoneyBillWave className="text-green-500 ml-2" />
                        <div className="flex flex-col">
                          <span className="text-sm font-medium text-green-600 dark:text-green-400">
                            <FormattedCurrency amount={debt.amount - debt.remaining_amount} />
                          </span>
                          <span className="text-xs text-green-500 dark:text-green-400">
                            {debt.payments && debt.payments.length > 0 ? `${debt.payments.length} دفعة` : 'لا توجد دفعات'}
                          </span>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <FaMoneyBillWave className={`ml-2 ${debt.remaining_amount > 0 ? 'text-red-500' : 'text-green-500'}`} />
                        <span className={`text-sm font-medium ${debt.remaining_amount > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
                          <FormattedCurrency amount={debt.remaining_amount} />
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      {debt.description || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <FaCalendarAlt className="text-gray-400 ml-2" />
                        <span className="text-sm text-gray-900 dark:text-gray-100">
                          <FormattedDateTime date={debt.created_at} showTime={true} />
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        debt.payment_status === 'paid'
                          ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                          : debt.payment_status === 'partial'
                          ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300'
                          : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                      }`}>
                        {debt.payment_status === 'paid' ? (
                          <>
                            <FaCheck className="ml-1" />
                            مدفوعة بالكامل
                          </>
                        ) : debt.payment_status === 'partial' ? (
                          <>
                            <FaClock className="ml-1" />
                            مدفوعة جزئياً
                          </>
                        ) : (
                          <>
                            <FaTimes className="ml-1" />
                            غير مدفوعة
                          </>
                        )}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center gap-2 justify-end">
                        {debt.payments && debt.payments.length > 0 && (
                          <button
                            onClick={() => toggleDebtExpansion(debt.id)}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
                            title="عرض الدفعات"
                          >
                            {expandedDebt === debt.id ? <FaChevronUp /> : <FaChevronDown />}
                          </button>
                        )}
                        {!debt.is_paid && (
                          <button
                            onClick={() => handleAddPayment(debt)}
                            className="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300"
                            title="إضافة دفعة"
                          >
                            <FaCreditCard />
                          </button>
                        )}

                        <button
                          onClick={() => handleDeleteDebt(debt)}
                          className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                          title="حذف"
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </td>
                  </tr>
                  {/* Expanded payments row */}
                  {expandedDebt === debt.id && debt.payments && debt.payments.length > 0 && (
                    <tr key={`payments-${debt.id}`} className="bg-gray-50 dark:bg-gray-800">
                      <td colSpan={8} className="px-6 py-4">
                        <div className="bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                            دفعات الدين ({debt.payments.length})
                          </h4>
                          <div className="space-y-2">
                            {debt.payments.map((payment: Payment) => (
                              <div
                                key={payment.id}
                                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-600 rounded-lg"
                              >
                                <div className="flex items-center gap-4">
                                  <div className="text-sm">
                                    <span className="font-medium text-gray-900 dark:text-white">
                                      <FormattedCurrency amount={payment.amount} />
                                    </span>
                                  </div>
                                  <div className="text-sm text-gray-600 dark:text-gray-300">
                                    {payment.payment_method === 'cash' ? 'نقدي' :
                                     payment.payment_method === 'card' ? 'بطاقة' : payment.payment_method}
                                  </div>
                                  <div className="text-sm text-gray-500 dark:text-gray-400">
                                    <FormattedDateTime date={payment.created_at} showTime={true} />
                                  </div>
                                  {payment.notes && (
                                    <div className="text-sm text-gray-500 dark:text-gray-400">
                                      {payment.notes}
                                    </div>
                                  )}
                                </div>
                                <div className="flex items-center gap-2">
                                  <button
                                    onClick={() => handleEditPayment(debt, payment)}
                                    className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300"
                                    title="تعديل الدفعة"
                                  >
                                    <FaEdit />
                                  </button>
                                  <button
                                    onClick={() => handleDeletePayment(debt, payment)}
                                    className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                                    title="حذف الدفعة"
                                  >
                                    <FaTrash />
                                  </button>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {!loading && pagination.total > 0 && (
          <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700">
            <div className="flex-1 flex justify-between items-center sm:hidden">
              <button
                onClick={() => {
                  const newPage = Math.max(1, pagination.page - 1);
                  setCurrentPage(newPage);
                  fetchDebts(newPage, itemsPerPage);
                }}
                disabled={pagination.page <= 1}
                className={`relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${
                  pagination.page <= 1
                    ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                    : 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                }`}
              >
                السابق
              </button>
              <button
                onClick={() => {
                  const newPage = Math.min(pagination.pages, pagination.page + 1);
                  setCurrentPage(newPage);
                  fetchDebts(newPage, itemsPerPage);
                }}
                disabled={pagination.page >= pagination.pages}
                className={`relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${
                  pagination.page >= pagination.pages
                    ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                    : 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                }`}
              >
                التالي
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  <span className="font-medium">{pagination.page}</span> / <span className="font-medium">{pagination.pages}</span>{' '}
                  <span className="text-xs text-gray-500 dark:text-gray-400">(الإجمالي: {pagination.total})</span>
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px rtl:space-x-reverse" aria-label="Pagination">
                  <button
                    onClick={() => {
                      const newPage = Math.max(1, pagination.page - 1);
                      setCurrentPage(newPage);
                      fetchDebts(newPage, itemsPerPage);
                    }}
                    disabled={pagination.page <= 1}
                    className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${
                      pagination.page <= 1
                        ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                        : 'text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    <span className="sr-only">السابق</span>
                    <FaChevronRight className="h-5 w-5" />
                  </button>

                  {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                    let pageNum;
                    if (pagination.pages <= 5) {
                      pageNum = i + 1;
                    } else if (pagination.page <= 3) {
                      pageNum = i + 1;
                    } else if (pagination.page >= pagination.pages - 2) {
                      pageNum = pagination.pages - 4 + i;
                    } else {
                      pageNum = pagination.page - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => {
                          setCurrentPage(pageNum);
                          fetchDebts(pageNum, itemsPerPage);
                        }}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          pagination.page === pageNum
                            ? 'z-10 bg-primary-50 dark:bg-primary-900 border-primary-500 dark:border-primary-500 text-primary-600 dark:text-primary-300'
                            : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}

                  <button
                    onClick={() => {
                      const newPage = Math.min(pagination.pages, pagination.page + 1);
                      setCurrentPage(newPage);
                      fetchDebts(newPage, itemsPerPage);
                    }}
                    disabled={pagination.page >= pagination.pages}
                    className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${
                      pagination.page >= pagination.pages
                        ? 'text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed'
                        : 'text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    <span className="sr-only">التالي</span>
                    <FaChevronLeft className="h-5 w-5" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Debt Form Modal */}
      <Modal
        isOpen={showDebtModal}
        onClose={() => setShowDebtModal(false)}
        title="إضافة مديونية جديدة"
        size="lg"
      >
        <form onSubmit={handleDebtSubmit} className="space-y-5">
          {/* Basic Information Section */}
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <FaMoneyBillWave className="ml-2 text-primary-600 dark:text-primary-400" />
              معلومات المديونية
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <SelectInput
                label="العميل"
                name="customer_id"
                value={debtFormData.customer_id.toString()}
                onChange={(value) => {
                  setDebtFormData({ ...debtFormData, customer_id: parseInt(value) });
                  if (formErrors.customer_id) {
                    setFormErrors({ ...formErrors, customer_id: undefined });
                  }
                }}
                options={[
                  { value: '0', label: 'اختر العميل' },
                  ...customers.map(customer => ({
                    value: customer.id.toString(),
                    label: customer.name
                  }))
                ]}
                placeholder="اختر العميل"
                required
                error={formErrors.customer_id}
                icon={<FaUser />}
              />

              <NumberInput
                label="المبلغ"
                name="amount"
                value={debtFormData.amount.toString()}
                onChange={(value) => {
                  setDebtFormData({ ...debtFormData, amount: parseFloat(value) || 0 });
                  if (formErrors.amount) {
                    setFormErrors({ ...formErrors, amount: undefined });
                  }
                }}
                placeholder="أدخل مبلغ المديونية"
                required
                min={0}
                step="0.01"
                currency="د.ل"
                error={formErrors.amount}
                icon={<FaMoneyBillWave />}
                dir="ltr"
              />
            </div>
          </div>

          {/* Description Section */}
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <FaEdit className="ml-2 text-primary-600 dark:text-primary-400" />
              تفاصيل إضافية
            </h3>
            <TextArea
              label="الوصف"
              name="description"
              value={debtFormData.description}
              onChange={(value) => setDebtFormData({ ...debtFormData, description: value })}
              placeholder="أدخل وصف المديونية (اختياري)"
              rows={3}
            />
          </div>

          <div className="flex flex-col sm:flex-row gap-3 sm:gap-3 justify-center pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={() => setShowDebtModal(false)}
              disabled={loading}
              className="btn-secondary flex items-center justify-center min-w-[120px]"
            >
              <span>إلغاء</span>
            </button>
            <button
              type="submit"
              disabled={loading}
              className="btn-primary flex items-center justify-center min-w-[140px]"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                  <span>جاري الحفظ...</span>
                </>
              ) : (
                <>
                  <FaPlus className="ml-2" />
                  <span>إضافة المديونية</span>
                </>
              )}
            </button>
          </div>
        </form>
      </Modal>

      {/* Payment Form Modal */}
      <Modal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        title="إضافة دفعة"
        size="lg"
      >
        <form onSubmit={handlePaymentSubmit} className="space-y-5">
          {selectedDebt && (
            <div className="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 p-4 rounded-lg border border-primary-200 dark:border-primary-800 mb-4">
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                <FaMoneyBillWave className="ml-2 text-primary-600 dark:text-primary-400" />
                تفاصيل المديونية
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center">
                  <FaUser className="ml-2 text-gray-500 dark:text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-400">العميل:</span>
                  <span className="mr-2 font-medium text-gray-900 dark:text-gray-100">{selectedDebt.customer?.name}</span>
                </div>
                <div className="flex items-center">
                  <FaMoneyBillWave className="ml-2 text-gray-500 dark:text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-400">المبلغ الإجمالي:</span>
                  <span className="mr-2 font-medium text-gray-900 dark:text-gray-100">
                    <FormattedCurrency amount={selectedDebt.amount} />
                  </span>
                </div>
                <div className="flex items-center">
                  <FaCreditCard className="ml-2 text-red-500 dark:text-red-400" />
                  <span className="text-gray-600 dark:text-gray-400">المبلغ المتبقي:</span>
                  <span className="mr-2 font-medium text-red-600 dark:text-red-400">
                    <FormattedCurrency amount={selectedDebt.remaining_amount} />
                  </span>
                </div>
                <div className="flex items-center">
                  <FaCalendarAlt className="ml-2 text-gray-500 dark:text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-400">تاريخ الإنشاء:</span>
                  <span className="mr-2 font-medium text-gray-900 dark:text-gray-100">
                    <FormattedDateTime date={selectedDebt.created_at} showTime={true} />
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Payment Information Section */}
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <FaCreditCard className="ml-2 text-primary-600 dark:text-primary-400" />
              معلومات الدفعة
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <NumberInput
                label="مبلغ الدفعة"
                name="amount"
                value={paymentFormData.amount.toString()}
                onChange={(value) => {
                  setPaymentFormData({ ...paymentFormData, amount: parseFloat(value) || 0 });
                  if (formErrors.amount) {
                    setFormErrors({ ...formErrors, amount: undefined });
                  }
                }}
                placeholder="أدخل مبلغ الدفعة"
                required
                min={0}
                max={selectedDebt?.remaining_amount}
                step="0.01"
                currency="د.ل"
                error={formErrors.amount}
                icon={<FaMoneyBillWave />}
                dir="ltr"
              />

              <SelectInput
                label="طريقة الدفع"
                name="payment_method"
                value={paymentFormData.payment_method}
                onChange={(value) => setPaymentFormData({ ...paymentFormData, payment_method: value })}
                options={[
                  { value: 'cash', label: 'نقدي', icon: <FaMoneyBillWave /> },
                  { value: 'card', label: 'بطاقة', icon: <FaCreditCard /> }
                ]}
                placeholder="اختر طريقة الدفع"
                required
                icon={<FaCreditCard />}
              />
            </div>
          </div>

          {/* Notes Section */}
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <FaEdit className="ml-2 text-primary-600 dark:text-primary-400" />
              ملاحظات إضافية
            </h3>
            <TextArea
              label="ملاحظات"
              name="notes"
              value={paymentFormData.notes}
              onChange={(value) => setPaymentFormData({ ...paymentFormData, notes: value })}
              placeholder="أدخل ملاحظات الدفعة (اختياري)"
              rows={3}
            />
          </div>

          <div className="flex flex-col sm:flex-row gap-3 sm:gap-3 justify-center pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={() => setShowPaymentModal(false)}
              disabled={loading}
              className="btn-secondary flex items-center justify-center min-w-[120px]"
            >
              <span>إلغاء</span>
            </button>
            <button
              type="submit"
              disabled={loading}
              className="btn-primary flex items-center justify-center min-w-[140px]"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                  <span>جاري التسجيل...</span>
                </>
              ) : (
                <>
                  <FaCheck className="ml-2" />
                  <span>تسجيل الدفعة</span>
                </>
              )}
            </button>
          </div>
        </form>
      </Modal>

      {/* Edit Payment Form Modal */}
      <Modal
        isOpen={showEditPaymentModal}
        onClose={() => setShowEditPaymentModal(false)}
        title="تعديل الدفعة"
        size="lg"
      >
        <form onSubmit={handlePaymentSubmit} className="space-y-5">
          {selectedDebt && (
            <div className="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 p-4 rounded-lg border border-primary-200 dark:border-primary-800 mb-4">
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                <FaMoneyBillWave className="ml-2 text-primary-600 dark:text-primary-400" />
                تفاصيل المديونية
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center">
                  <FaUser className="ml-2 text-gray-500 dark:text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-400">العميل:</span>
                  <span className="mr-2 font-medium text-gray-900 dark:text-gray-100">{selectedDebt.customer?.name}</span>
                </div>
                <div className="flex items-center">
                  <FaMoneyBillWave className="ml-2 text-gray-500 dark:text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-400">المبلغ الإجمالي:</span>
                  <span className="mr-2 font-medium text-gray-900 dark:text-gray-100">{formatCurrency(selectedDebt.amount)}</span>
                </div>
                <div className="flex items-center">
                  <FaCreditCard className="ml-2 text-red-500 dark:text-red-400" />
                  <span className="text-gray-600 dark:text-gray-400">المبلغ المتبقي:</span>
                  <span className="mr-2 font-medium text-red-600 dark:text-red-400">
                    {formatCurrency(selectedDebt.remaining_amount)}
                  </span>
                </div>
                <div className="flex items-center">
                  <FaCalendarAlt className="ml-2 text-gray-500 dark:text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-400">تاريخ الإنشاء:</span>
                  <span className="mr-2 font-medium text-gray-900 dark:text-gray-100">
                    <FormattedDateTime date={selectedDebt.created_at} showTime={true} />
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Payment Information Section */}
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <FaCreditCard className="ml-2 text-primary-600 dark:text-primary-400" />
              معلومات الدفعة
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <NumberInput
                label="مبلغ الدفعة"
                name="amount"
                value={paymentFormData.amount.toString()}
                onChange={(value) => {
                  setPaymentFormData({ ...paymentFormData, amount: parseFloat(value) || 0 });
                  if (formErrors.amount) {
                    setFormErrors({ ...formErrors, amount: undefined });
                  }
                }}
                placeholder="أدخل مبلغ الدفعة"
                required
                min={0}
                step="0.01"
                currency="د.ل"
                error={formErrors.amount}
                icon={<FaMoneyBillWave />}
                dir="ltr"
              />

              <SelectInput
                label="طريقة الدفع"
                name="payment_method"
                value={paymentFormData.payment_method}
                onChange={(value) => setPaymentFormData({ ...paymentFormData, payment_method: value })}
                options={[
                  { value: 'cash', label: 'نقدي', icon: <FaMoneyBillWave /> },
                  { value: 'card', label: 'بطاقة', icon: <FaCreditCard /> }
                ]}
                placeholder="اختر طريقة الدفع"
                required
                icon={<FaCreditCard />}
              />
            </div>
          </div>

          {/* Notes Section */}
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <FaEdit className="ml-2 text-primary-600 dark:text-primary-400" />
              ملاحظات إضافية
            </h3>
            <TextArea
              label="ملاحظات"
              name="notes"
              value={paymentFormData.notes}
              onChange={(value) => setPaymentFormData({ ...paymentFormData, notes: value })}
              placeholder="أدخل ملاحظات الدفعة (اختياري)"
              rows={3}
            />
          </div>

          <div className="flex flex-col sm:flex-row gap-3 sm:gap-3 justify-center pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={() => setShowEditPaymentModal(false)}
              disabled={loading}
              className="btn-secondary flex items-center justify-center min-w-[120px]"
            >
              <span>إلغاء</span>
            </button>
            <button
              type="submit"
              disabled={loading}
              className="btn-primary flex items-center justify-center min-w-[140px]"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                  <span>جاري التحديث...</span>
                </>
              ) : (
                <>
                  <FaEdit className="ml-2" />
                  <span>تحديث الدفعة</span>
                </>
              )}
            </button>
          </div>
        </form>
      </Modal>

      {/* Multiple Payment Modal */}
      <Modal
        isOpen={showMultiplePaymentModal}
        onClose={() => setShowMultiplePaymentModal(false)}
        title="دفع متعدد للديون"
        size="lg"
      >
        <form onSubmit={handleMultiplePaymentSubmit} className="space-y-5">
          {selectedCustomerId && (
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800 mb-4">
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                <FaMoneyBillWave className="ml-2 text-green-600 dark:text-green-400" />
                ملخص الديون
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center">
                  <FaUser className="ml-2 text-gray-500 dark:text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-400 ml-2">العميل:</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {customers.find(c => c.id === selectedCustomerId)?.name || 'غير محدد'}
                  </span>
                </div>
                <div className="flex items-center">
                  <FaFileInvoiceDollar className="ml-2 text-gray-500 dark:text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-400 ml-2">عدد الديون غير المدفوعة:</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{stats.unpaidDebts}</span>
                </div>
                <div className="flex items-center">
                  <FaMoneyBillWave className="ml-2 text-gray-500 dark:text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-400 ml-2">إجمالي المبلغ المتبقي:</span>
                  <span className="font-medium text-red-600 dark:text-red-400">{formatCurrency(stats.unpaidAmount)}</span>
                </div>
                <div className="flex items-center">
                  <FaCheckCircle className="ml-2 text-gray-500 dark:text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-400 ml-2">المبلغ المدفوع:</span>
                  <span className="font-medium text-green-600 dark:text-green-400">{formatCurrency(stats.totalPaidAmount)}</span>
                </div>
              </div>
              <div className="mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                <p className="text-sm text-yellow-800 dark:text-yellow-200 flex items-center">
                  <FaInfoCircle className="ml-2 flex-shrink-0" />
                  سيتم توزيع المبلغ المدفوع على الديون بذكاء: سداد الديون الأقدم أولاً، ثم توزيع المتبقي على الديون الأحدث.
                </p>
              </div>
            </div>
          )}

          {/* Payment Amount Section */}
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <FaMoneyBillWave className="ml-2 text-primary-600 dark:text-primary-400" />
              تفاصيل الدفع
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <NumberInput
                label="المبلغ المراد دفعه"
                name="total_amount"
                value={multiplePaymentFormData.total_amount.toString()}
                onChange={(value) => setMultiplePaymentFormData({ ...multiplePaymentFormData, total_amount: parseFloat(value) || 0 })}
                placeholder="0.00"
                min={0}
                max={stats.unpaidAmount}
                step="0.01"
                currency="د.ل"

                dir="ltr"
                error={formErrors.total_amount}
                required
              />
              <SelectInput
                label="طريقة الدفع"
                name="payment_method"
                value={multiplePaymentFormData.payment_method}
                onChange={(value: string) => setMultiplePaymentFormData({ ...multiplePaymentFormData, payment_method: value })}
                options={[
                  { value: 'cash', label: 'نقدي' },
                  { value: 'card', label: 'بطاقة' }
                ]}
                placeholder="اختر طريقة الدفع..."
              />
            </div>
          </div>

          {/* Notes Section */}
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <FaEdit className="ml-2 text-primary-600 dark:text-primary-400" />
              ملاحظات إضافية
            </h3>
            <TextArea
              label="ملاحظات"
              name="notes"
              value={multiplePaymentFormData.notes}
              onChange={(value) => setMultiplePaymentFormData({ ...multiplePaymentFormData, notes: value })}
              placeholder="أدخل ملاحظات الدفع المتعدد (اختياري)"
              rows={3}
            />
          </div>

          {/* Error Message */}
          {formErrors.general && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <div className="flex items-center">
                <FaTimes className="text-red-500 ml-2" />
                <span className="text-red-700 dark:text-red-300 text-sm">{formErrors.general}</span>
              </div>
            </div>
          )}

          <div className="flex flex-col sm:flex-row gap-3 sm:gap-3 justify-center pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={() => setShowMultiplePaymentModal(false)}
              disabled={loading}
              className="btn-secondary flex items-center justify-center min-w-[120px]"
            >
              <span>إلغاء</span>
            </button>
            <button
              type="submit"
              disabled={loading || multiplePaymentFormData.total_amount <= 0}
              className="btn-primary flex items-center justify-center min-w-[140px]"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                  <span>جاري المعالجة...</span>
                </>
              ) : (
                <>
                  <FaMoneyBillWave className="ml-2" />
                  <span>تنفيذ الدفع المتعدد</span>
                </>
              )}
            </button>
          </div>
        </form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={confirmDelete}
        title={paymentToDelete ? "تأكيد حذف الدفعة" : "تأكيد حذف المديونية"}
        message={paymentToDelete ? "هل أنت متأكد من رغبتك في حذف هذه الدفعة؟" : "هل أنت متأكد من رغبتك في حذف هذه المديونية؟"}
        itemName={
          paymentToDelete
            ? `دفعة بمبلغ ${formatCurrency(paymentToDelete.amount)} - ${paymentToDelete.payment_method === 'cash' ? 'نقدي' : 'بطاقة'}`
            : debtToDelete
              ? `مديونية ${debtToDelete.customer?.name || 'غير محدد'} - ${formatCurrency(debtToDelete.amount)}`
              : ''
        }
        isLoading={loading}
      />

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="نجح العملية"
        message={successMessage}
      />
    </div>
  );
};

export default Debts;
