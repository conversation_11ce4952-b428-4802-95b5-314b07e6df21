import React from 'react';
import { FaTrash, FaExclamationTriangle } from 'react-icons/fa';
import Modal from './Modal';

interface DeleteConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  itemName: string;
  isLoading?: boolean;
}

const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  itemName,
  isLoading = false
}) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} title={title} size="md" zIndex="high">
      <div className="text-center">
        {/* Warning Icon */}
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30 mb-4">
          <FaExclamationTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
        </div>

        {/* Message */}
        <div className="mb-6">
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
            {message}
          </p>
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-4">
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100 break-all">
              {itemName}
            </p>
          </div>
          <p className="text-xs text-red-600 dark:text-red-400 font-medium">
            ⚠️ هذا الإجراء لا يمكن التراجع عنه
          </p>
        </div>

        {/* Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-3 justify-center">
          <button
            onClick={onClose}
            disabled={isLoading}
            className="btn-secondary flex items-center justify-center min-w-[120px]"
          >
            <span>إلغاء</span>
          </button>
          <button
            onClick={onConfirm}
            disabled={isLoading}
            className="btn-danger flex items-center justify-center min-w-[120px]"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                <span>جاري الحذف...</span>
              </>
            ) : (
              <>
                <FaTrash className="ml-2" />
                <span>حذف</span>
              </>
            )}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteConfirmModal;
