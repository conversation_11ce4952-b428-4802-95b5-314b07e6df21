import React, { useState, useEffect, useRef } from 'react';
import { FaSearch, FaTimes } from 'react-icons/fa';

interface SearchSuggestion {
  id: number;
  name: string;
  barcode?: string;
}

interface DynamicSearchInputProps {
  value: string;
  onChange: (value: string) => void;
  onSearch: (value: string) => void;
  suggestions: SearchSuggestion[];
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

const DynamicSearchInput: React.FC<DynamicSearchInputProps> = ({
  value,
  onChange,
  onSearch,
  suggestions,
  placeholder = "البحث بالاسم أو الباركود...",
  className = "",
  disabled = false
}) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Filter suggestions based on input value
  const filteredSuggestions = suggestions.filter(suggestion => {
    if (!value.trim()) return false;

    const searchTerm = value.toLowerCase();
    const nameMatch = suggestion.name.toLowerCase().includes(searchTerm);
    const barcodeMatch = suggestion.barcode?.toLowerCase().includes(searchTerm);

    return nameMatch || barcodeMatch;
  }).slice(0, 10); // Limit to 10 suggestions



  // Handle input change (only update value and show suggestions, no search)
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    setSelectedIndex(-1);
    // Show suggestions only if there's text and at least 2 characters
    setShowSuggestions(newValue.trim().length >= 2);
  };

  // Handle key down events
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!showSuggestions || filteredSuggestions.length === 0) {
      if (e.key === 'Enter') {
        e.preventDefault();
        handleSearch();
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < filteredSuggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : filteredSuggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < filteredSuggestions.length) {
          const selected = filteredSuggestions[selectedIndex];
          const searchValue = selected.name; // Always use product name
          onChange(searchValue);
          setShowSuggestions(false);
          setSelectedIndex(-1);
          onSearch(searchValue);
        } else {
          handleSearch();
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    const searchValue = suggestion.name; // Always use product name
    onChange(searchValue);
    setShowSuggestions(false);
    setSelectedIndex(-1);
    // Trigger search immediately when suggestion is selected
    setTimeout(() => {
      onSearch(searchValue);
    }, 100);
  };

  // Handle search button click
  const handleSearch = () => {
    if (value.trim()) {
      onSearch(value.trim());
      setShowSuggestions(false);
      setSelectedIndex(-1);
    }
  };

  // Handle clear button click
  const handleClear = () => {
    onChange('');
    setShowSuggestions(false);
    setSelectedIndex(-1);
    // Trigger search with empty value to reload all data
    onSearch('');
    inputRef.current?.focus();
  };

  // Close suggestions when clicking outside and handle scroll/resize
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
        setSelectedIndex(-1);
      }
    };

    const handleScroll = (event: Event) => {
      // Don't hide suggestions if scrolling within the suggestions container
      if (showSuggestions && suggestionsRef.current) {
        const target = event.target as Element;
        const isScrollingInsideSuggestions = suggestionsRef.current.contains(target) ||
                                           target === suggestionsRef.current;

        if (!isScrollingInsideSuggestions) {
          setShowSuggestions(false);
          setSelectedIndex(-1);
        }
      }
    };

    const handleResize = () => {
      if (showSuggestions) {
        setShowSuggestions(false);
        setSelectedIndex(-1);
      }
    };

    if (showSuggestions) {
      document.addEventListener('mousedown', handleClickOutside);
      window.addEventListener('scroll', handleScroll, true);
      window.addEventListener('resize', handleResize);

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        window.removeEventListener('scroll', handleScroll, true);
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [showSuggestions]);

  // Update suggestions position when showing
  useEffect(() => {
    if (showSuggestions && inputRef.current && suggestionsRef.current) {
      const rect = inputRef.current.getBoundingClientRect();
      const suggestionsEl = suggestionsRef.current;

      // Calculate position
      let top = rect.bottom + 4;
      let left = rect.left;

      // Use exact input width (no minimum width)
      const suggestionsWidth = rect.width;

      // Check if suggestions would go off-screen
      const suggestionsHeight = suggestionsEl.scrollHeight;
      const viewportHeight = window.innerHeight;

      // If suggestions would go below viewport, show above input
      if (top + suggestionsHeight > viewportHeight - 20) {
        top = rect.top - suggestionsHeight - 4;
      }

      // Ensure suggestions don't go off left/right edges
      if (left + suggestionsWidth > window.innerWidth - 20) {
        left = window.innerWidth - suggestionsWidth - 20;
      }
      if (left < 20) {
        left = 20;
      }

      // Apply position with exact input width
      suggestionsEl.style.top = `${top}px`;
      suggestionsEl.style.left = `${left}px`;
      suggestionsEl.style.width = `${suggestionsWidth}px`;
    }
  }, [showSuggestions, filteredSuggestions.length]);

  // Highlight matching text in suggestions
  const highlightMatch = (text: string, searchTerm: string) => {
    if (!searchTerm.trim()) return text;

    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);

    return parts.map((part, index) =>
      regex.test(part) ? (
        <span key={index} className="bg-primary-100 dark:bg-primary-800 text-primary-800 dark:text-primary-200 font-semibold px-1 rounded">
          {part}
        </span>
      ) : part
    );
  };

  return (
    <div className={`relative ${className}`}>
      {/* Search Input Container */}
      <div className="flex">
        {/* Search Input */}
        <div className="relative flex-1">
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <FaSearch className="h-4 w-4 text-gray-400" />
          </div>
          <input
            ref={inputRef}
            type="text"
            placeholder={placeholder}
            value={value}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={() => {
              // Show suggestions only if there's enough text and filtered suggestions exist
              if (value.trim().length >= 2 && filteredSuggestions.length > 0) {
                setShowSuggestions(true);
              }
            }}
            disabled={disabled}
            className="block w-full pr-10 pl-3 h-12 border-2 border-gray-300 dark:border-gray-600 rounded-r-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-primary-500/20 focus:border-primary-500 hover:border-gray-400 dark:hover:border-gray-500 transition-all duration-200 ease-in-out text-sm border-l-0"
          />
          {value && (
            <button
              onClick={handleClear}
              className="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              type="button"
            >
              <FaTimes className="h-3 w-3" />
            </button>
          )}
        </div>

        {/* Search Button */}
        <button
          onClick={handleSearch}
          disabled={disabled || !value.trim()}
          className="px-6 h-12 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-l-xl transition-all duration-200 ease-in-out flex items-center justify-center border-2 border-primary-600 hover:border-primary-700 disabled:border-gray-400 focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl"
          type="button"
        >
          <FaSearch className="h-4 w-4" />
        </button>
      </div>

      {/* Suggestions Dropdown */}
      {showSuggestions && filteredSuggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="fixed z-[9999] bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-xl overflow-hidden"
          onMouseEnter={() => {
            // Prevent hiding suggestions when mouse enters
          }}
          onMouseLeave={() => {
            // Allow hiding suggestions when mouse leaves
          }}
        >
          <div
            className="max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-gray-100 dark:scrollbar-track-gray-800"
            onScroll={(e) => e.stopPropagation()}
          >
            {filteredSuggestions.map((suggestion, index) => (
              <div
                key={suggestion.id}
                onClick={() => handleSuggestionClick(suggestion)}
                className={`px-3 py-2 cursor-pointer transition-colors duration-150 ${
                  index === selectedIndex
                    ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                    : 'hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100'
                } ${index !== filteredSuggestions.length - 1 ? 'border-b border-gray-100 dark:border-gray-700' : ''}`}
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0 ml-2">
                    <div className="w-5 h-5 bg-primary-100 dark:bg-primary-900/30 rounded flex items-center justify-center">
                      <svg className="w-2.5 h-2.5 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                      </svg>
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      {highlightMatch(suggestion.name, value)}
                    </div>
                    {suggestion.barcode && (
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5 truncate">
                        {highlightMatch(suggestion.barcode, value)}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default DynamicSearchInput;
