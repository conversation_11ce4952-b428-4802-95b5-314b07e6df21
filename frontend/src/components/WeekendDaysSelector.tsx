import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Che<PERSON> } from 'react-icons/fi';

interface WeekendDaysSelectorProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

interface DayOption {
  key: string;
  label: string;
  shortLabel: string;
  color: string;
}

const WeekendDaysSelector: React.FC<WeekendDaysSelectorProps> = ({
  value,
  onChange,
  className = ''
}) => {
  // تعريف أيام الأسبوع
  const days: DayOption[] = [
    { key: 'sunday', label: 'الأحد', shortLabel: 'أحد', color: 'bg-red-100 text-red-700 border-red-300' },
    { key: 'monday', label: 'الاثنين', shortLabel: 'اثن', color: 'bg-blue-100 text-blue-700 border-blue-300' },
    { key: 'tuesday', label: 'الثلاثاء', shortLabel: 'ثلا', color: 'bg-green-100 text-green-700 border-green-300' },
    { key: 'wednesday', label: 'الأربعاء', shortLabel: 'أرب', color: 'bg-yellow-100 text-yellow-700 border-yellow-300' },
    { key: 'thursday', label: 'الخميس', shortLabel: 'خمي', color: 'bg-purple-100 text-purple-700 border-purple-300' },
    { key: 'friday', label: 'الجمعة', shortLabel: 'جمع', color: 'bg-indigo-100 text-indigo-700 border-indigo-300' },
    { key: 'saturday', label: 'السبت', shortLabel: 'سبت', color: 'bg-pink-100 text-pink-700 border-pink-300' }
  ];

  // تحويل القيمة الحالية إلى مصفوفة
  const selectedDays = value ? value.split(',').map(day => day.trim()) : [];

  // دالة تبديل اختيار اليوم
  const toggleDay = (dayKey: string) => {
    let newSelectedDays: string[];
    
    if (selectedDays.includes(dayKey)) {
      // إزالة اليوم من الاختيار
      newSelectedDays = selectedDays.filter(day => day !== dayKey);
    } else {
      // إضافة اليوم للاختيار
      newSelectedDays = [...selectedDays, dayKey];
    }
    
    // ترتيب الأيام حسب ترتيب الأسبوع
    const orderedDays = days
      .filter(day => newSelectedDays.includes(day.key))
      .map(day => day.key);
    
    onChange(orderedDays.join(','));
  };

  // دالة اختيار سريع للأنماط الشائعة
  const selectPattern = (pattern: string) => {
    onChange(pattern);
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* عنوان المكون */}
      <div className="flex items-center space-x-2 space-x-reverse">
        <FiCalendar className="text-blue-500" />
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          اختر الأيام التي لا يعمل فيها المتجر
        </span>
      </div>

      {/* شبكة الأيام */}
      <div className="grid grid-cols-7 gap-2">
        {days.map((day) => {
          const isSelected = selectedDays.includes(day.key);
          
          return (
            <button
              key={day.key}
              type="button"
              onClick={() => toggleDay(day.key)}
              className={`
                relative p-3 rounded-lg border-2 transition-all duration-200 text-center
                hover:scale-105 hover:shadow-md
                ${isSelected 
                  ? `${day.color} border-opacity-100 shadow-sm` 
                  : 'bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                }
              `}
            >
              {/* أيقونة التحديد */}
              {isSelected && (
                <div className="absolute -top-1 -right-1 bg-green-500 text-white rounded-full p-1">
                  <FiCheck className="w-3 h-3" />
                </div>
              )}
              
              {/* اسم اليوم */}
              <div className="text-sm font-medium">{day.label}</div>
            </button>
          );
        })}
      </div>

      {/* الأنماط السريعة */}
      <div className="space-y-2">
        <div className="text-xs font-medium text-gray-600 dark:text-gray-400">
          أنماط سريعة:
        </div>
        <div className="flex flex-wrap gap-2">
          <button
            type="button"
            onClick={() => selectPattern('friday,saturday')}
            className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors"
          >
            الجمعة والسبت
          </button>
          <button
            type="button"
            onClick={() => selectPattern('saturday,sunday')}
            className="px-3 py-1 text-xs bg-green-100 text-green-700 rounded-full hover:bg-green-200 transition-colors"
          >
            السبت والأحد
          </button>
          <button
            type="button"
            onClick={() => selectPattern('friday')}
            className="px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded-full hover:bg-purple-200 transition-colors"
          >
            الجمعة فقط
          </button>
          <button
            type="button"
            onClick={() => selectPattern('')}
            className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
          >
            لا توجد عطل
          </button>
        </div>
      </div>

      {/* معاينة الاختيار */}
      <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
        <div className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
          الأيام المختارة:
        </div>
        <div className="text-sm text-gray-800 dark:text-gray-200">
          {selectedDays.length > 0 ? (
            selectedDays.map(dayKey => {
              const day = days.find(d => d.key === dayKey);
              return day?.label;
            }).join(' - ')
          ) : (
            'لا توجد أيام عطل محددة'
          )}
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          {selectedDays.length > 0 
            ? `${selectedDays.length} ${selectedDays.length === 1 ? 'يوم' : 'أيام'} في الأسبوع`
            : 'المتجر يعمل جميع أيام الأسبوع'
          }
        </div>
      </div>
    </div>
  );
};

export default WeekendDaysSelector;
