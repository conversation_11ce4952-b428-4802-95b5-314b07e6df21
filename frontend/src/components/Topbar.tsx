import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { useSidebarStore } from '../stores/sidebarStore';
import { useTheme } from '../contexts/ThemeContext';
import { useFullscreen } from '../hooks/useFullscreen';
import { ChatHeaderButton } from './Chat';
import SystemStatusIndicators from './SystemStatusIndicators';
import SystemAlerts from './SystemAlerts';
import {
  FiMenu,
  FiSearch,
  FiPlus,
  FiShoppingCart,
  FiBell,
  FiUser,
  FiSun,
  FiMoon,
  FiMaximize,
  FiMinimize,
  FiLogOut,
  FiSettings,
  FiHelpCircle,
  FiChevronDown
} from 'react-icons/fi';

interface TopbarProps {
  className?: string;
}

const Topbar: React.FC<TopbarProps> = ({ className = '' }) => {
  const navigate = useNavigate();
  const { logout, user } = useAuthStore();
  const { toggleSidebar, toggleMobileMenu } = useSidebarStore();
  const { toggleTheme, currentTheme } = useTheme();
  const { isFullscreen, toggleFullscreen } = useFullscreen();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showQuickActions, setShowQuickActions] = useState(false);

  // معالج تسجيل الخروج
  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  // معالج البحث
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // يمكن إضافة منطق البحث هنا
      console.log('البحث عن:', searchQuery);
    }
  };

  // الإجراءات السريعة
  const quickActions = [
    {
      id: 'new-sale',
      name: 'بيع جديد',
      path: '/pos',
      icon: FiShoppingCart,
      color: 'bg-green-500 hover:bg-green-600'
    },
    {
      id: 'new-product',
      name: 'منتج جديد',
      path: '/products?action=new',
      icon: FiPlus,
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      id: 'new-customer',
      name: 'عميل جديد',
      path: '/customers?action=new',
      icon: FiUser,
      color: 'bg-purple-500 hover:bg-purple-600'
    }
  ];

  return (
    <header className={`bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40 shadow-sm ${className}`}>
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-14">
          
          {/* الجانب الأيمن - الشعار والقائمة */}
          <div className="flex items-center gap-3">
            {/* زر القائمة */}
            <button
              onClick={toggleMobileMenu}
              className="lg:hidden p-2 rounded-lg text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              aria-label="فتح القائمة"
            >
              <FiMenu className="w-4 h-4" />
            </button>

            {/* زر تبديل الشريط الجانبي للشاشات الكبيرة */}
            <button
              onClick={toggleSidebar}
              className="hidden lg:block p-2 rounded-lg text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              aria-label="تبديل الشريط الجانبي"
            >
              <FiMenu className="w-4 h-4" />
            </button>

            {/* شعار التطبيق */}
            <Link to="/" className="flex items-center">
              <span className="text-lg sm:text-xl font-bold text-primary-600">
                Smart<span className="text-secondary-800 dark:text-secondary-200">POS</span>
              </span>
            </Link>

            {/* مؤشرات حالة النظام */}
            <div className="hidden md:block">
              <SystemStatusIndicators />
            </div>
          </div>

          {/* الوسط - شريط البحث */}
          <div className="hidden md:flex flex-1 max-w-md mx-6">
            <form onSubmit={handleSearch} className="w-full">
              <div className="relative">
                <FiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="البحث..."
                  className="w-full pl-4 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 text-sm"
                />
              </div>
            </form>
          </div>

          {/* الجانب الأيسر - الإجراءات والمستخدم */}
          <div className="flex items-center gap-2">

            {/* التنبيهات */}
            <SystemAlerts showInHeader={true} />

            {/* المحادثة */}
            <ChatHeaderButton />

            {/* الإجراءات السريعة */}
            <div className="relative">
              <button
                onClick={() => setShowQuickActions(!showQuickActions)}
                className="p-2 rounded-lg bg-primary-600 text-white hover:bg-primary-700 transition-colors shadow-sm"
                aria-label="الإجراءات السريعة"
              >
                <FiPlus className="w-4 h-4" />
              </button>

              {showQuickActions && (
                <div className="absolute left-0 mt-2 w-44 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50">
                  {quickActions.map((action) => (
                    <Link
                      key={action.id}
                      to={action.path}
                      onClick={() => setShowQuickActions(false)}
                      className="flex items-center px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-sm"
                    >
                      <span className={`p-1.5 rounded-md ${action.color} text-white ml-2`}>
                        <action.icon className="w-3 h-3" />
                      </span>
                      <span className="font-medium">{action.name}</span>
                    </Link>
                  ))}
                </div>
              )}
            </div>

            {/* زر POS السريع */}
            <Link
              to="/pos"
              className="hidden sm:flex items-center gap-1.5 px-3 py-1.5 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors shadow-sm text-sm"
            >
              <FiShoppingCart className="w-3 h-3" />
              <span className="font-medium">POS</span>
            </Link>

            {/* مركز المساعدة */}
            <button
              onClick={() => navigate('/help')}
              className="p-2 rounded-lg text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              aria-label="مركز المساعدة"
            >
              <FiHelpCircle className="w-4 h-4" />
            </button>

            {/* تبديل الوضع المظلم */}
            <button
              onClick={toggleTheme}
              className="p-2 rounded-lg text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              aria-label={currentTheme === 'dark' ? 'التبديل إلى الوضع المضيء' : 'التبديل إلى الوضع المظلم'}
            >
              {currentTheme === 'dark' ? (
                <FiSun className="w-4 h-4" />
              ) : (
                <FiMoon className="w-4 h-4" />
              )}
            </button>

            {/* ملء الشاشة */}
            <button
              onClick={toggleFullscreen}
              className="hidden lg:block p-2 rounded-lg text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              aria-label={isFullscreen ? 'الخروج من ملء الشاشة' : 'ملء الشاشة'}
            >
              {isFullscreen ? (
                <FiMinimize className="w-4 h-4" />
              ) : (
                <FiMaximize className="w-4 h-4" />
              )}
            </button>

            {/* قائمة المستخدم */}
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center gap-2 p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <div className="w-7 h-7 bg-primary-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
                  {user?.full_name?.charAt(0) || user?.username?.charAt(0) || 'U'}
                </div>
                <div className="hidden md:block text-right">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate max-w-24">
                    {user?.full_name || user?.username}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {user?.role === 'admin' ? 'مدير' : 'كاشير'}
                  </div>
                </div>
                <FiChevronDown className="w-3 h-3 text-gray-500" />
              </button>

              {showUserMenu && (
                <div className="absolute left-0 mt-2 w-44 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50">
                  <Link
                    to="/settings"
                    onClick={() => setShowUserMenu(false)}
                    className="flex items-center px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-sm"
                  >
                    <FiSettings className="w-3 h-3 ml-2" />
                    <span>الإعدادات</span>
                  </Link>
                  <hr className="my-1 border-gray-200 dark:border-gray-700" />
                  <button
                    onClick={handleLogout}
                    className="flex items-center w-full px-3 py-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors text-sm"
                  >
                    <FiLogOut className="w-3 h-3 ml-2" />
                    <span>تسجيل الخروج</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* شريط البحث للشاشات الصغيرة */}
        <div className="md:hidden pb-3">
          <form onSubmit={handleSearch}>
            <div className="relative">
              <FiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="البحث..."
                className="w-full pl-4 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 text-sm"
              />
            </div>
          </form>
        </div>
      </div>

      {/* إغلاق القوائم عند النقر خارجها */}
      {(showUserMenu || showQuickActions) && (
        <div
          className="fixed inset-0 z-30"
          onClick={() => {
            setShowUserMenu(false);
            setShowQuickActions(false);
          }}
        />
      )}
    </header>
  );
};

export default Topbar;
