/**
 * مكون إعدادات تنسيق الأرقام المالية
 * يوفر واجهة لتخصيص طريقة عرض الأرقام المالية في النظام
 */

import React, { useState, useEffect } from 'react';
import {
  FiHash,
  FiCheck,
  FiRefreshCw,
  FiDollarSign,
  FiSettings
} from 'react-icons/fi';
import {
  numberFormattingService,
  type NumberFormatSettings as NumberFormatSettingsType
} from '../services/numberFormattingService';
import { SelectInput, NumberInput, TextInput } from './inputs';
import ToggleSwitch from './ToggleSwitch';

interface NumberFormatSettingsProps {
  className?: string;
  settings?: Record<string, string>;
  onSettingChange?: (key: string, value: string) => void;
}

const NumberFormatSettings: React.FC<NumberFormatSettingsProps> = ({
  className = '',
  settings: parentSettings,
  onSettingChange
}) => {
  const [localSettings, setLocalSettings] = useState<NumberFormatSettingsType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [previewAmount] = useState(1234567890.00);
  const [previewText, setPreviewText] = useState('');

  // تحميل الإعدادات أو استخدام الإعدادات من الصفحة الرئيسية
  useEffect(() => {
    if (parentSettings) {
      // استخدام الإعدادات من الصفحة الرئيسية
      const formatSettings: NumberFormatSettingsType = {
        separatorType: (parentSettings.number_separator_type as any) || 'comma',
        formatType: (parentSettings.number_format_type as any) || 'thousands',
        showDecimals: typeof parentSettings.show_decimals === 'boolean' ?
          parentSettings.show_decimals :
          parentSettings.show_decimals === 'true',
        decimalPlaces: parseInt(parentSettings.decimal_places) || 2,
        currencySymbol: parentSettings.currency_symbol || 'د.ل',
        symbolPosition: (parentSettings.currency_position as any) || 'after'
      };
      setLocalSettings(formatSettings);
      setIsLoading(false);
    } else {
      loadSettings();
    }
  }, [parentSettings]);

  // تحديث المعاينة عند تغيير الإعدادات
  useEffect(() => {
    if (localSettings) {
      updatePreview();
    }
  }, [localSettings]);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      const currentSettings = await numberFormattingService.getCurrentSettings();
      setLocalSettings(currentSettings);
    } catch (error) {
      console.error('فشل في تحميل إعدادات التنسيق:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updatePreview = async () => {
    if (!localSettings) return;

    try {
      // تطبيق التنسيق يدوياً للمعاينة الفورية
      let numberStr = previewAmount.toFixed(localSettings.showDecimals ? localSettings.decimalPlaces : 0);

      // تطبيق الفواصل
      if (localSettings.separatorType !== 'none') {
        const parts = numberStr.split('.');
        const integerPart = parts[0];
        const decimalPart = parts[1];
        const separator = localSettings.separatorType === 'comma' ? ',' : ' ';
        const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, separator);
        numberStr = decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger;
      }

      // إضافة رمز العملة
      let formattedPreview = '';
      if (localSettings.symbolPosition === 'before') {
        formattedPreview = `${localSettings.currencySymbol} ${numberStr}`;
      } else {
        formattedPreview = `${numberStr} ${localSettings.currencySymbol}`;
      }

      setPreviewText(formattedPreview);
    } catch (error) {
      console.error('فشل في تحديث المعاينة:', error);
      setPreviewText('خطأ في المعاينة');
    }
  };

  const handleSettingChange = (key: keyof NumberFormatSettingsType, value: any) => {
    if (!localSettings) return;

    // تحديث الإعدادات محلياً أولاً لتحديث المعاينة فوراً
    const newSettings = { ...localSettings, [key]: value };
    setLocalSettings(newSettings);

    // إذا كان هناك callback من الصفحة الرئيسية، استخدمه
    if (onSettingChange) {
      // تحويل المفتاح إلى تنسيق الخادم
      const serverKey = mapClientKeyToServerKey(key);
      onSettingChange(serverKey, value.toString());
    }
  };

  // تحويل مفاتيح العميل إلى مفاتيح الخادم
  const mapClientKeyToServerKey = (clientKey: string): string => {
    const keyMap: Record<string, string> = {
      'separatorType': 'number_separator_type',
      'formatType': 'number_format_type',
      'showDecimals': 'show_decimals',
      'decimalPlaces': 'decimal_places',
      'currencySymbol': 'currency_symbol',
      'symbolPosition': 'currency_position'
    };
    return keyMap[clientKey] || clientKey;
  };

  const refreshSettings = async () => {
    numberFormattingService.clearCache();
    await loadSettings();
  };

  if (isLoading) {
    return (
      <div className={`${className}`}>
        <div className="flex items-center justify-center py-8">
          <FiRefreshCw className="animate-spin text-2xl text-primary-500 ml-2" />
          <span className="text-gray-600 dark:text-gray-400">جاري تحميل إعدادات التنسيق...</span>
        </div>
      </div>
    );
  }

  if (!localSettings) {
    return (
      <div className={`${className}`}>
        <div className="text-center py-8 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
          <span className="text-red-600 dark:text-red-400">فشل في تحميل إعدادات التنسيق</span>
          <button
            onClick={loadSettings}
            className="block mx-auto mt-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      {/* العنوان */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <FiHash className="text-2xl text-primary-500 ml-3" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              تنسيق الأرقام المالية
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              تخصيص طريقة عرض الأرقام المالية في جميع أنحاء النظام
            </p>
          </div>
        </div>
        <button
          onClick={refreshSettings}
          className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          title="تحديث الإعدادات"
        >
          <FiRefreshCw className="text-gray-600 dark:text-gray-400" />
        </button>
      </div>



      {/* إعدادات العملة */}
      <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6 mb-6 border border-gray-200 dark:border-gray-600">
        <h4 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-6 flex items-center">
          <FiDollarSign className="ml-2 text-primary-500" />
          إعدادات العملة
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* رمز العملة */}
          <div className="space-y-2">
            <TextInput
              label="رمز العملة"
              name="currencySymbol"
              value={localSettings.currencySymbol}
              onChange={(value) => handleSettingChange('currencySymbol', value)}
              placeholder="د.ل"
              icon={<FiDollarSign />}
            />
          </div>

          {/* موضع رمز العملة */}
          <div className="space-y-2">
            <SelectInput
              label="موضع رمز العملة"
              name="symbolPosition"
              value={localSettings.symbolPosition}
              onChange={(value) => handleSettingChange('symbolPosition', value)}
              options={[
                { value: 'after', label: 'بعد الرقم' },
                { value: 'before', label: 'قبل الرقم' }
              ]}
            />
          </div>
        </div>
      </div>

      {/* إعدادات التنسيق */}
      <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6 mb-6 border border-gray-200 dark:border-gray-600">
        <h4 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-6 flex items-center">
          <FiSettings className="ml-2 text-primary-500" />
          إعدادات التنسيق
        </h4>

        <div className="space-y-6">
          {/* نوع الفاصل */}
          <div className="space-y-2">
            <SelectInput
              label="نوع الفاصل"
              name="separatorType"
              value={localSettings.separatorType}
              onChange={(value) => handleSettingChange('separatorType', value)}
              options={[
                { value: 'comma', label: 'فاصلة (,)' },
                { value: 'space', label: 'مسافة ( )' },
                { value: 'none', label: 'بدون فاصل' }
              ]}
            />
          </div>

          {/* إظهار الأرقام العشرية */}
          <div className="bg-white dark:bg-gray-800 p-4 rounded-xl border-2 border-gray-200 dark:border-gray-600 h-12 flex items-center">
            <ToggleSwitch
              id="showDecimals"
              checked={localSettings.showDecimals}
              onChange={(checked) => handleSettingChange('showDecimals', checked)}
              label="إظهار الأرقام العشرية"
              className="w-full"
            />
          </div>

          {/* عدد الأرقام العشرية */}
          {localSettings.showDecimals && (
            <div className="space-y-2">
              <NumberInput
                label="عدد الأرقام العشرية"
                name="decimalPlaces"
                value={localSettings.decimalPlaces.toString()}
                onChange={(value) => handleSettingChange('decimalPlaces', parseInt(value) || 0)}
                min={0}
                max={4}
                step={1}
                placeholder="2"
              />
            </div>
          )}
        </div>
      </div>

      {/* معاينة */}
      <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
        <h4 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4 flex items-center">
          <FiCheck className="ml-2 text-green-500" />
          معاينة التنسيق
        </h4>
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 text-center border border-gray-200 dark:border-gray-600">
          <div className="text-sm text-gray-500 dark:text-gray-400 mb-3">
            الرقم الأصلي: {previewAmount.toLocaleString()}
          </div>
          <div className="text-2xl font-bold text-primary-600 dark:text-primary-400 font-mono bg-gray-50 dark:bg-gray-700 rounded-lg px-4 py-3 border border-gray-200 dark:border-gray-600">
            {previewText || 'جاري التحميل...'}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400 mt-3">
            التنسيق المطبق حسب الإعدادات المحددة
          </div>
        </div>
      </div>
    </div>
  );
};

export default NumberFormatSettings;
