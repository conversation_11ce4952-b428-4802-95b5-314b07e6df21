/**
 * نافذة منبثقة لاختيار العنوان من الخريطة
 * تحتوي على الخريطة التفاعلية مع إمكانية البحث وتحديد الموقع
 * يطبق مبادئ البرمجة الكائنية والتصميم الموحد للنظام
 */

import React, { useState, useEffect } from 'react';
import { FiMapPin, FiCheck, FiX, FiLoader } from 'react-icons/fi';
import Modal from './Modal';
import InteractiveMap from './InteractiveMap';
import { LocationCoordinates } from '../services/openStreetMapService';
import './InteractiveMap.css';

interface AddressPickerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddressSelect: (address: string, coordinates?: LocationCoordinates) => Promise<void>;
  currentAddress?: string;
  currentCoordinates?: LocationCoordinates;
  title?: string;
}

const AddressPickerModal: React.FC<AddressPickerModalProps> = ({
  isOpen,
  onClose,
  onAddressSelect,
  currentAddress = '',
  currentCoordinates,
  title = 'اختيار عنوان المتجر من الخريطة'
}) => {
  const [selectedAddress, setSelectedAddress] = useState<string>('');
  const [selectedCoordinates, setSelectedCoordinates] = useState<LocationCoordinates | null>(null);
  const [isConfirming, setIsConfirming] = useState(false);

  // إعادة تعيين الحالة عند فتح النافذة
  useEffect(() => {
    if (isOpen) {
      setSelectedAddress(currentAddress);
      // الاحتفاظ بالإحداثيات الحالية إذا كانت متوفرة
      setSelectedCoordinates(currentCoordinates || null);
      setIsConfirming(false);

      console.log('🗺️ فتح نافذة اختيار العنوان');
      console.log('📍 العنوان الحالي:', currentAddress);
      console.log('📍 الإحداثيات الحالية:', currentCoordinates);
    }
  }, [isOpen, currentAddress, currentCoordinates]);

  // التعامل مع اختيار الموقع من الخريطة
  const handleLocationSelect = (coordinates: LocationCoordinates, address: string) => {
    setSelectedCoordinates(coordinates);
    setSelectedAddress(address);
  };

  // تأكيد اختيار العنوان
  const handleConfirmSelection = async () => {
    if (!selectedAddress.trim()) {
      return;
    }

    setIsConfirming(true);

    try {
      // حفظ العنوان والإحداثيات
      await onAddressSelect(selectedAddress, selectedCoordinates || undefined);
      onClose();
    } catch (error) {
      console.error('خطأ في تأكيد العنوان:', error);
    } finally {
      setIsConfirming(false);
    }
  };

  // إلغاء الاختيار
  const handleCancel = () => {
    setSelectedAddress('');
    setSelectedCoordinates(null);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleCancel}
      title={title}
      size="mobile-full"
      zIndex="highest"
    >
      <div className="flex flex-col h-full">
        {/* معلومات الاستخدام */}
        <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
          <div className="flex items-start gap-3">
            <FiMapPin className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <div className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                كيفية استخدام الخريطة:
              </div>
              <ul className="text-blue-700 dark:text-blue-300 space-y-1">
                <li>• ابحث عن العنوان في شريط البحث أعلاه</li>
                <li>• أو انقر على أي مكان في الخريطة لتحديد الموقع</li>
                <li>• استخدم زر "موقعي الحالي" للانتقال إلى موقعك</li>
                <li>• اضغط "حفظ الموقع" لحفظ العنوان المحدد</li>
              </ul>
            </div>
          </div>
        </div>

        {/* الخريطة التفاعلية */}
        <div className="flex-1 min-h-0">
          <InteractiveMap
            onLocationSelect={handleLocationSelect}
            currentCoordinates={currentCoordinates}
            className="h-full"
          />
        </div>



        {/* أزرار التحكم */}
        <div className="flex gap-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={handleCancel}
            className="flex-1 px-4 py-3 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 rounded-xl transition-colors font-medium"
          >
            <div className="flex items-center justify-center gap-2">
              <FiX className="h-4 w-4" />
              <span>إلغاء</span>
            </div>
          </button>
          
          <button
            onClick={handleConfirmSelection}
            disabled={!selectedAddress.trim() || isConfirming}
            className="flex-1 px-4 py-3 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-300 dark:disabled:bg-gray-600 text-white disabled:text-gray-500 dark:disabled:text-gray-400 rounded-xl transition-colors font-medium disabled:cursor-not-allowed"
          >
            <div className="flex items-center justify-center gap-2">
              {isConfirming ? (
                <FiLoader className="h-4 w-4 animate-spin" />
              ) : (
                <FiCheck className="h-4 w-4" />
              )}
              <span>
                {isConfirming ? 'جاري الحفظ...' : 'حفظ الموقع'}
              </span>
            </div>
          </button>
        </div>

        {/* رسالة تحذيرية */}
        {!selectedAddress && (
          <div className="mt-3 text-center text-sm text-gray-500 dark:text-gray-400">
            يرجى تحديد موقع على الخريطة أو البحث عن عنوان
          </div>
        )}
      </div>
    </Modal>
  );
};

export default AddressPickerModal;
