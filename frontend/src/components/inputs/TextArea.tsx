import React, { useState, forwardRef } from 'react';
import { FaExclamationTriangle, FaCheckCircle } from 'react-icons/fa';

interface TextAreaProps {
  label?: string;
  name: string;
  id?: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  success?: string;
  rows?: number;
  maxLength?: number;
  minLength?: number;
  dir?: 'rtl' | 'ltr';
  className?: string;
  autoFocus?: boolean;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
}

const TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>(({
  label,
  name,
  id,
  value,
  onChange,
  placeholder = '',
  required = false,
  disabled = false,
  error,
  success,
  rows = 3,
  maxLength,
  minLength,
  dir = 'rtl',
  className = '',
  autoFocus = false,
  resize = 'vertical'
}, ref) => {
  const [isFocused, setIsFocused] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);

  // Generate unique ID if not provided
  const textareaId = id || `${name}-${Math.random().toString(36).substring(2, 11)}`;

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
    if (!hasInteracted) {
      setHasInteracted(true);
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
    setHasInteracted(true);
  };

  const showError = error && hasInteracted;
  const showSuccess = success && hasInteracted && !error;
  const isEmpty = !value || value.trim() === '';
  const showRequiredMessage = required && isEmpty && hasInteracted && !error;

  const currentLength = value ? value.length : 0;
  const showCharacterCount = maxLength && maxLength > 0;

  return (
    <div className={`relative ${className}`}>
      {/* Label */}
      {label && (
        <label
          htmlFor={textareaId}
          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
        >
          {label}
          {required && <span className="text-red-500 mr-1">*</span>}
        </label>
      )}

      {/* TextArea Container */}
      <div className="relative">
        {/* TextArea Field */}
        <textarea
          ref={ref}
          id={textareaId}
          name={name}
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          dir={dir}
          rows={rows}
          maxLength={maxLength}
          minLength={minLength}
          autoFocus={autoFocus}
          style={{ resize }}
          className={`
            w-full rounded-xl border-2 py-3 px-4 transition-all duration-200 ease-in-out
            ${disabled 
              ? 'bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400 cursor-not-allowed border-gray-200 dark:border-gray-700' 
              : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
            }
            ${showError
              ? 'border-red-500 focus:border-red-500 focus:ring-4 focus:ring-red-500/20'
              : showSuccess
                ? 'border-green-500 focus:border-green-500 focus:ring-4 focus:ring-green-500/20'
                : isFocused
                  ? 'border-primary-500 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20'
                  : 'border-gray-300/60 dark:border-gray-600/40'
            }
            focus:outline-none
            placeholder:text-gray-400 dark:placeholder:text-gray-500
          `}
        />

        {/* Status Icon */}
        {(showError || showSuccess) && (
          <div className="absolute top-3 left-3">
            {showError ? (
              <FaExclamationTriangle className="h-5 w-5 text-red-500" />
            ) : (
              <FaCheckCircle className="h-5 w-5 text-green-500" />
            )}
          </div>
        )}

        {/* Character Count */}
        {showCharacterCount && (
          <div className="absolute bottom-2 left-3">
            <span className={`text-xs ${
              currentLength > maxLength! * 0.9 
                ? 'text-red-500' 
                : currentLength > maxLength! * 0.7 
                  ? 'text-orange-500' 
                  : 'text-gray-400 dark:text-gray-500'
            }`}>
              {currentLength}/{maxLength}
            </span>
          </div>
        )}
      </div>

      {/* Error Message */}
      {showError && (
        <div className="mt-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-red-700 dark:text-red-300 text-sm flex items-center">
            <FaExclamationTriangle className="ml-2 flex-shrink-0 text-red-500" />
            {error}
          </p>
        </div>
      )}

      {/* Success Message */}
      {showSuccess && (
        <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <p className="text-green-700 dark:text-green-300 text-sm flex items-center">
            <FaCheckCircle className="ml-2 flex-shrink-0 text-green-500" />
            {success}
          </p>
        </div>
      )}

      {/* Required Message */}
      {showRequiredMessage && (
        <div className="mt-2 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
          <p className="text-orange-700 dark:text-orange-300 text-sm flex items-center">
            <FaExclamationTriangle className="ml-2 flex-shrink-0 text-orange-500" />
            هذا الحقل مطلوب، يرجى تعبئته
          </p>
        </div>
      )}
    </div>
  );
});

TextArea.displayName = 'TextArea';

export default TextArea;
