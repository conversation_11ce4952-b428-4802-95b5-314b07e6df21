import React, { useState, forwardRef, useEffect } from 'react';
import { FaExclamationTriangle, FaCheckCircle, FaChevronUp, FaChevronDown } from 'react-icons/fa';
import { numberFormattingService } from '../../services/numberFormattingService';

interface NumberInputProps {
  label?: string;
  name: string;
  id?: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  success?: string;
  icon?: React.ReactNode;
  min?: number;
  max?: number;
  step?: string | number;
  dir?: 'rtl' | 'ltr';
  className?: string;
  currency?: string;
  showControls?: boolean;
  precision?: number;
}

const NumberInput = forwardRef<HTMLInputElement, NumberInputProps>(({
  label,
  name,
  id,
  value,
  onChange,
  placeholder = '0',
  required = false,
  disabled = false,
  error,
  success,
  icon,
  min,
  max,
  step = '0.01',
  dir = 'ltr',
  className = '',
  currency,
  showControls = false,
  precision
}, ref) => {
  const [isFocused, setIsFocused] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);
  const [numberSettings, setNumberSettings] = useState<any>(null);

  // تحميل إعدادات تنسيق الأرقام
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const settings = await numberFormattingService.getCurrentSettings();
        setNumberSettings(settings);
      } catch (error) {
        console.error('Error loading number format settings:', error);
      }
    };
    loadSettings();
  }, []);

  // Generate unique ID if not provided
  const inputId = id || `${name}-${Math.random().toString(36).substring(2, 11)}`;

  // Use settings values as defaults if not provided
  const decimalPlaces = numberSettings?.decimalPlaces || 2;
  const finalPrecision = precision !== undefined ? precision : decimalPlaces;
  const finalCurrency = currency; // Only show currency if explicitly provided

  // دالة لتنسيق الرقم بشكل صحيح
  const formatNumberProperly = (num: number): string => {
    if (isNaN(num) || !isFinite(num)) {
      return '0';
    }

    // التعامل مع الأرقام العلمية والأرقام الطويلة
    const rounded = Math.round(num * Math.pow(10, finalPrecision)) / Math.pow(10, finalPrecision);
    return rounded.toFixed(finalPrecision);
  };

  // تنسيق القيمة المعروضة
  const displayValue = React.useMemo(() => {
    if (!value || value === '') return '';

    // إذا كان المستخدم يكتب أو في حالة تركيز، نعرض القيمة كما هي
    if (isFocused) return value;

    const numValue = parseFloat(value);
    if (isNaN(numValue)) return value;

    // إذا لم يكن في حالة تركيز، نعرض القيمة منسقة
    return formatNumberProperly(numValue);
  }, [value, isFocused, finalPrecision]);



  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let newValue = e.target.value;

    // Allow empty value
    if (newValue === '') {
      onChange('');
      return;
    }

    // Validate number format - allow numbers with decimal points
    const numberRegex = /^-?\d*\.?\d*$/;
    if (numberRegex.test(newValue)) {
      // Check min/max constraints if provided
      const numValue = parseFloat(newValue);
      if (!isNaN(numValue)) {
        if ((min !== undefined && numValue < min) || (max !== undefined && numValue > max)) {
          // Don't update if outside bounds
          return;
        }
      }
      onChange(newValue);
    }

    if (!hasInteracted) {
      setHasInteracted(true);
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
    // لا نغير القيمة عند التركيز، نحتفظ بالتنسيق الحالي
  };

  const handleBlur = () => {
    setIsFocused(false);
    setHasInteracted(true);

    // Format number on blur if valid
    if (value && !isNaN(Number(value))) {
      const numValue = Number(value);
      const formatted = formatNumberProperly(numValue);
      onChange(formatted);
    }
  };

  const handleIncrement = () => {
    const currentValue = Number(value) || 0;
    const stepValue = Number(step);
    const newValue = currentValue + stepValue;

    if (max === undefined || newValue <= max) {
      onChange(formatNumberProperly(newValue));
    }
  };

  const handleDecrement = () => {
    const currentValue = Number(value) || 0;
    const stepValue = Number(step);
    const newValue = currentValue - stepValue;

    if (min === undefined || newValue >= min) {
      onChange(formatNumberProperly(newValue));
    }
  };

  const showError = error && hasInteracted;
  const showSuccess = success && hasInteracted && !error;
  const isEmpty = !value || value.trim() === '';
  const showRequiredMessage = required && isEmpty && hasInteracted && !error;

  return (
    <div className={`relative ${className}`}>
      {/* Label */}
      {label && (
        <label
          htmlFor={inputId}
          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
        >
          {label}
          {required && <span className="text-red-500 mr-1">*</span>}
        </label>
      )}

      {/* Input Container */}
      <div className="relative">
        {/* Currency Symbol */}
        {finalCurrency && (
          <div className={`absolute inset-y-0 flex items-center pointer-events-none z-10 ${
            showControls ? 'left-10 pl-3' : 'left-0 pl-4'
          }`}>
            <span className="text-gray-500 dark:text-gray-400 text-sm font-medium">
              {finalCurrency}
            </span>
          </div>
        )}

        {/* Icon */}
        {icon && !finalCurrency && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none z-10">
            <div className={`transition-colors duration-200 ${
              showError
                ? 'text-red-500'
                : showSuccess
                  ? 'text-green-500'
                  : isFocused
                    ? 'text-primary-500'
                    : 'text-gray-400 dark:text-gray-500'
            }`}>
              {icon}
            </div>
          </div>
        )}

        {/* Input Field */}
        <input
          ref={ref}
          id={inputId}
          type="text"
          inputMode="decimal"
          name={name}
          value={displayValue}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          dir={dir}
          className={`
            number-input-custom w-full rounded-xl border-2 py-3 px-4 transition-all duration-200 ease-in-out
            ${finalCurrency && showControls ? 'pl-20' : finalCurrency ? 'pl-16' : showControls ? 'pl-10' : ''}
            ${icon && !finalCurrency ? 'pr-12' : ''}
            ${disabled
              ? 'bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400 cursor-not-allowed border-gray-200 dark:border-gray-700'
              : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100'
            }
            ${showError
              ? 'border-red-500 focus:border-red-500 focus:ring-4 focus:ring-red-500/20'
              : showSuccess
                ? 'border-green-500 focus:border-green-500 focus:ring-4 focus:ring-green-500/20'
                : isFocused
                  ? 'border-primary-500 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20'
                  : 'border-gray-300/60 dark:border-gray-600/40'
            }
            focus:outline-none
            placeholder:text-gray-400 dark:placeholder:text-gray-500
            font-mono
          `}
        />

        {/* Number Controls */}
        {showControls && !disabled && (
          <div className="absolute left-2 top-1/2 -translate-y-1/2 flex flex-col">
            <button
              type="button"
              onClick={handleIncrement}
              className="w-6 h-4 flex items-center justify-center bg-transparent hover:bg-primary-50 dark:hover:bg-primary-900/30 text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-all duration-200 rounded-t border border-gray-300 dark:border-gray-600 border-b-0 group"
            >
              <FaChevronUp className="h-2 w-2 group-hover:scale-110 transition-transform" />
            </button>
            <button
              type="button"
              onClick={handleDecrement}
              className="w-6 h-4 flex items-center justify-center bg-transparent hover:bg-primary-50 dark:hover:bg-primary-900/30 text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-all duration-200 rounded-b border border-gray-300 dark:border-gray-600 group"
            >
              <FaChevronDown className="h-2 w-2 group-hover:scale-110 transition-transform" />
            </button>
          </div>
        )}

        {/* Status Icon */}
        {(showError || showSuccess) && (
          <div className={`absolute inset-y-0 flex items-center ${
            finalCurrency && showControls
              ? 'left-20'
              : finalCurrency
                ? 'left-16'
                : showControls
                  ? 'left-10'
                  : 'left-3'
          }`}>
            {showError ? (
              <FaExclamationTriangle className="h-5 w-5 text-red-500" />
            ) : (
              <FaCheckCircle className="h-5 w-5 text-green-500" />
            )}
          </div>
        )}
      </div>



      {/* Error Message */}
      {showError && (
        <div className="mt-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-red-700 dark:text-red-300 text-sm flex items-center">
            <FaExclamationTriangle className="ml-2 flex-shrink-0 text-red-500" />
            {error}
          </p>
        </div>
      )}

      {/* Success Message */}
      {showSuccess && (
        <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <p className="text-green-700 dark:text-green-300 text-sm flex items-center">
            <FaCheckCircle className="ml-2 flex-shrink-0 text-green-500" />
            {success}
          </p>
        </div>
      )}

      {/* Required Message */}
      {showRequiredMessage && (
        <div className="mt-2 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
          <p className="text-orange-700 dark:text-orange-300 text-sm flex items-center">
            <FaExclamationTriangle className="ml-2 flex-shrink-0 text-orange-500" />
            هذا الحقل مطلوب، يرجى تعبئته
          </p>
        </div>
      )}
    </div>
  );
});

NumberInput.displayName = 'NumberInput';

export default NumberInput;
