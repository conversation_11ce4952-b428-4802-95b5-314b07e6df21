/**
 * مكون إعدادات الصوت للمحادثة الفورية
 * يوفر واجهة لتخصيص إعدادات الصوت في نظام المحادثة
 */

import React, { useState, useEffect } from 'react';
import {
  FiVolume,
  FiPlay,
  FiSettings,
  FiCheck,
  FiRefreshCw
} from 'react-icons/fi';
import { chatNotificationService } from '../services/chatNotificationService';
import ToggleSwitch from './ToggleSwitch';
import VolumeSlider from './VolumeSlider';
import SoundSelector from './SoundSelector';
import { DEFAULT_SOUND_ID } from '../types/audioTypes';

interface ChatAudioSettingsProps {
  className?: string;
  settings?: Record<string, string>;
  onSettingChange?: (key: string, value: string) => void;
}

const ChatAudioSettings: React.FC<ChatAudioSettingsProps> = ({
  className = '',
  settings: parentSettings,
  onSettingChange
}) => {
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<'success' | 'error' | null>(null);
  const [audioSettings, setAudioSettings] = useState({
    enabled: true,
    volume: 70,
    soundId: DEFAULT_SOUND_ID
  });

  // تحميل الإعدادات الحالية
  useEffect(() => {
    loadAudioSettings();
  }, [parentSettings]);

  const loadAudioSettings = () => {
    if (parentSettings) {
      // استخدام الإعدادات من الصفحة الرئيسية
      setAudioSettings({
        enabled: parentSettings.chat_sound_enabled === 'true',
        volume: parseInt(parentSettings.chat_sound_volume) || 70,
        soundId: parentSettings.chat_sound_id || DEFAULT_SOUND_ID
      });
    } else {
      // تحميل من خدمة الصوت مباشرة
      const currentSettings = chatNotificationService.getAudioSettings();
      setAudioSettings({
        enabled: currentSettings.enabled,
        volume: Math.round(currentSettings.volume * 100),
        soundId: currentSettings.soundId || DEFAULT_SOUND_ID
      });
    }
  };

  const handleEnabledChange = async (enabled: boolean) => {
    const newSettings = { ...audioSettings, enabled };
    setAudioSettings(newSettings);

    // تحديث خدمة الصوت
    try {
      await chatNotificationService.updateAudioSettings({ enabled });
    } catch (error) {
      console.error('خطأ في تحديث إعدادات الصوت:', error);
    }

    // إشعار الصفحة الرئيسية
    if (onSettingChange) {
      onSettingChange('chat_sound_enabled', enabled.toString());
    }
  };

  const handleVolumeChange = async (volume: number) => {
    const clampedVolume = Math.max(0, Math.min(100, volume));
    const newSettings = { ...audioSettings, volume: clampedVolume };
    setAudioSettings(newSettings);

    // تحديث خدمة الصوت (تحويل إلى 0.0-1.0)
    try {
      await chatNotificationService.updateAudioSettings({ volume: clampedVolume / 100 });
    } catch (error) {
      console.error('خطأ في تحديث مستوى الصوت:', error);
    }

    // إشعار الصفحة الرئيسية
    if (onSettingChange) {
      onSettingChange('chat_sound_volume', clampedVolume.toString());
    }

    // تشغيل صوت تجريبي سريع عند التغيير (كل 500ms فقط)
    const now = Date.now();
    if (now - (handleVolumeChange as any).lastPlay > 500) {
      (handleVolumeChange as any).lastPlay = now;
      chatNotificationService.testAudioNotification().catch(() => {
        // تجاهل الأخطاء في التشغيل التجريبي
      });
    }
  };

  const handleSoundChange = async (soundId: string) => {
    const newSettings = { ...audioSettings, soundId };
    setAudioSettings(newSettings);

    // تحديث خدمة الصوت
    try {
      await chatNotificationService.updateAudioSettings({ soundId });
    } catch (error) {
      console.error('خطأ في تحديث نوع الصوت:', error);
    }

    // إشعار الصفحة الرئيسية
    if (onSettingChange) {
      onSettingChange('chat_sound_id', soundId);
    }
  };

  const testSound = async () => {
    setIsTesting(true);
    setTestResult(null);

    try {
      const success = await chatNotificationService.testAudioNotification();
      setTestResult(success ? 'success' : 'error');
    } catch (error) {
      console.error('خطأ في اختبار الصوت:', error);
      setTestResult('error');
    } finally {
      setIsTesting(false);
      
      // إخفاء النتيجة بعد 3 ثوان
      setTimeout(() => setTestResult(null), 3000);
    }
  };



  return (
    <div className={`space-y-6 ${className}`}>
      {/* عنوان القسم */}
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-primary-100 dark:bg-primary-900/30 rounded-lg">
          <FiSettings className="w-5 h-5 text-primary-600 dark:text-primary-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            إعدادات الصوت للمحادثة
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            تخصيص أصوات التنبيه عند وصول رسائل جديدة
          </p>
        </div>
      </div>

      {/* تفعيل/تعطيل الصوت */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-xl border-2 border-gray-200 dark:border-gray-600 h-12 flex items-center transition-all duration-200 hover:border-gray-300 dark:hover:border-gray-500 hover:shadow-md">
        <ToggleSwitch
          id="chatSoundEnabled"
          checked={audioSettings.enabled}
          onChange={handleEnabledChange}
          label="تفعيل أصوات التنبيه"
          className="w-full"
        />
      </div>

      {/* اختيار صوت التنبيه */}
      <div className="space-y-3">
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          نوع صوت التنبيه
        </label>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl border-2 border-gray-200 dark:border-gray-600 transition-all duration-200 hover:border-gray-300 dark:hover:border-gray-500">
          <SoundSelector
            selectedSoundId={audioSettings.soundId}
            onSoundChange={handleSoundChange}
            disabled={!audioSettings.enabled}
            className="w-full"
          />
        </div>
      </div>

      {/* مستوى الصوت */}
      <div className="space-y-3">
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          مستوى الصوت
        </label>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl border-2 border-gray-200 dark:border-gray-600 transition-all duration-200 hover:border-gray-300 dark:hover:border-gray-500">
          <VolumeSlider
            value={audioSettings.volume}
            onChange={handleVolumeChange}
            disabled={!audioSettings.enabled}
            size="lg"
            showIcon={true}
            showValue={true}
            step={5}
            debounceMs={100}
            className="w-full"
          />
        </div>
      </div>

      {/* اختبار الصوت */}
      <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-700/30 p-5 rounded-xl border border-gray-200 dark:border-gray-700 transition-all duration-200 hover:shadow-md">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <FiSettings className="w-4 h-4 text-primary-600 dark:text-primary-400" />
              اختبار الصوت
            </h4>
            <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
              اضغط لتشغيل صوت تجريبي أو حرك شريط التمرير للاستماع
            </p>
          </div>

          <div className="flex items-center gap-3">
            {testResult && (
              <div className={`flex items-center gap-1 text-sm transition-all duration-300 ${
                testResult === 'success'
                  ? 'text-green-600 dark:text-green-400 animate-pulse'
                  : 'text-red-600 dark:text-red-400'
              }`}>
                {testResult === 'success' ? (
                  <>
                    <FiCheck className="w-4 h-4" />
                    <span>تم التشغيل بنجاح</span>
                  </>
                ) : (
                  <>
                    <FiVolume className="w-4 h-4" />
                    <span>فشل في التشغيل</span>
                  </>
                )}
              </div>
            )}

            <button
              onClick={testSound}
              disabled={isTesting || !audioSettings.enabled}
              className={`flex items-center gap-2 px-5 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 transform ${
                audioSettings.enabled
                  ? 'bg-primary-600 hover:bg-primary-700 text-white shadow-md hover:shadow-lg hover:scale-105 active:scale-95'
                  : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed opacity-60'
              }`}
            >
              {isTesting ? (
                <>
                  <FiRefreshCw className="w-4 h-4 animate-spin" />
                  <span>جاري التشغيل...</span>
                </>
              ) : (
                <>
                  <FiPlay className="w-4 h-4" />
                  <span>اختبار الصوت</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* معلومات إضافية */}
      <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800">
        <div className="flex items-start gap-3">
          <div className="p-1 bg-blue-100 dark:bg-blue-900/50 rounded">
            <FiSettings className="w-4 h-4 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="text-sm">
            <h5 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
              ملاحظات مهمة
            </h5>
            <ul className="text-blue-700 dark:text-blue-300 space-y-1 text-xs">
              <li>• سيتم تشغيل الصوت فقط عند وصول رسائل جديدة ونافذة المحادثة مغلقة</li>
              <li>• يمكن تعديل مستوى الصوت في أي وقت</li>
              <li>• الإعدادات محفوظة محلياً في المتصفح</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatAudioSettings;
