import React from 'react';
import { FaTrash, FaExclamationTriangle, FaComments } from 'react-icons/fa';
import Modal from '../Modal';

interface DeleteConversationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  userName: string;
  messageCount?: number;
  isLoading?: boolean;
}

const DeleteConversationModal: React.FC<DeleteConversationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  userName,
  messageCount = 0,
  isLoading = false
}) => {
  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose} 
      title="حذف المحادثة بالكامل" 
      size="md" 
      zIndex="highest"
    >
      <div className="text-center">
        {/* Warning Icon */}
        <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30 mb-6">
          <FaComments className="h-8 w-8 text-red-600 dark:text-red-400" />
        </div>

        {/* Message */}
        <div className="mb-6">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
            هل أنت متأكد من حذف المحادثة؟
          </h4>
          
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
            سيتم حذف المحادثة مع:
          </p>
          
          {/* User Info */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-center gap-3">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                {userName.charAt(0)}
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {userName}
                </p>
                {messageCount > 0 && (
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {messageCount} رسالة
                  </p>
                )}
              </div>
            </div>
          </div>
          
          {/* Warning Details */}
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-4">
            <h5 className="text-sm font-semibold text-red-800 dark:text-red-300 mb-2">
              سيتم تنفيذ العمليات التالية:
            </h5>
            <ul className="text-xs text-red-700 dark:text-red-400 space-y-1 text-right">
              <li>• حذف جميع الرسائل في هذه المحادثة</li>
              <li>• إزالة المحادثة من قائمة المحادثات</li>
              <li>• إشعار المستخدم الآخر بحذف المحادثة</li>
              <li>• فقدان جميع الرسائل والملفات المرسلة</li>
            </ul>
          </div>
          
          <p className="text-xs text-red-600 dark:text-red-400 font-medium">
            ⚠️ هذا الإجراء لا يمكن التراجع عنه!
          </p>
        </div>

        {/* Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-3 justify-center">
          <button
            onClick={onClose}
            disabled={isLoading}
            className="btn-secondary flex items-center justify-center min-w-[120px]"
          >
            <span>إلغاء</span>
          </button>
          <button
            onClick={onConfirm}
            disabled={isLoading}
            className="btn-danger flex items-center justify-center min-w-[140px]"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                <span>جاري الحذف...</span>
              </>
            ) : (
              <>
                <FaTrash className="ml-2" />
                <span>حذف المحادثة</span>
              </>
            )}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteConversationModal;
