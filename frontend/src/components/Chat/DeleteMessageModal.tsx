import React from 'react';
import { FaTrash, FaExclamationTriangle } from 'react-icons/fa';
import Modal from '../Modal';

interface DeleteMessageModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  messageContent: string;
  isLoading?: boolean;
}

const DeleteMessageModal: React.FC<DeleteMessageModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  messageContent,
  isLoading = false
}) => {
  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose} 
      title="حذف الرسالة" 
      size="md" 
      zIndex="highest"
    >
      <div className="text-center">
        {/* Warning Icon */}
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30 mb-4">
          <FaExclamationTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
        </div>

        {/* Message */}
        <div className="mb-6">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
            هل أنت متأكد من حذف هذه الرسالة؟
          </h4>
          
          {/* Message Preview */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-4 max-h-32 overflow-y-auto">
            <p className="text-sm text-gray-900 dark:text-gray-100 break-words text-right">
              {messageContent.length > 100 
                ? `${messageContent.substring(0, 100)}...` 
                : messageContent
              }
            </p>
          </div>
          
          <p className="text-xs text-red-600 dark:text-red-400 font-medium">
            ⚠️ هذا الإجراء لا يمكن التراجع عنه
          </p>
        </div>

        {/* Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-3 justify-center">
          <button
            onClick={onClose}
            disabled={isLoading}
            className="btn-secondary flex items-center justify-center min-w-[120px]"
          >
            <span>إلغاء</span>
          </button>
          <button
            onClick={onConfirm}
            disabled={isLoading}
            className="btn-danger flex items-center justify-center min-w-[120px]"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                <span>جاري الحذف...</span>
              </>
            ) : (
              <>
                <FaTrash className="ml-2" />
                <span>حذف الرسالة</span>
              </>
            )}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteMessageModal;
