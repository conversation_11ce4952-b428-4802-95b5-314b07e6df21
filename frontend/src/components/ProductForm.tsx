import React, { useState, useEffect, useRef } from 'react';
import useProductStore from '../stores/productStore';
import type { Product } from '../stores/productStore';
import { useTheme } from '../contexts/ThemeContext';
import { FaExclamationTriangle, FaTrash, FaPlus, FaEdit, FaCheckCircle, FaBox, FaTag, FaDollarSign, FaRulerCombined } from 'react-icons/fa';
import api from '../lib/axios';
import { TextInput, NumberInput, TextArea, SelectInput, BarcodeInput } from './inputs';
import ToggleSwitch from './ToggleSwitch';

interface ProductFormProps {
  product?: Product;
  onClose: () => void;
}

const ProductForm: React.FC<ProductFormProps> = ({ product, onClose }) => {
  const { createProduct, updateProduct, categories, deleteCategory } = useProductStore();
  useTheme(); // Usar el hook para asegurar que los estilos dark mode se apliquen correctamente
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showNewCategoryInput, setShowNewCategoryInput] = useState(false);
  const [newCategory, setNewCategory] = useState('');
  const [categoryError, setCategoryError] = useState<string | null>(null);
  const [barcodeError, setBarcodeError] = useState<string | null>(null);
  const [barcodeChecking, setBarcodeChecking] = useState(false);
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({});
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [hasInteracted] = useState(false);
  const newCategoryInputRef = useRef<HTMLInputElement>(null);

  const [formData, setFormData] = useState({
    name: '',
    barcode: '',
    description: '',
    price: '',
    cost_price: '',
    quantity: '0',
    min_quantity: '0',
    category: '',
    unit: '',
    is_active: true
  });

  // دالة توليد باركود عشوائي
  const generateBarcode = () => {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${timestamp}${random}`;
  };

  // دالة التحقق من وجود الباركود
  const checkBarcodeExists = async (barcode: string) => {
    if (!barcode.trim()) {
      setBarcodeError(null);
      return;
    }

    setBarcodeChecking(true);
    setBarcodeError(null);

    try {
      const response = await api.get(`/api/products/check-barcode/${barcode}`);
      if (response.data.exists) {
        setBarcodeError(`هذا الباركود مستخدم بالفعل في المنتج "${response.data.product.name}". يرجى توليد باركود جديد أو استخدام باركود مختلف.`);
      } else {
        // الباركود متاح للاستخدام
        setSuccessMessage('الباركود متاح ويمكن استخدامه');
        setTimeout(() => setSuccessMessage(null), 2000);
      }
    } catch (error) {
      console.error('Error checking barcode:', error);
      setBarcodeError('حدث خطأ أثناء التحقق من الباركود. يرجى المحاولة مرة أخرى.');
    } finally {
      setBarcodeChecking(false);
    }
  };

  // توليد باركود تلقائي عند فتح النافذة للمنتج الجديد
  useEffect(() => {
    if (!product) {
      const newBarcode = generateBarcode();
      setFormData(prev => ({ ...prev, barcode: newBarcode }));
    }
  }, [product]);

  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name,
        barcode: product.barcode || '',
        description: product.description || '',
        price: product.price.toString(),
        cost_price: product.cost_price.toString(),
        quantity: product.quantity.toString(),
        min_quantity: product.min_quantity.toString(),
        category: product.category || '',
        unit: product.unit,
        is_active: product.is_active
      });
    }
  }, [product]);

  const handleDeleteCategory = async (category: string) => {
    if (!category) return;

    try {
      if (window.confirm(`هل أنت متأكد من حذف الفئة "${category}"؟`)) {
        await deleteCategory(category);
        if (formData.category === category) {
          setFormData(prev => ({
            ...prev,
            category: ''
          }));
        }
      }
    } catch (err: any) {
      // Check if this is the specific error about products in category
      if (err?.response?.status === 400 &&
          err?.response?.data?.detail?.includes("Cannot delete category that has products")) {
        setError("لا يمكن حذف هذه الفئة لأنها تحتوي على منتجات. يرجى نقل المنتجات إلى فئة أخرى أو حذفها أولاً.");
      } else {
        setError('فشل في حذف الفئة. يرجى المحاولة مرة أخرى.');
      }
    }
  };

  const validateForm = () => {
    const errors: {[key: string]: string} = {};

    // التحقق من اسم المنتج
    if (!formData.name.trim()) {
      errors.name = 'يرجى إدخال اسم المنتج';
    } else if (formData.name.trim().length < 2) {
      errors.name = 'اسم المنتج يجب أن يكون أكثر من حرفين';
    }

    // التحقق من الباركود
    if (!formData.barcode.trim()) {
      errors.barcode = 'يرجى إدخال الباركود أو توليد باركود جديد';
    } else if (barcodeError) {
      errors.barcode = barcodeError;
    }

    // التحقق من سعر البيع
    if (!formData.price || formData.price.trim() === '') {
      errors.price = 'يرجى إدخال سعر البيع';
    } else if (isNaN(Number(formData.price)) || Number(formData.price) <= 0) {
      errors.price = 'سعر البيع يجب أن يكون رقماً أكبر من الصفر';
    }

    // التحقق من تكلفة الشراء
    if (!formData.cost_price || formData.cost_price.trim() === '') {
      errors.cost_price = 'يرجى إدخال تكلفة الشراء';
    } else if (isNaN(Number(formData.cost_price)) || Number(formData.cost_price) < 0) {
      errors.cost_price = 'تكلفة الشراء يجب أن تكون رقماً صحيحاً';
    }

    // التحقق من أن سعر البيع أكبر من أو يساوي تكلفة الشراء
    if (formData.price && formData.cost_price &&
        Number(formData.price) < Number(formData.cost_price)) {
      errors.price = 'سعر البيع لا يمكن أن يكون أقل من تكلفة الشراء';
    }

    // التحقق من الفئة
    if (!formData.category && !showNewCategoryInput) {
      errors.category = 'يرجى اختيار فئة المنتج';
    } else if (showNewCategoryInput && !newCategory.trim()) {
      errors.category = 'يرجى إدخال اسم الفئة الجديدة';
    }

    // التحقق من وحدة القياس
    if (!formData.unit) {
      errors.unit = 'يرجى اختيار وحدة القياس';
    }

    setValidationErrors(errors);

    if (Object.keys(errors).length > 0) {
      setError('يرجى تصحيح البيانات المطلوبة أدناه لإتمام عملية الحفظ');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      // Prepare the product data
      let finalCategory = formData.category;

      // If there's a new category input shown and it has a value
      if (showNewCategoryInput && newCategory.trim()) {
        finalCategory = newCategory.trim();
      }

      // Create the product data with the final category
      const productData = {
        ...formData,
        category: finalCategory, // Use the final category
        price: Number(formData.price),
        cost_price: Number(formData.cost_price),
        quantity: Number(formData.quantity),
        min_quantity: Number(formData.min_quantity)
      };

      // Save the product first
      if (product) {
        await updateProduct(product.id, productData);
      } else {
        try {
          // Try to create the product using the regular endpoint
          await createProduct(productData);
        } catch (createError: any) {
          console.error('Error creating product:', createError);
          if (createError?.response?.status === 400 && createError?.response?.data?.detail?.includes('barcode already exists')) {
            setError('الباركود المدخل موجود بالفعل. يرجى توليد باركود جديد أو استخدام باركود مختلف.');
          } else {
            setError('حدث خطأ أثناء حفظ المنتج. يرجى التحقق من البيانات والمحاولة مرة أخرى.');
          }
          setLoading(false);
          return;
        }
      }

      // Reset states
      if (showNewCategoryInput) {
        setNewCategory('');
        setShowNewCategoryInput(false);
        setCategoryError(null);
      }

      onClose();
    } catch (err: any) {
      console.error('Error in form submission:', err);
      if (err?.response?.status === 400) {
        setError('البيانات المدخلة غير صحيحة. يرجى مراجعة جميع الحقول والمحاولة مرة أخرى.');
      } else if (err?.response?.status === 500) {
        setError('حدث خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً.');
      } else {
        setError('حدث خطأ غير متوقع. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-2">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <FaExclamationTriangle className="h-5 w-5 text-red-500 mt-0.5" />
            </div>
            <div className="mr-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-300">
                تنبيه: يوجد أخطاء في النموذج
              </h3>
              <p className="mt-1 text-sm text-red-700 dark:text-red-400">
                {error}
              </p>
            </div>
          </div>
        </div>
      )}

      {successMessage && (
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4 mb-2">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <FaCheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
            </div>
            <div className="mr-3">
              <h3 className="text-sm font-medium text-green-800 dark:text-green-300">
                تم بنجاح
              </h3>
              <p className="mt-1 text-sm text-green-700 dark:text-green-400">
                {successMessage}
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <TextInput
          label="اسم المنتج"
          name="name"
          value={formData.name}
          onChange={(value) => {
            setFormData(prev => ({ ...prev, name: value }));
            if (validationErrors.name) {
              setValidationErrors(prev => ({ ...prev, name: '' }));
            }
          }}
          placeholder="أدخل اسم المنتج"
          required
          error={validationErrors.name}
          icon={<FaBox />}
          maxLength={100}
        />

        <BarcodeInput
          label="الباركود"
          name="barcode"
          value={formData.barcode}
          onChange={(value) => {
            setFormData(prev => ({ ...prev, barcode: value }));
            if (validationErrors.barcode) {
              setValidationErrors(prev => ({ ...prev, barcode: '' }));
            }
            setBarcodeError(null);

            // التحقق من الباركود عند تغييره
            if (value.trim()) {
              setTimeout(() => {
                checkBarcodeExists(value);
              }, 500);
            }
          }}
          onGenerate={generateBarcode}
          placeholder="أدخل الباركود أو اضغط توليد"
          required
          error={validationErrors.barcode || barcodeError || undefined}
          success={!barcodeError && formData.barcode && hasInteracted ? 'الباركود متاح للاستخدام' : undefined}
          validating={barcodeChecking}
          showGenerateButton={true}
        />

        <div className="md:col-span-2">
          <TextArea
            label="وصف المنتج"
            name="description"
            value={formData.description}
            onChange={(value) => setFormData(prev => ({ ...prev, description: value }))}
            placeholder="أدخل وصف المنتج (اختياري)"
            rows={3}
            maxLength={500}
          />
        </div>

        <NumberInput
          label="سعر البيع"
          name="price"
          value={formData.price}
          onChange={(value) => {
            setFormData(prev => ({ ...prev, price: value }));
            if (validationErrors.price) {
              setValidationErrors(prev => ({ ...prev, price: '' }));
            }
          }}
          placeholder="0.00"
          required
          error={validationErrors.price}
          currency="د.ل"
          min={0}
          step="0.01"
          precision={2}
          icon={<FaDollarSign />}
        />

        <NumberInput
          label="تكلفة الشراء"
          name="cost_price"
          value={formData.cost_price}
          onChange={(value) => {
            setFormData(prev => ({ ...prev, cost_price: value }));
            if (validationErrors.cost_price) {
              setValidationErrors(prev => ({ ...prev, cost_price: '' }));
            }
          }}
          placeholder="0.00"
          required
          error={validationErrors.cost_price}
          currency="د.ل"
          min={0}
          step="0.01"
          precision={2}
          icon={<FaDollarSign />}
        />

        <NumberInput
          label="المخزون الحالي"
          name="quantity"
          value={formData.quantity}
          onChange={(value) => setFormData(prev => ({ ...prev, quantity: value }))}
          placeholder="0"
          min={0}
          step="1"
          precision={0}
          icon={<FaBox />}
          showControls={true}
        />

        <NumberInput
          label="الحد الأدنى للمخزون"
          name="min_quantity"
          value={formData.min_quantity}
          onChange={(value) => setFormData(prev => ({ ...prev, min_quantity: value }))}
          placeholder="0"
          min={0}
          step="1"
          precision={0}
          icon={<FaExclamationTriangle />}
          showControls={true}
        />

        <div>
          {showNewCategoryInput ? (
            <div className="space-y-3">
              <TextInput
                label="الفئة الجديدة"
                name="newCategory"
                value={newCategory}
                onChange={(value) => {
                  setNewCategory(value);
                  if (validationErrors.category) {
                    setValidationErrors(prev => ({ ...prev, category: '' }));
                  }
                }}
                placeholder="أدخل اسم الفئة الجديدة"
                required
                error={validationErrors.category || categoryError || undefined}
                icon={<FaTag />}
                autoFocus
              />
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={() => {
                    setShowNewCategoryInput(false);
                    setNewCategory('');
                    setCategoryError(null);
                  }}
                  className="btn-secondary px-4 py-2 text-sm"
                >
                  إلغاء
                </button>
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400 bg-blue-50 dark:bg-blue-900/20 p-2 rounded-lg">
                💡 سيتم إضافة الفئة الجديدة تلقائيًا عند حفظ المنتج
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="space-y-2">
                <SelectInput
                  label="الفئة"
                  name="category"
                  value={formData.category}
                  onChange={(value) => {
                    if (value === 'new') {
                      setShowNewCategoryInput(true);
                      setTimeout(() => {
                        if (newCategoryInputRef.current) {
                          newCategoryInputRef.current.focus();
                        }
                      }, 0);
                      return;
                    }
                    setFormData(prev => ({ ...prev, category: value }));
                    if (validationErrors.category) {
                      setValidationErrors(prev => ({ ...prev, category: '' }));
                    }
                  }}
                  options={[
                    ...categories.map(category => ({ value: category, label: category })),
                    { value: 'new', label: '+ إضافة فئة جديدة' }
                  ]}
                  placeholder="اختر الفئة"
                  required
                  error={validationErrors.category}
                  icon={<FaTag />}
                />

                {formData.category && formData.category !== 'new' && (
                  <div className="flex justify-end">
                    <button
                      type="button"
                      onClick={() => handleDeleteCategory(formData.category)}
                      className="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg hover:bg-red-100 dark:hover:bg-red-900/30 hover:text-red-700 dark:hover:text-red-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                      title="حذف الفئة المحددة"
                    >
                      <FaTrash className="h-3 w-3" />
                      <span>حذف الفئة</span>
                    </button>
                  </div>
                )}
              </div>
              {categories.length > 0 && (
                <div className="text-xs text-gray-500 dark:text-gray-400 bg-orange-50 dark:bg-orange-900/20 p-2 rounded-lg">
                  ⚠️ يمكنك حذف الفئة فقط إذا لم تكن تحتوي على منتجات
                </div>
              )}
            </div>
          )}
        </div>

        <SelectInput
          label="وحدة القياس"
          name="unit"
          value={formData.unit}
          onChange={(value) => {
            setFormData(prev => ({ ...prev, unit: value }));
            if (validationErrors.unit) {
              setValidationErrors(prev => ({ ...prev, unit: '' }));
            }
          }}
          options={[
            { value: 'piece', label: 'قطعة' },
            { value: 'kg', label: 'كيلوجرام' },
            { value: 'g', label: 'جرام' },
            { value: 'l', label: 'لتر' },
            { value: 'ml', label: 'مليلتر' },
            { value: 'box', label: 'صندوق' },
            { value: 'pack', label: 'عبوة' }
          ]}
          placeholder="اختر وحدة القياس"
          required
          error={validationErrors.unit}
          icon={<FaRulerCombined />}
        />

        <div className="md:col-span-2">
          <div className="p-6 bg-gray-50 dark:bg-gray-700/50 rounded-xl border-2 border-gray-200 dark:border-gray-600">
            <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-12 flex items-center mb-3">
              <ToggleSwitch
                id="is_active"
                checked={formData.is_active}
                onChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                label="المنتج نشط ومتاح للبيع"
                className="w-full"
              />
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              💡 المنتجات النشطة فقط تظهر في نقطة البيع ويمكن بيعها
            </p>
          </div>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-3 sm:gap-3 justify-center pt-4 border-t border-gray-200 dark:border-gray-700">
        <button
          type="button"
          onClick={onClose}
          disabled={loading}
          className="btn-secondary flex items-center justify-center min-w-[120px]"
        >
          <span>إلغاء</span>
        </button>
        <button
          type="submit"
          disabled={loading}
          className="btn-primary flex items-center justify-center min-w-[140px]"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
              <span>جاري الحفظ...</span>
            </>
          ) : (
            <>
              {product ? <FaEdit className="ml-2" /> : <FaPlus className="ml-2" />}
              <span>{product ? 'تحديث المنتج' : 'إضافة المنتج'}</span>
            </>
          )}
        </button>
      </div>
    </form>
  );
};

export default ProductForm;