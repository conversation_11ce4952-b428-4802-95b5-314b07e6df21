import React from 'react';

interface ToggleSwitchProps {
  id: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  label: string;
  className?: string;
}

const ToggleSwitch: React.FC<ToggleSwitchProps> = ({
  id,
  checked,
  onChange,
  label,
  className = ''
}) => {
  return (
    <div className={`flex items-center justify-between ${className}`}>
      <label className="inline-flex items-center cursor-pointer w-full justify-between group">
        {label && (
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-gray-100 transition-colors duration-200">
            {label}
          </span>
        )}
        <input
          type="checkbox"
          id={id}
          checked={checked}
          onChange={(e) => onChange(e.target.checked)}
          className="sr-only peer"
        />
        <div className={`
          relative w-12 h-7 rounded-full transition-all duration-200 ease-in-out shadow-inner
          ${checked
            ? 'bg-primary-600 dark:bg-primary-500 shadow-primary-500/20'
            : 'bg-gray-200 dark:bg-gray-600 shadow-gray-400/20'
          }
          peer-focus:outline-none peer-focus:ring-4
          ${checked
            ? 'peer-focus:ring-primary-500/20 dark:peer-focus:ring-primary-400/20'
            : 'peer-focus:ring-gray-300/50 dark:peer-focus:ring-gray-500/50'
          }
          hover:shadow-lg
          ${checked
            ? 'hover:bg-primary-700 dark:hover:bg-primary-400'
            : 'hover:bg-gray-300 dark:hover:bg-gray-500'
          }
          after:content-[''] after:absolute after:top-[3px] after:start-[3px]
          after:bg-white dark:after:bg-gray-100 after:border-2 after:rounded-full
          after:h-5 after:w-5 after:transition-all after:duration-200 after:ease-in-out
          after:shadow-md
          ${checked
            ? 'after:translate-x-full rtl:after:-translate-x-full after:border-primary-200/60 dark:after:border-primary-300/60'
            : 'after:border-gray-300/60 dark:after:border-gray-400/60'
          }
          hover:after:shadow-lg
        `}></div>
      </label>
    </div>
  );
};

export default ToggleSwitch;
