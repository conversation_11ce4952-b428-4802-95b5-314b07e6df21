import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { <PERSON>a<PERSON>ser, FaPlus, FaSearch, FaTimes } from 'react-icons/fa';
import api from '../lib/axios';

interface Customer {
  id: number;
  name: string;
  phone?: string;
  email?: string;
  total_debt: number;
  is_active: boolean;
}

interface CustomerSelectorProps {
  selectedCustomer: Customer | null;
  onCustomerSelect: (customer: Customer | null) => void;
  onAddCustomer?: () => void;
}

export interface CustomerSelectorRef {
  refreshCustomers: () => void;
}

const CustomerSelector = forwardRef<CustomerSelectorRef, CustomerSelectorProps>(({
  selectedCustomer,
  onCustomerSelect,
  onAddCustomer
}, ref) => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [totalCustomers, setTotalCustomers] = useState(0);

  useEffect(() => {
    fetchCustomers();
  }, []);

  // Expose refresh function to parent component
  useImperativeHandle(ref, () => ({
    refreshCustomers: fetchCustomers
  }));

  const fetchCustomers = async (page = 1, isLoadMore = false, search = '') => {
    try {
      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
        setCurrentPage(1);
        setHasMore(true);
      }

      const limit = 10; // Load 10 customers at a time
      const skip = (page - 1) * limit;

      const response = await api.get('/api/customers', {
        params: {
          active_only: true,
          skip: skip,
          limit: limit,
          search: search || searchTerm
        }
      });

      const newCustomers = response.data || [];
      console.log(`CustomerSelector: Fetched ${newCustomers.length} customers for page ${page}`);

      // Check if backend provides total count in headers
      let hasMoreData = newCustomers.length === limit; // Fallback logic
      let totalCount = 0;

      if (response.headers['x-total-count']) {
        // Use server-provided total count for accurate pagination
        totalCount = parseInt(response.headers['x-total-count']);
        const totalLoaded = isLoadMore ? (page * limit) : limit;
        hasMoreData = totalLoaded < totalCount;
        setTotalCustomers(totalCount);
        console.log(`CustomerSelector: Total count from server: ${totalCount}, hasMore: ${hasMoreData}`);
      } else {
        // Fallback: estimate based on response size
        setTotalCustomers(prev => isLoadMore ? prev + newCustomers.length : newCustomers.length);
        console.log(`CustomerSelector: Using fallback logic, hasMore: ${hasMoreData}`);
      }

      setHasMore(hasMoreData);

      if (isLoadMore) {
        setCustomers(prev => [...prev, ...newCustomers]);
        setCurrentPage(page);
      } else {
        setCustomers(newCustomers);
        setCurrentPage(1);
      }
    } catch (error) {
      console.error('Error fetching customers:', error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const fetchDefaultCustomer = async () => {
    try {
      const response = await api.get('/api/customers/default/direct');
      return response.data;
    } catch (error) {
      console.error('Error fetching default customer:', error);
      return null;
    }
  };

  const loadMoreCustomers = () => {
    console.log(`CustomerSelector: loadMoreCustomers called - hasMore: ${hasMore}, loadingMore: ${loadingMore}, currentPage: ${currentPage}`);
    if (hasMore && !loadingMore) {
      console.log(`CustomerSelector: Loading page ${currentPage + 1}`);
      fetchCustomers(currentPage + 1, true, searchTerm);
    }
  };

  // Handle search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchCustomers(1, false, searchTerm);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Since we're now filtering on the server side, we just need to filter out the direct customer
  const filteredCustomers = customers.filter(customer => {
    // تصفية "عميل مباشر" من القائمة العادية لأنه يظهر كخيار افتراضي
    return !(customer.name === 'عميل مباشر' || customer.name.toLowerCase() === 'direct customer');
  });

  // Handle scroll to load more customers
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

    // If scrolled to bottom (with a threshold of 50px)
    if (scrollHeight - scrollTop - clientHeight < 50) {
      loadMoreCustomers();
    }
  };

  const handleCustomerSelect = (customer: Customer) => {
    onCustomerSelect(customer);
    setIsDropdownOpen(false);
    setSearchTerm('');
  };

  const handleClearSelection = () => {
    onCustomerSelect(null);
    setSearchTerm('');
  };

  const handleSelectDefaultCustomer = async () => {
    const defaultCustomer = await fetchDefaultCustomer();
    if (defaultCustomer) {
      onCustomerSelect(defaultCustomer);
      setIsDropdownOpen(false);
      setSearchTerm('');
    }
  };

  return (
    <div className="relative">
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        العميل
      </label>

      {selectedCustomer ? (
        // Selected customer display
        <div className="flex items-center justify-between p-3 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-700 rounded-lg">
          <div className="flex items-center">
            <div className="flex-shrink-0 h-8 w-8 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center">
              <FaUser className="text-primary-600 dark:text-primary-400 text-sm" />
            </div>
            <div className="mr-3">
              <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {selectedCustomer.name}
              </div>
              {selectedCustomer.phone && (
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {selectedCustomer.phone}
                </div>
              )}
              {selectedCustomer.total_debt > 0 && (
                <div className="text-xs text-red-600 dark:text-red-400">
                  مديونية: {selectedCustomer.total_debt.toFixed(2)} د.ل
                </div>
              )}
            </div>
          </div>
          <button
            onClick={handleClearSelection}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            title="إلغاء الاختيار"
          >
            <FaTimes />
          </button>
        </div>
      ) : (
        // Customer selection interface
        <div>
          <div className="relative">
            <input
              type="text"
              placeholder="البحث عن عميل أو اختيار عميل جديد..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setIsDropdownOpen(true);
              }}
              onFocus={() => setIsDropdownOpen(true)}
              className="input-field pr-10"
            />
            <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>

          {/* Dropdown */}
          {isDropdownOpen && (
            <div className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg overflow-hidden">
              {loading ? (
                <div className="p-4 text-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mx-auto"></div>
                </div>
              ) : (
                <>
                  {/* Default customer option */}
                  <button
                    onClick={handleSelectDefaultCustomer}
                    className="w-full px-4 py-3 text-right hover:bg-green-50 dark:hover:bg-green-900/20 border-b border-gray-200 dark:border-gray-700 text-green-600 dark:text-green-400"
                  >
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                        <FaUser className="text-green-600 dark:text-green-400 text-sm" />
                      </div>
                      <div className="mr-3">
                        <div className="font-medium">عميل مباشر</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">للمبيعات النقدية المباشرة</div>
                      </div>
                    </div>
                  </button>

                  {/* Add new customer option */}
                  {onAddCustomer && (
                    <button
                      onClick={() => {
                        onAddCustomer();
                        setIsDropdownOpen(false);
                      }}
                      className="w-full px-4 py-3 text-right hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-700 flex items-center"
                    >
                      <FaPlus className="text-primary-600 dark:text-primary-400 ml-3" />
                      <span className="text-primary-600 dark:text-primary-400 font-medium">
                        إضافة عميل جديد
                      </span>
                    </button>
                  )}

                  {/* Customer list with scroll */}
                  <div
                    className="max-h-60 overflow-y-auto custom-scrollbar-thin"
                    onScroll={handleScroll}
                  >
                    {loading && filteredCustomers.length === 0 ? (
                      <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                        <div className="animate-spin h-5 w-5 border-2 border-gray-300 border-t-primary-500 rounded-full mx-auto mb-2"></div>
                        جاري التحميل...
                      </div>
                    ) : filteredCustomers.length === 0 ? (
                      <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                        {searchTerm ? 'لا توجد نتائج' : 'لا توجد عملاء'}
                      </div>
                    ) : (
                      <>
                        {filteredCustomers.map((customer) => (
                          <button
                            key={customer.id}
                            onClick={() => handleCustomerSelect(customer)}
                            className="w-full px-4 py-3 text-right hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-700 last:border-b-0"
                          >
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-8 w-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                                <FaUser className="text-gray-600 dark:text-gray-400 text-sm" />
                              </div>
                              <div className="mr-3 flex-1">
                                <div className="text-sm font-medium text-gray-900 dark:text-gray-100 text-right">
                                  {customer.name}
                                </div>
                                <div className="flex justify-between items-center">
                                  {customer.phone && (
                                    <span className="text-xs text-gray-500 dark:text-gray-400">
                                      {customer.phone}
                                    </span>
                                  )}
                                  {customer.total_debt > 0 && (
                                    <span className="text-xs text-red-600 dark:text-red-400">
                                      مديونية: {customer.total_debt.toFixed(2)} د.ل
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                          </button>
                        ))}

                        {/* Load more indicator */}
                        {loadingMore && (
                          <div className="p-3 text-center text-gray-500 dark:text-gray-400">
                            <div className="animate-spin h-4 w-4 border-2 border-gray-300 border-t-primary-500 rounded-full mx-auto mb-1"></div>
                            <span className="text-xs">جاري تحميل المزيد...</span>
                          </div>
                        )}

                        {/* End of list indicator */}
                        {!hasMore && filteredCustomers.length > 0 && (
                          <div className="p-2 text-center text-xs text-gray-400 dark:text-gray-500">
                            تم عرض جميع العملاء ({totalCustomers})
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </>
              )}
            </div>
          )}
        </div>
      )}

      {/* Backdrop to close dropdown */}
      {isDropdownOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsDropdownOpen(false)}
        />
      )}
    </div>
  );
});

CustomerSelector.displayName = 'CustomerSelector';

export default CustomerSelector;
