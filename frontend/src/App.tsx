import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Suspense, lazy, useEffect, useState, useRef } from 'react';
import { Toaster } from 'react-hot-toast';
import { useAuthStore } from './stores/authStore';
import { ThemeProvider } from './contexts/ThemeContext';
import LoadingSpinner from './components/LoadingSpinner';
import ErrorBoundary from './components/ErrorBoundary';
import errorLogger from './services/errorLogger';
import unifiedConnectionService from './services/unifiedConnectionService';
import { urlDetectionService } from './services/urlDetectionService';
// Device block check functions will be imported dynamically

// Lazy load components with preload hints
const Login = lazy(() => import('./pages/Login'));
const Dashboard = lazy(() => import('./pages/Dashboard'));
const Layout = lazy(() => import('./components/Layout'));
const NewLayout = lazy(() => import('./components/NewLayout'));
const POS = lazy(() => import('./pages/POS'));
const Products = lazy(() => import('./pages/Products'));
const Sales = lazy(() => import('./pages/Sales'));
const Users = lazy(() => import('./pages/Users'));
const Settings = lazy(() => import('./pages/Settings'));
const Receipt = lazy(() => import('./pages/Receipt'));
const Reports = lazy(() => import('./pages/Reports'));
const Customers = lazy(() => import('./pages/Customers'));
const Debts = lazy(() => import('./pages/Debts'));
const HelpCenter = lazy(() => import('./pages/HelpCenter'));
// const TestErrorBoundary = lazy(() => import('./pages/TestErrorBoundary')); // تم حذف الملف

// Import DeviceBlocked and DevicePendingApproval directly (not lazy) for immediate blocking
import DeviceBlocked from './pages/DeviceBlocked';
import DevicePendingApproval from './pages/DevicePendingApproval';

// Loading component
const Loading = () => <LoadingSpinner size="md" message="تحميل..." fullScreen />;

// Protected Route component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated, isInitialized, user } = useAuthStore();

  console.log('ProtectedRoute - Auth state:', { isAuthenticated, isInitialized, user: !!user });

  if (!isInitialized) {
    console.log('ProtectedRoute - Not initialized, showing loading');
    return <Loading />;
  }

  if (!isAuthenticated) {
    console.log('ProtectedRoute - Not authenticated, redirecting to login');
    return <Navigate to="/login" replace />;
  }

  console.log('ProtectedRoute - Authenticated, rendering children');
  return <>{children}</>;
};

// Admin Route component
const AdminRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isAuthenticated, isInitialized } = useAuthStore();

  if (!isInitialized) {
    return <Loading />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (user?.role !== 'admin') {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
};

function App() {
  const { initialize, isInitialized } = useAuthStore();
  const [isDeviceBlocked, setIsDeviceBlocked] = useState(false);
  const [isDevicePending, setIsDevicePending] = useState(false);
  const [isCheckingDevice, setIsCheckingDevice] = useState(true);
  const hasChecked = useRef(false);

  useEffect(() => {
    // منع التحميل المتكرر باستخدام ref بدلاً من state
    if (hasChecked.current) {
      console.log('Device check already performed, skipping...');
      return;
    }

    console.log('App component mounted, checking device status...');
    hasChecked.current = true;

    // بدء خدمة الاتصال الموحدة
    console.log('🔍 Starting unified connection service...');
    // الخدمة تبدأ المراقبة تلقائياً عند الاستيراد

    // فحص حظر الجهاز وانتظار الموافقة أولاً
    const checkDevice = async () => {
      try {
        // فحص مبكر للتأكد من أن الجهاز يمكنه الوصول إلى backend
        const backendUrl = await urlDetectionService.getBackendURL();

        console.log('🔗 محاولة الاتصال بـ backend:', backendUrl);

        // محاولة الوصول إلى backend أولاً للتحقق من حالة الجهاز
        try {
          // استخدام api بدلاً من fetch لضمان إرسال البصمة
          const { default: api } = await import('./lib/axios');
          const testResponse = await api.get('/api/device/status');

          console.log('📡 استجابة backend:', testResponse.status);

          // إذا كانت الاستجابة 200، فحص البيانات
          if (testResponse.status === 200) {
            const deviceData = testResponse.data;
            console.log('📊 Device data from backend:', deviceData);

            if (deviceData.status === 'pending_approval') {
              console.log('⏳ الجهاز في انتظار الموافقة، عرض صفحة انتظار الموافقة...');
              console.log('📊 بيانات الجهاز:', deviceData);
              setIsDevicePending(true);
              setIsCheckingDevice(false);
              return; // توقف هنا ولا تستمر
            } else if (deviceData.status === 'allowed') {
              console.log('✅ الجهاز معتمد، السماح بالوصول...');
              setIsCheckingDevice(false);
              // تهيئة التطبيق للجهاز المعتمد
              initialize();
              return; // توقف هنا ولا تستمر
            } else if (deviceData.status === 'blocked') {
              console.log('🚫 الجهاز محظور، عرض صفحة الحظر...');
              setIsDeviceBlocked(true);
              setIsCheckingDevice(false);
              return; // توقف هنا ولا تستمر
            } else {
              console.log('⚠️ حالة جهاز غير معروفة:', deviceData.status);
              console.log('📊 بيانات الجهاز الكاملة:', deviceData);
            }
          }

        } catch (backendError: any) {
          console.warn('⚠️ لا يمكن الوصول إلى backend مباشرة:', backendError);

          // إذا كانت الاستجابة 403، فهذا يعني أن الجهاز في انتظار الموافقة
          if (backendError.response?.status === 403) {
            console.log('🔒 الجهاز في انتظار الموافقة (من catch)، عرض صفحة انتظار الموافقة...');
            const deviceData = backendError.response?.data;
            if (deviceData) {
              console.log('📊 Device data from backend (403):', deviceData);
            }
            setIsDevicePending(true);
            setIsCheckingDevice(false);
            return; // توقف هنا ولا تستمر
          }

          // إذا كان خطأ آخر، سجله واستمر
          console.error('❌ خطأ غير متوقع من backend:', backendError);
        }

        // Dynamic import to avoid TypeScript issues
        const { checkDeviceBlocked } = await import('./services/deviceBlockCheck');
        const deviceStatus = await checkDeviceBlocked();

        if (deviceStatus.isBlocked) {
          setIsDeviceBlocked(true);
          setIsCheckingDevice(false);
          return;
        }
        if (deviceStatus.isPending) {
          console.log('🔍 Device is pending approval, showing React page...');
          setIsDevicePending(true);
          setIsCheckingDevice(false);
          return;
        }
      } catch (error) {
        console.error('خطأ في فحص حالة الجهاز:', error);

        // التحقق من نوع الخطأ
        if (error instanceof Error) {
          // إذا كان خطأ 403 (Forbidden) - قد يكون الجهاز في انتظار الموافقة
          if (error.message.includes('403') || error.message.includes('Forbidden')) {
            console.log('🔍 Received 403 error - device might be pending approval, showing React page...');
            setIsDevicePending(true);
            setIsCheckingDevice(false);
            return;
          }

          // في حالة خطأ الشبكة، إعادة تعيين cache والمحاولة مرة أخرى
          if (error.message.includes('Failed to fetch') || error.message.includes('Network Error')) {
            console.log('🔄 Network error, clearing cache and retrying...');
            // إعادة تعيين cache عند فشل الاتصال
            localStorage.removeItem('lastDeviceCheck');
            localStorage.removeItem('deviceStatus');
            setTimeout(() => {
              checkDevice();
            }, 2000);
            return;
          }
        }
      }

      setIsCheckingDevice(false);

      // Initialize auth after device check
      initialize();

      // Initialize error logging
      errorLogger.logInfo('Application started', {
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      });

      // لا نحتاج تحديث دوري للجهاز في صفحة انتظار الموافقة
    };

    // التحقق من المسار الحالي - إذا كان في صفحة انتظار الموافقة، لا نحتاج لفحص إضافي
    if (window.location.pathname === '/device-pending-approval') {
      console.log('Already on pending approval page, skipping device check');
      setIsDevicePending(true);
      setIsCheckingDevice(false);
      return;
    }

    checkDevice();

    // لا نحتاج فحص دوري إضافي هنا لأن صفحة DevicePendingApproval تتولى الفحص بنفسها
    // فقط نقوم بالتنظيف إذا لزم الأمر
    return () => {
      // تنظيف خدمة الاتصال الموحدة
      unifiedConnectionService.destroy();
      console.log('🔄 Unified connection service stopped');
    };
  }, []); // إزالة dependency لمنع التحميل المتكرر

  // Show device blocked page if device is blocked
  if (isDeviceBlocked) {
    return <DeviceBlocked />;
  }

  // Show device pending approval page if device is pending
  if (isDevicePending) {
    return <DevicePendingApproval />;
  }

  // Show loading while checking device or auth is initializing
  if (isCheckingDevice) {
    return <LoadingSpinner size="lg" message="جاري فحص الجهاز..." fullScreen />;
  }

  if (!isInitialized) {
    return <LoadingSpinner size="lg" message="جاري تهيئة التطبيق..." fullScreen />;
  }

  return (
    <ErrorBoundary>
      <ThemeProvider>
        <Router>
          <Suspense fallback={<LoadingSpinner size="sm" message="تحميل الصفحة..." fullScreen />}>
            <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/device-blocked" element={<DeviceBlocked />} />
            <Route path="/device-pending-approval" element={<DevicePendingApproval />} />

          {/* Dashboard Route */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <NewLayout>
                  <Dashboard />
                </NewLayout>
              </ProtectedRoute>
            }
          />

          {/* POS Route - Direct without Layout */}
          <Route
            path="/pos"
            element={
              <ProtectedRoute>
                <POS />
              </ProtectedRoute>
            }
          />

          {/* Products Routes */}
          <Route
            path="/products"
            element={
              <ProtectedRoute>
                <NewLayout>
                  <Products />
                </NewLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/products/:id"
            element={
              <ProtectedRoute>
                <NewLayout>
                  <Products />
                </NewLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/products/new"
            element={
              <ProtectedRoute>
                <NewLayout>
                  <Products />
                </NewLayout>
              </ProtectedRoute>
            }
          />

          {/* Sales Routes */}
          <Route
            path="/sales"
            element={
              <ProtectedRoute>
                <NewLayout>
                  <Sales />
                </NewLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/sales/:id"
            element={
              <ProtectedRoute>
                <NewLayout>
                  <Sales />
                </NewLayout>
              </ProtectedRoute>
            }
          />

          {/* Receipt Routes */}
          <Route
            path="/sales/:id/print"
            element={
              <ProtectedRoute>
                <Receipt />
              </ProtectedRoute>
            }
          />

          <Route
            path="/receipt/:id"
            element={
              <ProtectedRoute>
                <Receipt />
              </ProtectedRoute>
            }
          />

          {/* Reports Routes */}
          <Route
            path="/reports"
            element={
              <ProtectedRoute>
                <NewLayout>
                  <Reports />
                </NewLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/reports/:type"
            element={
              <ProtectedRoute>
                <NewLayout>
                  <Reports />
                </NewLayout>
              </ProtectedRoute>
            }
          />

          {/* Customers Routes */}
          <Route
            path="/customers"
            element={
              <ProtectedRoute>
                <NewLayout>
                  <Customers />
                </NewLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/customers/:id"
            element={
              <ProtectedRoute>
                <NewLayout>
                  <Customers />
                </NewLayout>
              </ProtectedRoute>
            }
          />

          {/* Debts Routes */}
          <Route
            path="/debts"
            element={
              <ProtectedRoute>
                <NewLayout>
                  <Debts />
                </NewLayout>
              </ProtectedRoute>
            }
          />

          {/* Help Center Route */}
          <Route
            path="/help"
            element={
              <ProtectedRoute>
                <NewLayout>
                  <HelpCenter />
                </NewLayout>
              </ProtectedRoute>
            }
          />

          {/* Admin Only Routes */}
          <Route
            path="/users"
            element={
              <AdminRoute>
                <NewLayout>
                  <Users />
                </NewLayout>
              </AdminRoute>
            }
          />

          <Route
            path="/settings"
            element={
              <AdminRoute>
                <NewLayout>
                  <Settings />
                </NewLayout>
              </AdminRoute>
            }
          />

          {/* Test Route - Admin Only */}
          {/* <Route
            path="/test-error-boundary"
            element={
              <AdminRoute>
                <TestErrorBoundary />
              </AdminRoute>
            }
          /> */}

          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </Suspense>
        </Router>

        {/* نظام الإشعارات */}
        <Toaster
          position="top-right"
          reverseOrder={false}
          gutter={8}
          containerClassName=""
          containerStyle={{}}
          toastOptions={{
            duration: 4000,
            style: {
              background: 'var(--toast-bg)',
              color: 'var(--toast-color)',
              fontSize: '14px',
              borderRadius: '8px',
              padding: '12px 16px',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
              border: '1px solid var(--toast-border)',
              fontFamily: 'almarai, sans-serif',
              direction: 'rtl'
            },
            success: {
              iconTheme: {
                primary: '#10B981',
                secondary: '#FFFFFF',
              },
            },
            error: {
              iconTheme: {
                primary: '#EF4444',
                secondary: '#FFFFFF',
              },
            },
          }}
        />
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;