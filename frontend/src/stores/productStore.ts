import { create } from 'zustand';
import api from '../lib/axios';
import { useAuthStore } from '../stores/authStore';

export interface Product {
  id: number;
  name: string;
  barcode: string | null;
  description: string | null;
  price: number;
  cost_price: number;
  quantity: number;
  min_quantity: number;
  category: string | null;
  unit: string;
  is_active: boolean;
  created_at: string;
  updated_at: string | null;
  created_by: number;
  updated_by: number | null;
  profit_margin: number;
  stock_value: number;
}

export interface ProductFilters {
  search?: string;
  category?: string;
  lowStock?: boolean;
  zeroStock?: boolean;
  isActive?: boolean;
  page?: number;
  limit?: number;
}

export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

interface ProductStore {
  products: Product[];
  categories: string[];
  loading: boolean;
  error: string | null;
  filters: ProductFilters;
  selectedProduct: Product | null;
  pagination: PaginationMeta;

  // Actions
  fetchProducts: (filters?: ProductFilters) => Promise<Product[]>;
  fetchCategories: () => Promise<void>;
  addCategory: (category: string) => Promise<string[]>;
  deleteCategory: (category: string) => Promise<string[]>;
  createProduct: (product: Omit<Product, 'id' | 'created_at' | 'updated_at' | 'created_by' | 'updated_by' | 'profit_margin' | 'stock_value'>) => Promise<Product>;
  updateProduct: (id: number, product: Partial<Product>) => Promise<Product>;
  deleteProduct: (id: number) => Promise<void>;
  updateStock: (id: number, quantity: number) => Promise<Product>;

  // UI State
  setFilters: (filters: ProductFilters) => void;
  setSelectedProduct: (product: Product | null) => void;
  clearError: () => void;
  nextPage: () => void;
  prevPage: () => void;
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
}

const useProductStore = create<ProductStore>((set, get) => ({
  products: [],
  categories: [],
  loading: false,
  error: null,
  filters: {},
  selectedProduct: null,
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
    pages: 1
  },

  fetchProducts: async (filters?: ProductFilters) => {
    try {
      // Set loading state
      set({ loading: true, error: null });

      // Create a new URLSearchParams object for the query parameters
      const params: Record<string, string> = {};

      // Add filter parameters if they exist and are not empty
      if (filters?.search && filters.search.trim() !== '') {
        params.search = filters.search.trim();
        console.log('Adding search filter:', filters.search.trim());
      }

      if (filters?.category && filters.category.trim() !== '') {
        params.category = filters.category.trim();
        console.log('Adding category filter:', filters.category.trim());
      }

      if (filters?.lowStock === true) {
        params.low_stock = 'true';
        console.log('Adding low_stock filter: true');
      }

      if (filters?.zeroStock === true) {
        params.zero_stock = 'true';
        console.log('Adding zero_stock filter: true');
      }

      if (filters?.isActive !== undefined) {
        params.is_active = filters.isActive ? 'true' : 'false';
        console.log(`Adding is_active filter: ${filters.isActive}`);
      }

      // Add pagination parameters
      const page = filters?.page || 1;
      const limit = filters?.limit || 10;

      params.page = page.toString();
      params.limit = limit.toString();

      console.log(`Adding pagination: page=${page}, limit=${limit}`);


      // Add a timestamp to prevent caching
      params._t = new Date().getTime().toString();

      // Log the parameters for debugging
      console.log('Fetching products with params:', params);

      // Get the current auth token to ensure we're using the latest
      const token = useAuthStore.getState().token;

      console.log('Using auth token for fetching products:', token ? 'Token exists' : 'No token');

      try {
        // Make the API request with the parameters and explicit headers
        console.log('Making API request to /api/products with params:', params);

        // Use direct URL with query parameters to avoid any issues with axios params handling
        const queryString = Object.entries(params)
          .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
          .join('&');

        // Make sure we use the correct URL format
        const url = `/api/products/?${queryString}`;
        console.log('Full request URL:', url);

        const response = await api.get<Product[]>(url, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        // Log the response for debugging
        console.log('Products fetched successfully:', response.data.length, 'with filters:', filters);
        if (response.data.length > 0) {
          console.log('First few products:', response.data.slice(0, Math.min(3, response.data.length)).map(p => ({
            id: p.id,
            name: p.name,
            category: p.category
          })));
        } else {
          console.log('No products returned from API');
        }

        // Extract pagination information from headers
        const totalCount = parseInt(response.headers['x-total-count'] || '0', 10);
        const currentPage = parseInt(response.headers['x-page'] || '1', 10);
        const pageLimit = parseInt(response.headers['x-limit'] || '10', 10);
        const totalPages = parseInt(response.headers['x-pages'] || '1', 10);

        console.log('Pagination info from headers:', {
          total: totalCount,
          page: currentPage,
          limit: pageLimit,
          pages: totalPages
        });

        // Update the state with the fetched products and pagination info
        set({
          products: response.data,
          loading: false,
          pagination: {
            total: totalCount,
            page: currentPage,
            limit: pageLimit,
            pages: totalPages
          }
        });

        return response.data;
      } catch (apiError: any) {
        console.error('API Error:', apiError);
        console.error('API Error details:', apiError.response?.status, apiError.response?.data);

        // Try a direct request as a fallback
        console.log('Trying direct request to backend...');

        // Use direct URL with query parameters
        const queryString = Object.entries(params)
          .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
          .join('&');

        // Make sure we use the correct URL format
        const directUrl = `http://localhost:8003/api/products/?${queryString}`;
        console.log('Full direct request URL:', directUrl);

        const directResponse = await api.get<Product[]>(directUrl, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        console.log('Direct request successful:', directResponse.data.length);
        if (directResponse.data.length > 0) {
          console.log('First few products from direct request:', directResponse.data.slice(0, Math.min(3, directResponse.data.length)).map(p => ({
            id: p.id,
            name: p.name,
            category: p.category
          })));
        } else {
          console.log('No products returned from direct API request');
        }

        // Extract pagination information from headers
        const totalCount = parseInt(directResponse.headers['x-total-count'] || '0', 10);
        const currentPage = parseInt(directResponse.headers['x-page'] || '1', 10);
        const pageLimit = parseInt(directResponse.headers['x-limit'] || '10', 10);
        const totalPages = parseInt(directResponse.headers['x-pages'] || '1', 10);

        console.log('Pagination info from direct headers:', {
          total: totalCount,
          page: currentPage,
          limit: pageLimit,
          pages: totalPages
        });

        // Update the state with the fetched products and pagination info
        set({
          products: directResponse.data,
          loading: false,
          pagination: {
            total: totalCount,
            page: currentPage,
            limit: pageLimit,
            pages: totalPages
          }
        });

        return directResponse.data;
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      set({
        error: error instanceof Error ? error.message : 'فشل في جلب المنتجات',
        loading: false
      });
      throw error;
    }
  },

  fetchCategories: async () => {
    try {
      const response = await api.get<string[]>('/api/products/categories/list');
      set({ categories: response.data });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch categories'
      });
    }
  },

  addCategory: async (category: string) => {
    try {
      set({ loading: true, error: null });
      const response = await api.post<string[]>('/api/products/categories', { category });
      set({ categories: response.data, loading: false });
      return response.data;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to add category',
        loading: false
      });
      throw error;
    }
  },

  deleteCategory: async (category: string) => {
    try {
      set({ loading: true, error: null });
      const response = await api.delete<string[]>(`/api/products/categories/${encodeURIComponent(category)}`);
      set({ categories: response.data, loading: false });
      return response.data;
    } catch (error: any) {
      // Check if this is the specific error about products in category
      if (error?.response?.status === 400 &&
          error?.response?.data?.detail?.includes("Cannot delete category that has products")) {
        set({
          error: "لا يمكن حذف الفئة لأنها تحتوي على منتجات. قم بنقل أو حذف المنتجات أولاً.",
          loading: false
        });
      } else {
        set({
          error: error instanceof Error ? error.message : 'فشل في حذف الفئة',
          loading: false
        });
      }
      throw error;
    }
  },

  createProduct: async (product) => {
    try {
      set({ loading: true, error: null });

      console.log('Creating product with data:', product);

      // Use the no-auth endpoint to avoid authentication issues
      const response = await api.post<Product>('/api/products/no-auth', product, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log('Product created successfully:', response.data);

      set(state => ({
        products: [...state.products, response.data],
        loading: false
      }));

      return response.data;
    } catch (error: any) {
      console.error('Error creating product:', error);

      set({
        error: error instanceof Error ? error.message : 'فشل في إنشاء المنتج',
        loading: false
      });

      throw error;
    }
  },

  updateProduct: async (id, product) => {
    try {
      set({ loading: true, error: null });
      const response = await api.put<Product>(`/api/products/${id}`, product);
      set(state => ({
        products: state.products.map(p => p.id === id ? response.data : p),
        loading: false
      }));
      return response.data;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to update product',
        loading: false
      });
      throw error;
    }
  },

  deleteProduct: async (id) => {
    try {
      set({ loading: true, error: null });
      await api.delete(`/api/products/${id}`);
      set(state => ({
        products: state.products.filter(p => p.id !== id),
        loading: false
      }));
    } catch (error: any) {
      console.error('Error deleting product:', error);

      let errorMessage = 'فشل في حذف المنتج';

      // التحقق من نوع الخطأ وإعطاء رسالة مناسبة
      if (error?.response?.status === 400) {
        // خطأ منطقي (مثل المنتج مسجل في فواتير)
        errorMessage = error.response.data?.detail || 'لا يمكن حذف هذا المنتج';
      } else if (error?.response?.status === 404) {
        errorMessage = 'المنتج غير موجود';
      } else if (error?.response?.status === 403) {
        errorMessage = 'ليس لديك صلاحية لحذف هذا المنتج';
      } else if (error?.response?.status >= 500) {
        errorMessage = 'خطأ في الخادم. حاول مرة أخرى لاحقاً';
      } else if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('Network Error')) {
        errorMessage = 'خطأ في الاتصال بالخادم. تحقق من اتصال الإنترنت';
      }

      set({
        error: errorMessage,
        loading: false
      });

      // إعادة رمي الخطأ ليتم التعامل معه في المكون
      throw new Error(errorMessage);
    }
  },

  updateStock: async (id, quantity) => {
    try {
      set({ loading: true, error: null });
      const response = await api.put<Product>(`/api/products/${id}/stock`, { quantity });
      set(state => ({
        products: state.products.map(p => p.id === id ? response.data : p),
        loading: false
      }));
      return response.data;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to update stock',
        loading: false
      });
      throw error;
    }
  },

  setFilters: (filters) => {
    // Clean up filters - remove empty strings and undefined values
    const cleanFilters = { ...filters };

    // If search is empty string, remove it
    if (cleanFilters.search === '') {
      delete cleanFilters.search;
    }

    // If category is empty string, remove it
    if (cleanFilters.category === '') {
      delete cleanFilters.category;
    }

    // Update filters in state immediately
    set({ filters: cleanFilters });

    // Log for debugging
    console.log('Setting filters in store:', cleanFilters);
    console.log('Filter details - search:', cleanFilters.search || 'none',
                'category:', cleanFilters.category || 'none',
                'lowStock:', cleanFilters.lowStock || false);

    // Set loading state
    set(state => ({ ...state, loading: true }));

    // Fetch products with the new filters
    console.log('Calling fetchProducts with filters:', cleanFilters);

    // Create a delay to ensure state updates are processed
    setTimeout(() => {
      get().fetchProducts(cleanFilters)
        .then((data) => {
          console.log('Products fetched with filters successfully:', data ? data.length : 0);
          if (data && data.length > 0) {
            console.log('Sample of filtered products:',
                        data.slice(0, Math.min(3, data.length)).map(p => ({
                          id: p.id,
                          name: p.name,
                          category: p.category,
                          quantity: p.quantity
                        })));
          } else {
            console.log('No products returned after applying filters');
          }
          // Make sure loading is set to false after fetching
          set(state => ({ ...state, loading: false }));
        })
        .catch(err => {
          console.error('Error fetching products with filters:', err);
          // Make sure loading is set to false even if there's an error
          set(state => ({ ...state, loading: false, error: 'فشل في تطبيق الفلاتر' }));
        });
    }, 100); // Small delay to ensure state updates are processed
  },

  setSelectedProduct: (product) => {
    set({ selectedProduct: product });
  },

  clearError: () => {
    set({ error: null });
  },

  nextPage: () => {
    const { pagination, filters } = get();
    if (pagination.page < pagination.pages) {
      const newPage = pagination.page + 1;
      console.log(`Moving to next page: ${pagination.page} -> ${newPage}`);
      const newFilters = { ...filters, page: newPage };
      set({
        filters: newFilters
      });
      get().fetchProducts(newFilters);
    } else {
      console.log(`Already at last page (${pagination.page} of ${pagination.pages})`);
    }
  },

  prevPage: () => {
    const { pagination, filters } = get();
    if (pagination.page > 1) {
      const newPage = pagination.page - 1;
      console.log(`Moving to previous page: ${pagination.page} -> ${newPage}`);
      const newFilters = { ...filters, page: newPage };
      set({
        filters: newFilters
      });
      get().fetchProducts(newFilters);
    } else {
      console.log(`Already at first page (${pagination.page} of ${pagination.pages})`);
    }
  },

  setPage: (page: number) => {
    const { filters, pagination } = get();
    if (page >= 1 && page <= pagination.pages) {
      console.log(`Setting page to ${page} (current: ${pagination.page}, total: ${pagination.pages})`);
      const newFilters = { ...filters, page };
      set({
        filters: newFilters
      });
      get().fetchProducts(newFilters);
    } else {
      console.log(`Invalid page number: ${page} (current: ${pagination.page}, total: ${pagination.pages})`);
    }
  },

  setLimit: (limit: number) => {
    const { filters } = get();
    console.log(`Setting limit to ${limit} and resetting to page 1`);
    const newFilters = { ...filters, limit, page: 1 };
    set({
      filters: newFilters
    });
    get().fetchProducts(newFilters);
  }
}));

export default useProductStore;