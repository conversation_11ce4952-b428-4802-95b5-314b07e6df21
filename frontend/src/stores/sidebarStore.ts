import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

// تعريف أنواع القوائم الفرعية
export interface SubMenuItem {
  id: string;
  name: string;
  path: string;
  icon?: string;
}

// تعريف عنصر القائمة الرئيسية
export interface MenuItem {
  id: string;
  name: string;
  path: string;
  icon: string;
  iconColor?: string; // لون الأيقونة
  subItems?: SubMenuItem[];
  badge?: number; // للإشعارات
}

// حالة الشريط الجانبي
interface SidebarState {
  // حالة الفتح/الإغلاق
  isOpen: boolean;
  isMobileMenuOpen: boolean;
  
  // القائمة النشطة
  activeMenuItem: string | null;
  activeSubMenuItem: string | null;
  
  // القوائم المفتوحة (للقوائم الفرعية)
  expandedMenus: string[];
  
  // قوائم التطبيق
  menuItems: MenuItem[];
  
  // الإجراءات
  toggleSidebar: () => void;
  toggleMobileMenu: () => void;
  setActiveMenuItem: (itemId: string) => void;
  setActiveSubMenuItem: (itemId: string) => void;
  toggleMenuExpansion: (menuId: string) => void;
  closeSidebar: () => void;
  openSidebar: () => void;
  updateMenuBadge: (menuId: string, count: number) => void;
}

// القوائم الافتراضية للتطبيق
const defaultMenuItems: MenuItem[] = [
  {
    id: 'dashboard',
    name: 'لوحة التحكم',
    path: '/',
    icon: 'FiHome',
    iconColor: 'text-blue-500 dark:text-blue-400'
  },
  {
    id: 'pos',
    name: 'نقطة البيع',
    path: '/pos',
    icon: 'FiShoppingCart',
    iconColor: 'text-green-500 dark:text-green-400'
  },
  {
    id: 'sales',
    name: 'المبيعات',
    path: '/sales',
    icon: 'FiTrendingUp',
    iconColor: 'text-emerald-500 dark:text-emerald-400',
    subItems: [
      { id: 'sales-list', name: 'قائمة المبيعات', path: '/sales' },
      { id: 'quotations', name: 'عروض الأسعار', path: '/quotations' },
      { id: 'returns', name: 'المرتجعات', path: '/returns' }
    ]
  },
  {
    id: 'inventory',
    name: 'المخزون',
    path: '/products',
    icon: 'FiPackage',
    iconColor: 'text-orange-500 dark:text-orange-400',
    subItems: [
      { id: 'products', name: 'المنتجات', path: '/products' },
      { id: 'categories', name: 'الفئات', path: '/categories' },
      { id: 'brands', name: 'العلامات التجارية', path: '/brands' },
      { id: 'suppliers', name: 'الموردين', path: '/suppliers' },
      { id: 'purchases', name: 'إدخال مخزون (المشتريات)', path: '/purchases' },
      { id: 'supplier-returns', name: 'مرتجعات الموردين', path: '/supplier-returns' },
      { id: 'barcode-print', name: 'طباعة الباركود', path: '/barcode-print' }
    ]
  },
  {
    id: 'customers',
    name: 'العملاء',
    path: '/customers',
    icon: 'FiUsers',
    iconColor: 'text-purple-500 dark:text-purple-400'
  },
  {
    id: 'employees',
    name: 'الموظفون',
    path: '/users',
    icon: 'FiUser',
    iconColor: 'text-indigo-500 dark:text-indigo-400'
  },
  {
    id: 'expenses',
    name: 'المصروفات',
    path: '/expenses',
    icon: 'FiCreditCard',
    iconColor: 'text-red-500 dark:text-red-400'
  },
  {
    id: 'reports',
    name: 'التقارير',
    path: '/reports',
    icon: 'FiBarChart',
    iconColor: 'text-cyan-500 dark:text-cyan-400',
    subItems: [
      { id: 'sales-reports', name: 'تقارير المبيعات', path: '/reports/sales' },
      { id: 'inventory-reports', name: 'تقارير المخزون', path: '/reports/inventory' },
      { id: 'financial-reports', name: 'التقارير المالية', path: '/reports/financial' },
      { id: 'customer-reports', name: 'تقارير العملاء', path: '/reports/customers' }
    ]
  },
  {
    id: 'settings',
    name: 'الإعدادات',
    path: '/settings',
    icon: 'FiSettings',
    iconColor: 'text-gray-500 dark:text-gray-400',
    subItems: [
      { id: 'general-settings', name: 'الإعدادات العامة', path: '/settings/general' },
      { id: 'user-settings', name: 'إعدادات المستخدم', path: '/settings/user' },
      { id: 'system-settings', name: 'إعدادات النظام', path: '/settings/system' },
      { id: 'backup-settings', name: 'النسخ الاحتياطي', path: '/settings/backup' },
      { id: 'security-settings', name: 'الأمان', path: '/settings/security' },
      { id: 'printer-settings', name: 'إعدادات الطابعة', path: '/settings/printer' },
      { id: 'tax-settings', name: 'إعدادات الضرائب', path: '/settings/tax' },
      { id: 'currency-settings', name: 'إعدادات العملة', path: '/settings/currency' },
      { id: 'notification-settings', name: 'إعدادات التنبيهات', path: '/settings/notifications' },
      { id: 'integration-settings', name: 'التكاملات', path: '/settings/integrations' }
    ]
  },
  // إضافة عناصر إضافية لضمان ظهور التمرير
  {
    id: 'analytics',
    name: 'التحليلات',
    path: '/analytics',
    icon: 'FiBarChart',
    iconColor: 'text-pink-500 dark:text-pink-400',
    subItems: [
      { id: 'sales-analytics', name: 'تحليل المبيعات', path: '/analytics/sales' },
      { id: 'customer-analytics', name: 'تحليل العملاء', path: '/analytics/customers' },
      { id: 'product-analytics', name: 'تحليل المنتجات', path: '/analytics/products' }
    ]
  },
  {
    id: 'help',
    name: 'المساعدة',
    path: '/help',
    icon: 'FiSettings',
    iconColor: 'text-blue-500 dark:text-blue-400',
    subItems: [
      { id: 'documentation', name: 'التوثيق', path: '/help/docs' },
      { id: 'support', name: 'الدعم الفني', path: '/help/support' },
      { id: 'about', name: 'حول البرنامج', path: '/help/about' }
    ]
  }
];

// إنشاء المتجر
export const useSidebarStore = create<SidebarState>()(
  persist(
    (set, get) => ({
      // الحالة الافتراضية
      isOpen: true, // مفتوح افتراضياً على الشاشات الكبيرة
      isMobileMenuOpen: false,
      activeMenuItem: 'dashboard',
      activeSubMenuItem: null,
      expandedMenus: [],
      menuItems: defaultMenuItems,

      // تبديل حالة الشريط الجانبي
      toggleSidebar: () => {
        set((state) => ({ isOpen: !state.isOpen }));
      },

      // تبديل القائمة المحمولة
      toggleMobileMenu: () => {
        set((state) => ({ isMobileMenuOpen: !state.isMobileMenuOpen }));
      },

      // تعيين القائمة النشطة
      setActiveMenuItem: (itemId: string) => {
        set({ activeMenuItem: itemId, activeSubMenuItem: null });
      },

      // تعيين القائمة الفرعية النشطة
      setActiveSubMenuItem: (itemId: string) => {
        set({ activeSubMenuItem: itemId });
      },

      // تبديل توسيع القائمة
      toggleMenuExpansion: (menuId: string) => {
        set((state) => {
          const expandedMenus = [...state.expandedMenus];
          const index = expandedMenus.indexOf(menuId);
          
          if (index > -1) {
            expandedMenus.splice(index, 1);
          } else {
            expandedMenus.push(menuId);
          }
          
          return { expandedMenus };
        });
      },

      // إغلاق الشريط الجانبي
      closeSidebar: () => {
        set({ isOpen: false, isMobileMenuOpen: false });
      },

      // فتح الشريط الجانبي
      openSidebar: () => {
        set({ isOpen: true });
      },

      // تحديث شارة القائمة
      updateMenuBadge: (menuId: string, count: number) => {
        set((state) => ({
          menuItems: state.menuItems.map(item =>
            item.id === menuId ? { ...item, badge: count } : item
          )
        }));
      }
    }),
    {
      name: 'sidebar-storage',
      storage: createJSONStorage(() => localStorage),
      // حفظ فقط الحالات المهمة
      partialize: (state) => ({
        isOpen: state.isOpen,
        activeMenuItem: state.activeMenuItem,
        activeSubMenuItem: state.activeSubMenuItem,
        expandedMenus: state.expandedMenus
      })
    }
  )
);

// Hook مساعد للحصول على القائمة النشطة
export const useActiveMenuItem = () => {
  const { activeMenuItem, menuItems } = useSidebarStore();
  return menuItems.find(item => item.id === activeMenuItem);
};

// Hook مساعد للحصول على القائمة الفرعية النشطة
export const useActiveSubMenuItem = () => {
  const { activeSubMenuItem, menuItems } = useSidebarStore();
  
  for (const item of menuItems) {
    if (item.subItems) {
      const subItem = item.subItems.find(sub => sub.id === activeSubMenuItem);
      if (subItem) return subItem;
    }
  }
  
  return null;
};

// Hook للتحقق من حالة توسيع القائمة
export const useIsMenuExpanded = (menuId: string) => {
  const { expandedMenus } = useSidebarStore();
  return expandedMenus.includes(menuId);
};
