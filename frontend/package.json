{"name": "smartpos-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "NODE_ENV=development vite", "dev:network": "NODE_ENV=development vite --host 0.0.0.0", "build": "npx tsc && npx vite build", "preview": "vite preview", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write \"src/**/*.{ts,tsx}\""}, "dependencies": {"@types/leaflet": "^1.9.20", "apexcharts": "^4.7.0", "axios": "^1.6.7", "date-fns": "^4.1.0", "jspdf": "^3.0.1", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "qrcode": "^1.5.4", "react": "^18.2.0", "react-apexcharts": "^1.7.0", "react-countup": "^6.5.3", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.0.1", "react-leaflet": "^4.2.1", "react-router-dom": "^6.22.1", "zustand": "^4.5.1"}, "devDependencies": {"@types/jspdf": "^1.3.3", "@types/node": "^20.11.17", "@types/qrcode": "^1.5.5", "@types/react": "^18.3.23", "@types/react-countup": "^4.3.0", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^8.57.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.20", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^3.4.17", "terser": "^5.43.1", "typescript": "^5.8.3", "vite": "^7.0.4"}}