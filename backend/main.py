from fastapi import FastAP<PERSON>, Request, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import RedirectResponse
from contextlib import asynccontextmanager
from typing import AsyncIterator
import uvicorn
import logging
import json
import asyncio
from pathlib import Path
from datetime import datetime
from routers import auth, users, products, sales_router, dashboard_router, settings_router
from routers.customers import router as customers_router
from routers.debts import router as debts_router
from routers.debt_reports import router as debt_reports_router
from routers.system_logs import router as system_logs_router
from routers.device_security import router as device_security_router
from routers.link_preview import router as link_preview_router
from routers.product_analytics import router as product_analytics_router
from routers.support_email import router as support_email_router
from routers.performance import router as performance_router
from routers.data_streaming import router as data_streaming_router
from routers.pdf_export import router as pdf_export_router
from routers.chat import router as chat_router
from database.session import engine, Base, get_db

# Create database tables
Base.metadata.create_all(bind=engine)

# Setup logger
logger = logging.getLogger(__name__)

# مهمة دورية لتنظيف cache الأجهزة
cleanup_task = None

async def cleanup_device_cache_periodically():
    """
    مهمة دورية لتنظيف cache الأجهزة كل 10 دقائق
    """
    while True:
        try:
            await asyncio.sleep(600)  # 10 دقائق
            logger.debug("تشغيل مهمة تنظيف cache الأجهزة")
            # سيتم التنظيف تلقائياً داخل middleware عند انتهاء صلاحية cache

        except asyncio.CancelledError:
            break
        except Exception as e:
            logger.debug(f"خطأ في مهمة تنظيف cache: {e}")

def lifespan(app: FastAPI):  # noqa: ARG001
    """إدارة دورة حياة التطبيق - بدء وإيقاف الخدمات"""

    @asynccontextmanager
    async def _lifespan() -> AsyncIterator[None]:
        global cleanup_task

        # بدء الخدمات
        try:
            # Initialize scheduler service
            from services.scheduler_service import scheduler_service
            scheduler_service.start()
            logger.info("تم بدء خدمة جدولة المهام")

            # بدء مهمة تنظيف cache الأجهزة
            cleanup_task = asyncio.create_task(cleanup_device_cache_periodically())
            logger.info("تم بدء مهمة تنظيف cache الأجهزة")

            # بدء خدمة التنظيف التلقائي للأجهزة غير النشطة
            try:
                from utils.auto_device_cleanup import auto_cleanup_service
                if not auto_cleanup_service.is_running:
                    auto_cleanup_service.start()
                    logger.info("تم بدء خدمة التنظيف التلقائي للأجهزة غير النشطة")
                else:
                    logger.debug("خدمة التنظيف التلقائي تعمل بالفعل")
            except Exception as cleanup_error:
                logger.warning(f"فشل في بدء خدمة التنظيف التلقائي: {cleanup_error}")

        except Exception as e:
            logger.error(f"خطأ في بدء الخدمات: {e}")

        yield  # هنا يعمل التطبيق

        # إيقاف الخدمات
        try:
            if cleanup_task:
                cleanup_task.cancel()
                try:
                    await cleanup_task
                except asyncio.CancelledError:
                    pass

            from services.scheduler_service import scheduler_service
            scheduler_service.stop()
            logger.info("تم إيقاف خدمة جدولة المهام")

            # إيقاف خدمة التنظيف التلقائي
            try:
                from utils.auto_device_cleanup import auto_cleanup_service
                if auto_cleanup_service.is_running:
                    auto_cleanup_service.stop()
                    logger.info("تم إيقاف خدمة التنظيف التلقائي للأجهزة غير النشطة")
                else:
                    logger.debug("خدمة التنظيف التلقائي متوقفة بالفعل")
            except Exception as cleanup_error:
                logger.warning(f"فشل في إيقاف خدمة التنظيف التلقائي: {cleanup_error}")

        except Exception as e:
            logger.error(f"خطأ في إيقاف الخدمات: {e}")

    return _lifespan()

app = FastAPI(
    title="SmartPOS API",
    description="Backend API for SmartPOS system",
    version="1.0.0",
    lifespan=lifespan
)

# Dynamic CORS configuration with automatic IP detection
def get_dynamic_cors_origins():
    """
    إنشاء قائمة ديناميكية بعناوين CORS المسموحة مع الكشف التلقائي للشبكة
    """
    origins = []

    # العناوين المحلية الأساسية
    local_origins = [
        "http://localhost:3000",
        "http://localhost:5174",
        "http://localhost:5175",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5174",
        "http://127.0.0.1:5175"
    ]
    origins.extend(local_origins)

    # الكشف التلقائي عن IP الخادم
    try:
        from utils.url_manager import url_manager
        server_ip = url_manager._get_server_ip()

        if server_ip and server_ip not in ["127.0.0.1", "localhost"]:
            # إضافة عناوين الخادم المكتشف
            server_origins = [
                f"http://{server_ip}:3000",
                f"http://{server_ip}:5174",
                f"http://{server_ip}:5175",
                f"http://{server_ip}:8002"
            ]
            origins.extend(server_origins)
            print(f"🌐 تم اكتشاف IP الخادم وإضافته لـ CORS: {server_ip}")
    except Exception as e:
        print(f"⚠️ فشل في الكشف التلقائي عن IP الخادم: {e}")

    return origins

# تطبيق CORS مع الكشف التلقائي
dynamic_origins = get_dynamic_cors_origins()

app.add_middleware(
    CORSMiddleware,
    allow_origins=dynamic_origins,
    allow_origin_regex=r"http://(localhost|127\.0\.0\.1|192\.168\.\d+\.\d+|10\.\d+\.\d+\.\d+|172\.16\.\d+\.\d+|100\.\d+\.\d+\.\d+):(3000|5174|5175|8002)",
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["X-Total-Count", "X-Page", "X-Limit", "X-Pages"],
)

# إضافة middleware تحسين الأداء (يجب أن يكون أولاً)
from middleware.performance_middleware import add_performance_middleware
app.middleware("http")(add_performance_middleware)

# إضافة middleware الأمان الموحدة (الوحيدة المطلوبة)
from middleware.unified_security_middleware import UnifiedSecurityMiddleware
app.add_middleware(UnifiedSecurityMiddleware)



# Include routers
app.include_router(auth)
app.include_router(users)
app.include_router(products)
app.include_router(sales_router)
app.include_router(dashboard_router)
app.include_router(settings_router)
app.include_router(customers_router)
app.include_router(debts_router)
app.include_router(debt_reports_router)
app.include_router(system_logs_router, prefix="/api/system", tags=["system"])
app.include_router(device_security_router)
app.include_router(product_analytics_router)
app.include_router(support_email_router)
app.include_router(performance_router)
app.include_router(data_streaming_router)
app.include_router(pdf_export_router)
app.include_router(chat_router)
app.include_router(link_preview_router)

# Import and include scheduled tasks router
from routers.scheduled_tasks import router as scheduled_tasks_router
app.include_router(scheduled_tasks_router)

# Import and include Google Drive router
from routers.google_drive import router as google_drive_router
app.include_router(google_drive_router)

# Import and include comprehensive fingerprint router
from routers.comprehensive_fingerprint import router as comprehensive_fingerprint_router
app.include_router(comprehensive_fingerprint_router)

# تم حذف device_fingerprint_router المعطل - يتم استخدام device_fingerprints_router بدلاً منه



# Import and include Device Fingerprints router
from routes.device_fingerprints_api import router as device_fingerprints_router
app.include_router(device_fingerprints_router)

# Serve static files
static_path = Path(__file__).parent / "static"
static_path.mkdir(exist_ok=True)
app.mount("/static", StaticFiles(directory=str(static_path)), name="static")

# Setup templates
templates_path = Path(__file__).parent / "templates"
templates = Jinja2Templates(directory=str(templates_path))



# Cache لحفظ عناوين الأجهزة البعيدة
remote_devices_cache = {}



@app.get("/")
async def root(request: Request):
    """الصفحة الرئيسية - إعادة توجيه للتطبيق أو صفحة طلب الموافقة"""
    try:
        # الحصول على معلومات الجهاز
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "")

        # استخدام الخدمة الموحدة لإنشاء معرف الجهاز
        from services.unified_fingerprint_service import get_unified_fingerprint_service
        from database.session import get_db

        db = next(get_db())
        unified_service = get_unified_fingerprint_service(db)
        request_headers = dict(request.headers)
        device_data = unified_service.generate_unified_device_id(client_ip, user_agent, request_headers)
        device_id = device_data['device_id']

        # تطبيع IP
        normalized_ip = client_ip.strip()

        # التحقق من نوع الجهاز باستخدام url_manager
        try:
            from utils.url_manager import url_manager
            is_main_server = url_manager.is_main_server_ip(normalized_ip)
        except:
            is_main_server = normalized_ip in ['*************', '127.0.0.1', 'localhost']  # fallback

        # فحص إضافي: إذا كان الطلب يحتوي على بصمة متقدمة من جهاز بعيد
        device_fingerprint = request_headers.get('x-device-fingerprint') or request_headers.get('X-Device-Fingerprint')
        has_advanced_fingerprint = device_fingerprint and device_fingerprint.startswith('fp_')

        # إذا كان لديه بصمة متقدمة ولكن ليس من الخادم الرئيسي، فهو جهاز بعيد
        if has_advanced_fingerprint and not is_main_server:
            logger.info(f"🔍 جهاز بعيد مع بصمة متقدمة: {device_fingerprint} من IP: {normalized_ip}")
        elif has_advanced_fingerprint and is_main_server:
            logger.info(f"🔍 الخادم الرئيسي مع بصمة متقدمة: {device_fingerprint} من IP: {normalized_ip}")
        elif is_main_server:
            logger.info(f"🔍 الخادم الرئيسي بدون بصمة متقدمة من IP: {normalized_ip}")

        # إذا كان الخادم الرئيسي، إعادة توجيه للتطبيق
        if is_main_server:
            logger.info(f"✅ الخادم الرئيسي يصل إلى الصفحة الرئيسية: {device_id}")
            return RedirectResponse(url="/login", status_code=302)

        # للأجهزة البعيدة، فحص الأمان
        from services.device_security import DeviceSecurityService
        from database.session import get_db

        device_security = DeviceSecurityService()
        db = next(get_db())

        try:
            # فحص الحظر
            is_blocked = device_security.is_device_blocked(device_id, normalized_ip, db)
            if is_blocked:
                return RedirectResponse(url="/device-blocked", status_code=302)

            # فحص طلب الموافقة
            if device_security.requires_approval(db):
                # فحص إذا كان معتمد
                is_approved = device_security.is_device_approved(device_id, db)
                if is_approved:
                    return RedirectResponse(url="/login", status_code=302)

                # إضافة للانتظار إذا لم يكن موجود
                from models.device_security import PendingDevice
                from sqlalchemy import select

                stmt = select(PendingDevice).where(
                    PendingDevice.device_id == device_id,
                    PendingDevice.status == "pending"
                )
                existing_pending = db.execute(stmt).scalar_one_or_none()

                if not existing_pending:
                    # استخراج معلومات الجهاز من user agent
                    device_info = {
                        'hostname': 'جهاز غير معروف',
                        'device_type': 'غير محدد',
                        'system': 'غير معروف',
                        'platform': 'غير معروف'
                    }

                    # إضافة معلومات البصمة المتقدمة إذا كانت متوفرة
                    fingerprint_data = {}
                    if has_advanced_fingerprint:
                        fingerprint_data = {
                            'hardware_fingerprint': request_headers.get('x-device-hardware', ''),
                            'storage_fingerprint': request_headers.get('x-device-storage', ''),
                            'screen_fingerprint': request_headers.get('x-device-screen', ''),
                            'system_fingerprint': request_headers.get('x-device-system', ''),
                            'is_advanced_fingerprint': True
                        }

                    device_data_for_pending = {
                        'device_id': device_id,
                        'client_ip': normalized_ip,
                        'hostname': device_info.get('hostname', 'غير معروف'),
                        'device_type': 'جهاز بعيد',
                        'system': device_info.get('system', 'غير معروف'),
                        'platform': device_info.get('platform', 'غير معروف'),
                        'user_agent': user_agent,
                        'current_user': None,
                        **fingerprint_data  # إضافة معلومات البصمة
                    }
                    device_security.add_pending_device(device_data_for_pending, db)

                # توجيه إلى frontend React بدلاً من backend HTML
                client_ip = normalized_ip
                frontend_url = f"http://{client_ip}:5175/device-pending-approval"
                return RedirectResponse(url=frontend_url, status_code=302)

            # السماح بالوصول (نظام الموافقة معطل)
            return RedirectResponse(url="/login", status_code=302)

        finally:
            db.close()

    except Exception as e:
        logger.error(f"خطأ في الصفحة الرئيسية: {e}")
        # في حالة الخطأ، إعادة توجيه لصفحة تسجيل الدخول
        return RedirectResponse(url="/login", status_code=302)

@app.get("/device-blocked")
async def device_blocked_page(request: Request):
    """صفحة الجهاز المحظور"""
    return templates.TemplateResponse("device_blocked.html", {"request": request})

@app.get("/device-pending-approval")
async def device_pending_approval_page(request: Request):
    """صفحة انتظار موافقة الجهاز - إعادة توجيه إلى React"""
    # إعادة توجيه إلى React frontend بدلاً من HTML القديم
    client_ip = request.client.host if request.client else "*************"
    if client_ip == "127.0.0.1" or client_ip == "localhost":
        client_ip = "*************"

    frontend_url = f"http://{client_ip}:5175/device-pending-approval"
    return RedirectResponse(url=frontend_url, status_code=302)

@app.get("/api/device/client-ip")
async def get_client_ip(request: Request, device_id: str = ""):
    """إرجاع عنوان IP الحقيقي للجهاز من قاعدة البيانات"""
    try:
        # الحصول على معرف الجهاز من query parameter أو headers
        device_fingerprint = device_id or request.headers.get('x-device-fingerprint') or request.headers.get('X-Device-Fingerprint')

        if device_fingerprint:
            logger.info(f"🔍 البحث عن عنوان IP للجهاز: {device_fingerprint}")

            from database.session import get_db
            db = next(get_db())

            try:
                # البحث في الأجهزة المحظورة أولاً (الأولوية للأجهزة المحظورة)
                from models.device_security import BlockedDevice, PendingDevice, ApprovedDevice
                from sqlalchemy import select

                stmt = select(BlockedDevice).where(
                    BlockedDevice.device_id == device_fingerprint
                )
                blocked_device = db.execute(stmt).scalar_one_or_none()

                if blocked_device and blocked_device.client_ip:
                    logger.info(f"🚫 تم العثور على عنوان IP في الأجهزة المحظورة: {blocked_device.client_ip}")
                    return {
                        "client_ip": blocked_device.client_ip,
                        "source": "blocked_devices",
                        "device_id": device_fingerprint,
                        "status": "blocked"
                    }

                # البحث في الأجهزة المنتظرة للموافقة
                stmt = select(PendingDevice).where(
                    PendingDevice.device_id == device_fingerprint,
                    PendingDevice.status == "pending"
                )
                pending_device = db.execute(stmt).scalar_one_or_none()

                if pending_device and pending_device.client_ip:
                    logger.info(f"⏳ تم العثور على عنوان IP في الأجهزة المنتظرة: {pending_device.client_ip}")
                    return {
                        "client_ip": pending_device.client_ip,
                        "source": "pending_devices",
                        "device_id": device_fingerprint,
                        "status": "pending"
                    }

                # البحث في الأجهزة المعتمدة كـ fallback
                stmt = select(ApprovedDevice).where(
                    ApprovedDevice.device_id == device_fingerprint
                )
                approved_device = db.execute(stmt).scalar_one_or_none()

                if approved_device and approved_device.client_ip:
                    logger.info(f"✅ تم العثور على عنوان IP في الأجهزة المعتمدة: {approved_device.client_ip}")
                    return {
                        "client_ip": approved_device.client_ip,
                        "source": "approved_devices",
                        "device_id": device_fingerprint,
                        "status": "approved"
                    }

            finally:
                db.close()

        # إذا لم نجد معرف الجهاز، نبحث عن آخر جهاز بعيد في قاعدة البيانات
        from database.session import get_db
        from utils.url_manager import url_manager
        server_ip = url_manager.config.get('backend_host', '*************')

        db = next(get_db())
        try:
            from models.device_security import BlockedDevice, PendingDevice
            from sqlalchemy import select, desc

            # البحث عن آخر جهاز بعيد محظور أولاً
            stmt = select(BlockedDevice).where(
                BlockedDevice.client_ip != server_ip,
                BlockedDevice.client_ip != "127.0.0.1"
            ).order_by(desc(BlockedDevice.blocked_at)).limit(1)

            latest_blocked_device = db.execute(stmt).scalar_one_or_none()

            if latest_blocked_device:
                logger.info(f"🚫 تم العثور على آخر جهاز بعيد محظور: {latest_blocked_device.client_ip}")
                return {
                    "client_ip": latest_blocked_device.client_ip,
                    "source": "latest_blocked_device",
                    "device_id": latest_blocked_device.device_id,
                    "status": "blocked"
                }

            # البحث عن آخر جهاز بعيد منتظر
            stmt = select(PendingDevice).where(
                PendingDevice.status == "pending",
                PendingDevice.client_ip != server_ip,
                PendingDevice.client_ip != "127.0.0.1"
            ).order_by(desc(PendingDevice.created_at)).limit(1)

            latest_remote_device = db.execute(stmt).scalar_one_or_none()

            if latest_remote_device:
                logger.info(f"⏳ تم العثور على آخر جهاز بعيد منتظر: {latest_remote_device.client_ip}")
                return {
                    "client_ip": latest_remote_device.client_ip,
                    "source": "latest_remote_device",
                    "device_id": latest_remote_device.device_id,
                    "status": "pending"
                }

        finally:
            db.close()

        # fallback: استخدام عنوان IP الحقيقي من الطلب
        client_ip = request.client.host if request.client else "unknown"
        real_ip = (
            request.headers.get("X-Real-IP") or
            request.headers.get("X-Forwarded-For", "").split(",")[0].strip() or
            request.headers.get("CF-Connecting-IP") or
            client_ip
        )

        logger.info(f"📍 استخدام عنوان IP من الطلب كـ fallback: {real_ip}")

        return {
            "client_ip": real_ip,
            "source": "request_headers",
            "device_id": device_fingerprint or "unknown"
        }

    except Exception as e:
        logger.error(f"❌ خطأ في الحصول على عنوان IP: {e}")
        return {
            "client_ip": "غير متوفر",
            "error": str(e)
        }

@app.get("/api/device/status")
async def device_status(request: Request):
    """
    فحص حالة الجهاز - محسن للتوجيه الصحيح والدقة مع دعم البصمة المتقدمة
    """
    try:
        # الحصول على معلومات الجهاز
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "")
        request_headers = dict(request.headers)

        # فلترة الطلبات المشبوهة
        if not user_agent or len(user_agent.strip()) < 10:
            return {
                "status": "error",
                "message": "طلب غير صحيح",
                "error": "Invalid user agent"
            }

        # تطبيع IP
        normalized_ip = client_ip.strip()

        # التحقق من نوع الجهاز باستخدام url_manager
        try:
            from utils.url_manager import url_manager
            is_main_server = url_manager.is_main_server_ip(normalized_ip)
        except:
            is_main_server = normalized_ip in ['*************', '127.0.0.1', 'localhost']  # fallback

        # فحص إضافي: إذا كان الطلب يحتوي على بصمة متقدمة من جهاز بعيد
        device_fingerprint = request_headers.get('x-device-fingerprint') or request_headers.get('X-Device-Fingerprint')
        has_advanced_fingerprint = device_fingerprint and device_fingerprint.startswith('fp_')

        # إذا كان لديه بصمة متقدمة ولكن ليس من الخادم الرئيسي، فهو جهاز بعيد
        if has_advanced_fingerprint and not is_main_server:
            logger.info(f"🔍 جهاز بعيد مع بصمة متقدمة: {device_fingerprint} من IP: {normalized_ip}")
        elif has_advanced_fingerprint and is_main_server:
            logger.info(f"🔍 الخادم الرئيسي مع بصمة متقدمة: {device_fingerprint} من IP: {normalized_ip}")
        elif is_main_server:
            logger.info(f"🔍 الخادم الرئيسي بدون بصمة متقدمة من IP: {normalized_ip}")

        # إذا كان الخادم الرئيسي، السماح له
        if is_main_server:
            from services.unified_fingerprint_service import get_unified_fingerprint_service
            from database.session import get_db

            db = next(get_db())
            unified_service = get_unified_fingerprint_service(db)
            device_data = unified_service.generate_unified_device_id(client_ip, user_agent, request_headers)
            device_id = device_data['device_id']
            logger.info(f"✅ الخادم الرئيسي يصل: {device_id}")
            return {
                "status": "allowed",
                "device_type": "main_server",
                "device_id": device_id,
                "message": "الخادم الرئيسي - مسموح"
            }

        # للأجهزة البعيدة، استخدام الخدمة الموحدة للبصمة المتقدمة
        from services.unified_fingerprint_service import get_unified_fingerprint_service
        from database.session import get_db

        db = next(get_db())
        unified_service = get_unified_fingerprint_service(db)
        device_data = unified_service.generate_unified_device_id(client_ip, user_agent, request_headers)

        # إذا كان لديه بصمة متقدمة، استخدمها كمعرف أساسي
        if has_advanced_fingerprint and device_fingerprint:
            device_id = device_fingerprint
            logger.info(f"🔍 استخدام البصمة المتقدمة كمعرف أساسي: {device_id}")
        else:
            device_id = device_data['device_id']
            logger.info(f"🔍 استخدام المعرف التقليدي: {device_id}")

        logger.info(f"🔍 معرف الجهاز النهائي: {device_id}")

        # التأكد من وجود معرف الجهاز
        if not device_id:
            logger.error(f"❌ فشل في الحصول على معرف الجهاز")
            return {
                "status": "error",
                "message": "فشل في تحديد معرف الجهاز",
                "error": "device_id_not_found"
            }

        # الحصول على IP الخادم الحقيقي للمقارنة
        from utils.url_manager import url_manager
        server_ip = url_manager.config.get('backend_host', '*************')

        # حفظ عنوان الجهاز البعيد في الـ cache
        if device_id and normalized_ip != server_ip and normalized_ip != "127.0.0.1":
            remote_devices_cache[device_id] = normalized_ip
            logger.info(f"💾 تم حفظ عنوان الجهاز البعيد في الـ cache: {device_id} -> {normalized_ip}")

        # فحص الأمان
        from services.device_security import DeviceSecurityService
        from database.session import get_db

        device_security = DeviceSecurityService()
        db = next(get_db())

        try:
            # فحص الحظر باستخدام معرف الجهاز النهائي
            is_blocked = device_security.is_device_blocked(device_id, normalized_ip, db)

            if is_blocked:
                logger.warning(f"جهاز محظور يحاول الوصول: {device_id}")
                return {
                    "status": "blocked",
                    "device_type": "remote",
                    "device_id": device_id,
                    "message": "جهازك محظور من الوصول إلى النظام",
                    "redirect_url": "/device-blocked"
                }

            # فحص طلب الموافقة
            if device_security.requires_approval(db):
                # فحص أولي: البحث بـ IP أولاً للتعامل مع تغيير المعرفات
                from models.device_security import ApprovedDevice
                from sqlalchemy import select

                is_approved = False
                # approved_device_record = None  # سيتم استخدامه لاحقاً

                # البحث بـ IP أولاً
                stmt = select(ApprovedDevice).where(
                    ApprovedDevice.client_ip == normalized_ip
                )
                approved_by_ip = db.execute(stmt).scalar_one_or_none()

                if approved_by_ip:
                    # الجهاز معتمد بـ IP، تحديث المعرف إذا كان مختلف
                    if approved_by_ip.device_id != device_id:
                        logger.info(f"تحديث معرف الجهاز المعتمد من {approved_by_ip.device_id} إلى {device_id}")
                        approved_by_ip.device_id = device_id
                        approved_by_ip.last_access = datetime.now()
                        db.commit()
                    is_approved = True
                    # approved_device_record = approved_by_ip  # سيتم استخدامه لاحقاً
                else:
                    # البحث بمعرف الجهاز كـ fallback
                    is_approved = device_security.is_device_approved(device_id, db)
                    if is_approved:
                        stmt = select(ApprovedDevice).where(
                            ApprovedDevice.device_id == device_id
                        )
                        # approved_device_record = db.execute(stmt).scalar_one_or_none()  # سيتم استخدامه لاحقاً

                if is_approved:
                    logger.info(f"جهاز معتمد يصل: {device_id}")

                    # الحصول على الرابط الذكي لإعادة التوجيه للجهاز المعتمد
                    try:
                        from services.redirect_manager import redirect_manager
                        smart_redirect_url = redirect_manager.get_smart_redirect_url(device_id)
                        original_url = redirect_manager.get_original_url(device_id)
                        logger.info(f"🧠 الرابط الذكي للجهاز المعتمد: {smart_redirect_url}")
                        logger.info(f"📍 الرابط الأصلي المحفوظ: {original_url}")
                    except Exception as e:
                        logger.error(f"خطأ في الحصول على رابط إعادة التوجيه للجهاز المعتمد: {e}")
                        try:
                            from utils.url_manager import url_manager
                            smart_redirect_url = url_manager.get_default_redirect_url()
                        except Exception as url_error:
                            logger.error(f"خطأ في الحصول على الرابط الافتراضي: {url_error}")
                            smart_redirect_url = f"http://{server_ip}:5175/"
                        original_url = None

                    return {
                        "status": "allowed",
                        "device_type": "remote_approved",
                        "device_id": device_id,
                        "message": "جهاز بعيد معتمد - مسموح",
                        "smart_redirect_url": smart_redirect_url,
                        "original_url": original_url
                    }

                # التحقق أولاً: هل الجهاز معتمد بالفعل؟ (لمنع إنشاء سجلات مكررة)
                from models.device_security import ApprovedDevice, PendingDevice
                from sqlalchemy import select

                # فحص إذا كان الجهاز معتمد بـ IP أو معرف الجهاز
                stmt_approved = select(ApprovedDevice).where(
                    (ApprovedDevice.client_ip == normalized_ip) |
                    (ApprovedDevice.device_id == device_id)
                )
                existing_approved = db.execute(stmt_approved).scalar_one_or_none()

                if existing_approved:
                    logger.info(f"🚫 الجهاز معتمد بالفعل، لن يتم إنشاء سجل جديد في pending: {device_id}")
                    # لا نفعل شيء - الجهاز معتمد بالفعل
                else:
                    # فحص إذا كان في انتظار الموافقة - البحث بمعرف الجهاز النهائي
                    existing_pending = None
                    stmt = select(PendingDevice).where(
                        PendingDevice.device_id == device_id,
                        PendingDevice.status == "pending"
                    )
                    existing_pending = db.execute(stmt).scalar_one_or_none()

                    # فحص إضافي: البحث بـ IP إذا لم نجد بالمعرف (للتعامل مع تغيير المعرفات)
                    if not existing_pending:
                        stmt = select(PendingDevice).where(
                            PendingDevice.client_ip == normalized_ip,
                            PendingDevice.status == "pending"
                        )
                        existing_pending_by_ip = db.execute(stmt).scalar_one_or_none()

                        if existing_pending_by_ip:
                            # تحديث المعرف في السجل الموجود
                            existing_pending_by_ip.device_id = device_id
                            existing_pending_by_ip.last_access = datetime.now()
                            db.commit()
                            existing_pending = existing_pending_by_ip
                            logger.info(f"تم تحديث معرف الجهاز في السجل الموجود: {device_id}")

                    if not existing_pending:
                        # إضافة جهاز جديد للانتظار فقط إذا لم يوجد أي سجل ولم يكن معتمد
                        # استخراج معلومات الجهاز من user agent
                        device_info = {
                            'hostname': 'جهاز غير معروف',
                            'device_type': 'غير محدد',
                            'system': 'غير معروف',
                            'platform': 'غير معروف'
                        }

                        # إضافة معلومات البصمة المتقدمة إذا كانت متوفرة
                        fingerprint_data = {}
                        if has_advanced_fingerprint:
                            fingerprint_data = {
                                'hardware_fingerprint': request_headers.get('x-device-hardware', ''),
                                'storage_fingerprint': request_headers.get('x-device-storage', ''),
                                'screen_fingerprint': request_headers.get('x-device-screen', ''),
                                'system_fingerprint': request_headers.get('x-device-system', ''),
                                'is_advanced_fingerprint': True
                            }

                        device_data_for_pending = {
                            'device_id': device_id,
                            'client_ip': normalized_ip,
                            'hostname': device_info.get('hostname', 'غير معروف'),
                            'device_type': 'جهاز بعيد',
                            'system': device_info.get('system', 'غير معروف'),
                            'platform': device_info.get('platform', 'غير معروف'),
                            'user_agent': user_agent,
                            'current_user': None,
                            **fingerprint_data  # إضافة معلومات البصمة
                        }
                        device_security.add_pending_device(device_data_for_pending, db)
                        logger.info(f"تم إضافة جهاز جديد للانتظار مع معلومات البصمة: {device_id}")

                # حفظ الرابط الأصلي إذا كان متوفراً في headers
                original_request_url = None
                try:
                    # البحث عن الرابط الأصلي في headers
                    referer = request.headers.get('referer')
                    origin = request.headers.get('origin')

                    # استخدام IP الخادم الحقيقي للمقارنة
                    backend_url = f"http://{server_ip}:8002"

                    if referer and referer != f"{backend_url}/api/device/status":
                        original_request_url = referer
                        logger.info(f"🔗 تم العثور على الرابط الأصلي في referer: {original_request_url}")
                    elif origin and origin != backend_url:
                        original_request_url = origin
                        logger.info(f"🔗 تم العثور على الرابط الأصلي في origin: {original_request_url}")

                    # حفظ الرابط الأصلي إذا وُجد
                    if original_request_url:
                        from services.redirect_manager import redirect_manager
                        redirect_manager.save_original_url(device_id, original_request_url, normalized_ip)
                        logger.info(f"💾 تم حفظ الرابط الأصلي للجهاز {device_id}: {original_request_url}")

                except Exception as save_error:
                    logger.error(f"خطأ في حفظ الرابط الأصلي: {save_error}")

                # الحصول على الرابط الذكي لإعادة التوجيه
                try:
                    from services.redirect_manager import redirect_manager
                    smart_redirect_url = redirect_manager.get_smart_redirect_url(device_id)
                    original_url = redirect_manager.get_original_url(device_id)
                    logger.info(f"🧠 الرابط الذكي: {smart_redirect_url}")
                    logger.info(f"📍 الرابط الأصلي المحفوظ: {original_url}")
                except Exception as e:
                    logger.error(f"خطأ في الحصول على رابط إعادة التوجيه الذكي: {e}")
                    try:
                        from utils.url_manager import url_manager
                        smart_redirect_url = url_manager.get_default_redirect_url()
                    except Exception as url_error:
                        logger.error(f"خطأ في الحصول على الرابط الافتراضي: {url_error}")
                        smart_redirect_url = f"http://{server_ip}:5175/"
                    original_url = None

                # استخدام مدير الروابط الديناميكي لإنشاء رابط صفحة الانتظار
                try:
                    from utils.url_manager import url_manager
                    pending_page_url = url_manager.get_pending_approval_url(original_url, device_id)
                except Exception as url_error:
                    logger.error(f"خطأ في إنشاء رابط صفحة الانتظار: {url_error}")
                    pending_page_url = f"http://{server_ip}:8002/device-pending-approval"

                return {
                    "status": "pending_approval",
                    "device_type": "remote_pending",
                    "device_id": device_id,
                    "message": "جهازك في انتظار موافقة مدير النظام",
                    "redirect_url": pending_page_url,
                    "smart_redirect_url": smart_redirect_url,
                    "original_url": original_url
                }

            # السماح بالوصول (نظام الموافقة معطل)
            logger.info(f"السماح للجهاز البعيد: {device_id} (نظام الموافقة معطل)")
            return {
                "status": "allowed",
                "device_type": "remote",
                "device_id": device_id,
                "message": "مسموح بالوصول"
            }

        finally:
            db.close()

    except Exception as e:
        logger.error(f"خطأ في فحص حالة الجهاز: {e}")
        return {
            "status": "error",
            "message": "خطأ في فحص حالة الجهاز",
            "error": str(e)
        }

@app.post("/api/device/clear-cache")
async def clear_device_cache():
    """
    مسح cache الأجهزة لحل مشاكل التخزين المؤقت
    """
    try:
        # إنشاء instance جديد من middleware لمسح cache
        from middleware.unified_security_middleware import UnifiedSecurityMiddleware
        middleware_instance = UnifiedSecurityMiddleware(app)
        # مسح cache الأجهزة
        middleware_instance.device_cache.clear()
        middleware_instance.device_last_seen.clear()
        middleware_instance.rejected_devices.clear()

        return {"success": True, "message": "تم مسح cache الأجهزة بنجاح"}
    except Exception as e:
        return {"success": False, "message": f"خطأ في مسح cache: {str(e)}"}

@app.get("/api/device/fingerprint-analytics/{device_id}")
async def get_device_fingerprint_analytics(device_id: str):
    """
    الحصول على تحليلات مفصلة لبصمة الجهاز
    """
    try:
        from services.unified_fingerprint_service import get_unified_fingerprint_service
        from database.session import get_db

        db = next(get_db())
        try:
            fingerprint_service = get_unified_fingerprint_service(db)
            analytics = fingerprint_service.get_fingerprint_analytics(device_id)

            if 'error' in analytics:
                return {
                    "success": False,
                    "error": analytics['error'],
                    "message": "فشل في الحصول على تحليلات البصمة"
                }

            return {
                "success": True,
                "analytics": analytics,
                "message": "تم الحصول على تحليلات البصمة بنجاح"
            }

        finally:
            db.close()

    except Exception as e:
        logger.error(f"خطأ في الحصول على تحليلات البصمة: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "خطأ في الحصول على تحليلات البصمة"
        }

@app.get("/api/device/fingerprint-validation")
async def validate_current_device_fingerprint(request: Request):
    """
    التحقق من صحة وثبات بصمة الجهاز الحالي
    """
    try:
        from services.unified_fingerprint_service import get_unified_fingerprint_service
        from database.session import get_db

        # الحصول على headers البصمة
        request_headers = dict(request.headers)
        hardware_fp = request_headers.get('x-device-hardware') or request_headers.get('X-Device-Hardware')
        storage_fp = request_headers.get('x-device-storage') or request_headers.get('X-Device-Storage')
        device_fingerprint = request_headers.get('x-device-fingerprint') or request_headers.get('X-Device-Fingerprint')

        if not hardware_fp or not storage_fp:
            return {
                "success": False,
                "error": "بصمة الجهاز غير مكتملة",
                "message": "لم يتم العثور على بصمة الجهاز في الطلب"
            }

        db = next(get_db())
        try:
            fingerprint_service = get_unified_fingerprint_service(db)

            # التحقق من ثبات البصمة
            stability = fingerprint_service.validate_fingerprint_stability(hardware_fp, storage_fp)

            # البحث عن البصمة في قاعدة البيانات
            fingerprint_record = fingerprint_service.find_existing_fingerprint(hardware_fp, storage_fp)

            # تحليل البصمة إذا وُجدت
            analytics = None
            if fingerprint_record and device_fingerprint:
                analytics = fingerprint_service.get_fingerprint_analytics(device_fingerprint)

            return {
                "success": True,
                "validation": {
                    "device_fingerprint": device_fingerprint,
                    "hardware_fingerprint": hardware_fp[:8] + "..." if hardware_fp else None,
                    "storage_fingerprint": storage_fp[:8] + "..." if storage_fp else None,
                    "stability": stability,
                    "exists_in_database": fingerprint_record is not None,
                    "is_active": bool(fingerprint_record.is_active) if fingerprint_record else False,
                    "analytics": analytics if analytics and 'error' not in analytics else None
                },
                "message": "تم التحقق من البصمة بنجاح"
            }

        finally:
            db.close()

    except Exception as e:
        logger.error(f"خطأ في التحقق من البصمة: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "خطأ في التحقق من البصمة"
        }

@app.websocket("/ws/devices")
async def websocket_devices(websocket: WebSocket):
    """
    WebSocket endpoint مبسط لتتبع الأجهزة
    """
    from services.device_tracker import device_tracker

    # استخدام device_tracker المبسط
    await device_tracker.connect(websocket)

    try:
        while True:
            try:
                # استقبال رسائل من العميل مع timeout
                _ = await asyncio.wait_for(websocket.receive_text(), timeout=60.0)

                # إرسال استجابة بسيطة
                await websocket.send_text('{"type": "heartbeat_ack", "timestamp": "' + datetime.now().isoformat() + '"}')

            except asyncio.TimeoutError:
                logger.warning("⏰ WebSocket heartbeat timeout")
                break
            except Exception as e:
                logger.error(f"خطأ في معالجة رسالة WebSocket: {e}")
                break

    except WebSocketDisconnect:
        logger.info("🔌 WebSocket disconnected")
    except Exception as e:
        logger.error(f"خطأ في WebSocket: {e}")
    finally:
        await device_tracker.disconnect(websocket)

@app.websocket("/ws/chat/{user_id}")
async def websocket_chat(websocket: WebSocket, user_id: int):
    """
    WebSocket endpoint للمحادثة الفورية
    """
    from services.chat_websocket_manager import chat_websocket_manager
    from utils.auth import get_user_by_id

    try:
        # التحقق من وجود المستخدم
        db = next(get_db())
        user = get_user_by_id(db, user_id)
        if not user or not user.is_active:
            await websocket.close(code=4001, reason="مستخدم غير صحيح")
            return

        # الاتصال بـ WebSocket Manager
        success = await chat_websocket_manager.connect(websocket, user_id)
        if not success:
            await websocket.close(code=4002, reason="فشل في الاتصال")
            return

        # إرسال رسالة ترحيب
        welcome_message = {
            'type': 'connection_established',
            'user_id': user_id,
            'username': user.username,
            'timestamp': datetime.now().isoformat()
        }
        await websocket.send_text(json.dumps(welcome_message, ensure_ascii=False))

        # حلقة استقبال الرسائل
        while True:
            try:
                # استقبال رسائل من العميل
                message_data = await websocket.receive_text()
                await chat_websocket_manager.handle_message(websocket, message_data)

            except WebSocketDisconnect:
                logger.info(f"🔌 انقطع اتصال المحادثة للمستخدم {user_id}")
                break
            except Exception as e:
                logger.error(f"خطأ في معالجة رسالة المحادثة للمستخدم {user_id}: {e}")
                break

    except Exception as e:
        logger.error(f"خطأ في WebSocket المحادثة للمستخدم {user_id}: {e}")
    finally:
        await chat_websocket_manager.disconnect(websocket)

@app.get("/api/test/device-status")
async def test_device_status():
    """
    اختبار نظام تتبع حالة الأجهزة المحسن
    """
    try:
        # الحصول على ملخص الأجهزة من النظام المبسط
        status_summary = {'devices': [], 'summary': {}}

        # الحصول على الأجهزة من النظام المبسط
        from services.device_tracker import device_tracker
        devices_data = await device_tracker.get_devices_from_database()

        return {
            "status": "success",
            "enhanced_system": {
                "devices_count": len(status_summary['devices']),
                "summary": status_summary['summary'],
                "devices": status_summary['devices'][:5]  # أول 5 أجهزة فقط
            },
            "database_system": {
                "devices_count": len(devices_data.get('devices', [])),
                "devices": [d.get('device_id', 'unknown') for d in devices_data.get('devices', [])][:5]  # أول 5 أجهزة فقط
            },
            "comparison": {
                "enhanced_vs_database": len(status_summary['devices']) - len(devices_data.get('devices', []))
            }
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"خطأ في اختبار نظام تتبع الحالة: {str(e)}"
        }

@app.get("/api/system/health")
async def system_health_check():
    """فحص صحة النظام - بدون مصادقة للمراقبة"""
    try:
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "message": "النظام يعمل بشكل طبيعي"
        }
    except Exception as e:
        logger.error(f"خطأ في فحص صحة النظام: {e}")
        return {
            "status": "error",
            "error": str(e),
            "message": "خطأ في فحص صحة النظام"
        }

@app.post("/api/system/recovery")
async def system_recovery():
    """استعادة النظام - بدون مصادقة للاستعادة التلقائية"""
    try:
        logger.info("تم استلام طلب استعادة النظام")
        return {
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "message": "تم تنفيذ عملية الاستعادة بنجاح"
        }
    except Exception as e:
        logger.error(f"خطأ في استعادة النظام: {e}")
        return {
            "status": "error",
            "error": str(e),
            "message": "خطأ في استعادة النظام"
        }

@app.get("/api/system/performance")
async def get_performance_stats():
    """الحصول على إحصائيات الأداء"""
    try:
        from middleware.performance_middleware import get_performance_stats
        stats = get_performance_stats()
        return {
            "status": "success",
            "data": stats,
            "message": "تم الحصول على إحصائيات الأداء بنجاح"
        }
    except Exception as e:
        logger.error(f"خطأ في الحصول على إحصائيات الأداء: {e}")
        return {
            "status": "error",
            "error": str(e),
            "message": "فشل في الحصول على إحصائيات الأداء"
        }

@app.post("/api/system/performance/reset")
async def reset_performance_stats():
    """إعادة تعيين إحصائيات الأداء"""
    try:
        from middleware.performance_middleware import reset_performance_stats
        reset_performance_stats()

        # إعادة تعيين النظام الموحد أيضاً (سيتم تطبيقه لاحقاً)
        # try:
        #     from core.service_manager import get_service
        #     perf_service = get_service("performance_manager")
        #     if perf_service:
        #         perf_service.reset_request_stats()
        # except Exception:
        #     pass

        return {
            "status": "success",
            "message": "تم إعادة تعيين إحصائيات الأداء بنجاح"
        }
    except Exception as e:
        logger.error(f"خطأ في إعادة تعيين إحصائيات الأداء: {e}")
        return {
            "status": "error",
            "error": str(e),
            "message": "فشل في إعادة تعيين إحصائيات الأداء"
        }

@app.get("/api/system/performance/advanced")
async def get_advanced_performance():
    """الحصول على مراقبة الأداء المتقدمة - سيتم تطبيقه لاحقاً"""
    try:
        # سيتم تطبيق النظام المتقدم لاحقاً
        from middleware.performance_middleware import get_performance_stats
        stats = get_performance_stats()

        return {
            "status": "success",
            "data": {
                "current": stats,
                "message": "النظام المتقدم قيد التطوير"
            },
            "message": "تم الحصول على بيانات الأداء الأساسية"
        }

    except Exception as e:
        logger.error(f"خطأ في الحصول على الأداء المتقدم: {e}")
        return {
            "status": "error",
            "error": str(e),
            "message": "فشل في الحصول على بيانات الأداء"
        }

@app.post("/api/system/performance/monitoring/start")
async def start_performance_monitoring():
    """بدء مراقبة الأداء - سيتم تطبيقه لاحقاً"""
    try:
        # سيتم تطبيق النظام المتقدم لاحقاً
        return {
            "status": "success",
            "message": "مراقبة الأداء الأساسية نشطة (النظام المتقدم قيد التطوير)"
        }

    except Exception as e:
        logger.error(f"خطأ في بدء مراقبة الأداء: {e}")
        return {
            "status": "error",
            "error": str(e),
            "message": "فشل في بدء مراقبة الأداء"
        }

@app.post("/api/system/performance/monitoring/stop")
async def stop_performance_monitoring():
    """إيقاف مراقبة الأداء - سيتم تطبيقه لاحقاً"""
    try:
        # سيتم تطبيق النظام المتقدم لاحقاً
        return {
            "status": "success",
            "message": "تم إيقاف مراقبة الأداء المتقدمة (النظام الأساسي لا يزال نشطاً)"
        }

    except Exception as e:
        logger.error(f"خطأ في إيقاف مراقبة الأداء: {e}")
        return {
            "status": "error",
            "error": str(e),
            "message": "فشل في إيقاف مراقبة الأداء"
        }

@app.get("/api/system/security")
async def get_security_stats():
    """الحصول على إحصائيات الأمان"""
    try:
        from middleware.unified_security_middleware import get_security_middleware_stats
        stats = get_security_middleware_stats()
        return {
            "status": "success",
            "data": stats,
            "message": "تم الحصول على إحصائيات الأمان بنجاح",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"خطأ في الحصول على إحصائيات الأمان: {e}")
        return {
            "status": "error",
            "error": str(e),
            "message": "فشل في الحصول على إحصائيات الأمان"
        }

@app.get("/api/system/database/health")
async def get_database_health():
    """فحص صحة قاعدة البيانات"""
    try:
        from utils.database_optimizer import database_optimizer
        health = await database_optimizer.get_database_health()
        return {
            "status": "success",
            "data": health,
            "message": "تم فحص صحة قاعدة البيانات بنجاح"
        }
    except Exception as e:
        logger.error(f"خطأ في فحص صحة قاعدة البيانات: {e}")
        return {
            "status": "error",
            "error": str(e),
            "message": "فشل في فحص صحة قاعدة البيانات"
        }

@app.post("/api/system/database/optimize")
async def optimize_database():
    """تحسين قاعدة البيانات"""
    try:
        from utils.database_optimizer import database_optimizer
        result = await database_optimizer.optimize_database()
        return {
            "status": "success",
            "data": result,
            "message": "تم تحسين قاعدة البيانات بنجاح"
        }
    except Exception as e:
        logger.error(f"خطأ في تحسين قاعدة البيانات: {e}")
        return {
            "status": "error",
            "error": str(e),
            "message": "فشل في تحسين قاعدة البيانات"
        }

@app.get("/health/scheduler")
async def scheduler_health():
    """فحص صحة نظام المهام المجدولة"""
    try:
        from services.scheduler_service import scheduler_service

        scheduler_obj = scheduler_service.scheduler
        is_available = scheduler_obj is not None
        is_running = is_available and scheduler_obj.running

        return {
            "status": "healthy" if is_available else "warning",
            "scheduler_available": is_available,
            "scheduler_running": is_running,
            "scheduler_object": str(type(scheduler_obj)) if scheduler_obj else "None",
            "message": "نظام المهام المجدولة يعمل" if is_running else "المجدول غير نشط"
        }
    except Exception as e:
        import traceback
        return {
            "status": "error",
            "scheduler_available": False,
            "scheduler_running": False,
            "error": str(e),
            "traceback": traceback.format_exc(),
            "message": "خطأ في نظام المهام المجدولة"
        }

@app.post("/health/scheduler/start")
async def start_scheduler():
    """بدء المجدول يدوياً"""
    try:
        from services.scheduler_service import scheduler_service

        setup_result = "already_exists"
        if scheduler_service.scheduler is None:
            scheduler_service._setup_scheduler()
            setup_result = "created"

        start_result = "already_running"
        if scheduler_service.scheduler is not None and not scheduler_service.scheduler.running:
            scheduler_service.start()
            start_result = "started"

        return {
            "status": "success",
            "message": "تم بدء المجدول بنجاح",
            "setup_result": setup_result,
            "start_result": start_result,
            "scheduler_available": scheduler_service.scheduler is not None,
            "scheduler_running": scheduler_service.scheduler is not None and scheduler_service.scheduler.running,
            "scheduler_type": str(type(scheduler_service.scheduler)) if scheduler_service.scheduler else "None"
        }
    except Exception as e:
        import traceback
        return {
            "status": "error",
            "message": f"فشل في بدء المجدول: {str(e)}",
            "traceback": traceback.format_exc()
        }

if __name__ == "__main__":
    # Use 0.0.0.0 to allow access from other devices on the network
    uvicorn.run("main:app", host="0.0.0.0", port=8002, reload=True)