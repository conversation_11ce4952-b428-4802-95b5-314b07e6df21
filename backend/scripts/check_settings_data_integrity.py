#!/usr/bin/env python3
"""
سكريبت فحص سلامة بيانات الإعدادات
يتحقق من:
1. جميع البيانات الموجودة في جدول settings
2. مقارنة القيم مع القيم المتوقعة في صفحة الإعدادات
3. البحث عن التكرارات
4. التحقق من صحة البيانات
"""

import sys
import os
from typing import Dict, List, Any, Optional
from collections import defaultdict

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.session import SessionLocal
from models.setting import Setting
from sqlalchemy import func, text
import json

class SettingsDataChecker:
    """فئة فحص سلامة بيانات الإعدادات"""
    
    def __init__(self):
        self.db = SessionLocal()
        
        # القيم المتوقعة من صفحة الإعدادات
        self.expected_settings = {
            # معلومات المتجر
            'store_name': {'type': 'text', 'default': 'متجر ذكي', 'group': 'store'},
            'store_address': {'type': 'textarea', 'default': 'طرابلس، ليبيا', 'group': 'store'},
            'store_phone': {'type': 'text', 'default': '+218-91-1234567', 'group': 'store'},
            'store_email': {'type': 'text', 'default': '<EMAIL>', 'group': 'store'},

            # العملة والضرائب
            'currency_symbol': {'type': 'text', 'default': 'د.ل', 'group': 'currency'},
            'decimal_places': {'type': 'number', 'default': '2', 'group': 'currency'},
            'tax_rate': {'type': 'number', 'default': '0', 'group': 'currency'},
            'tax_included': {'type': 'boolean', 'default': 'false', 'group': 'currency'},
            'currency_position': {'type': 'select', 'default': 'after', 'group': 'currency'},
            'show_decimals': {'type': 'boolean', 'default': 'true', 'group': 'currency'},
            'number_format_type': {'type': 'select', 'default': 'thousands', 'group': 'currency'},
            'number_separator_type': {'type': 'select', 'default': 'comma', 'group': 'currency'},

            # الفواتير
            'receipt_header': {'type': 'textarea', 'default': 'مرحباً بكم في متجرنا', 'group': 'receipt'},
            'receipt_footer': {'type': 'textarea', 'default': 'شكراً لزيارتكم', 'group': 'receipt'},
            'receipt_show_tax': {'type': 'boolean', 'default': 'true', 'group': 'receipt'},
            'receipt_show_cashier': {'type': 'boolean', 'default': 'true', 'group': 'receipt'},

            # إعدادات النظام الأساسية
            'low_stock_threshold': {'type': 'number', 'default': '10', 'group': 'system'},
            'auto_refresh_data': {'type': 'boolean', 'default': 'true', 'group': 'system'},
            'show_cashier_profits': {'type': 'boolean', 'default': 'true', 'group': 'system'},
            'theme_mode': {'type': 'theme', 'default': 'light', 'group': 'system'},
            'system_activated': {'type': 'boolean', 'default': 'true', 'group': 'system'},

            # إعدادات التاريخ والوقت (معروضة في تبويب النظام)
            'date_format': {'type': 'select', 'default': 'dd/MM/yyyy', 'group': 'datetime'},
            'time_format': {'type': 'select', 'default': '24h', 'group': 'datetime'},
            'timezone': {'type': 'select', 'default': 'Africa/Tripoli', 'group': 'datetime'},
            'date_language': {'type': 'select', 'default': 'ar', 'group': 'datetime'},
            'show_seconds': {'type': 'boolean', 'default': 'false', 'group': 'datetime'},
            'date_separator': {'type': 'select', 'default': '/', 'group': 'datetime'},
            'time_separator': {'type': 'select', 'default': ':', 'group': 'datetime'},
            'auto_detect_timezone': {'type': 'boolean', 'default': 'false', 'group': 'datetime'},
            'sync_internet_time': {'type': 'boolean', 'default': 'false', 'group': 'datetime'},
            'calendar_type': {'type': 'select', 'default': 'gregorian', 'group': 'datetime'},
            'hijri_adjustment': {'type': 'number', 'default': '0', 'group': 'datetime'},
            'week_start_day': {'type': 'select', 'default': 'saturday', 'group': 'datetime'},

            # إعدادات ساعات العمل (معروضة في تبويب المتجر)
            'business_hours_start': {'type': 'time', 'default': '08:00', 'group': 'store'},
            'business_hours_end': {'type': 'time', 'default': '17:00', 'group': 'store'},
            'weekend_days': {'type': 'weekend_select', 'default': 'friday,saturday', 'group': 'store'},

            # النسخ الاحتياطية
            'backup_path': {'type': 'text', 'default': 'backups', 'group': 'backup'},

            # الترخيص
            'license_key': {'type': 'text', 'default': '', 'group': 'license'},
            'license_status': {'type': 'text', 'default': 'نشط', 'group': 'license'},
            'license_expires': {'type': 'text', 'default': '2025-12-31', 'group': 'license'},

            # Google Drive
            'google_drive_enabled': {'type': 'boolean', 'default': 'false', 'group': 'google_drive'},
            'google_drive_folder_id': {'type': 'text', 'default': '', 'group': 'google_drive'},
            'google_drive_auto_backup': {'type': 'boolean', 'default': 'false', 'group': 'google_drive'},
            'google_drive_backup_frequency': {'type': 'select', 'default': 'daily', 'group': 'google_drive'},
            'google_drive_credentials': {'type': 'text', 'default': '', 'group': 'google_drive'},
            'google_drive_backup_folder_id': {'type': 'text', 'default': '', 'group': 'google_drive'},
            'google_drive_keep_count': {'type': 'number', 'default': '10', 'group': 'google_drive'},
            'google_drive_delete_local_after_upload': {'type': 'boolean', 'default': 'false', 'group': 'google_drive'},
            'google_drive_auto_cleanup': {'type': 'boolean', 'default': 'true', 'group': 'google_drive'},

            # إعدادات الصوت للمحادثة
            'chat_sound_enabled': {'type': 'boolean', 'default': 'true', 'group': 'chat'},
            'chat_sound_volume': {'type': 'number', 'default': '100', 'group': 'chat'},
            'chat_sound_id': {'type': 'select', 'default': 'pop', 'group': 'chat'},
        }
    
    def check_database_settings(self) -> Dict[str, Any]:
        """فحص جميع الإعدادات في قاعدة البيانات"""
        print("🔍 فحص بيانات جدول الإعدادات...")
        print("=" * 60)
        
        try:
            # جلب جميع الإعدادات
            all_settings = self.db.query(Setting).all()
            
            result = {
                'total_settings': len(all_settings),
                'settings_data': {},
                'duplicates': [],
                'missing_settings': [],
                'unexpected_settings': [],
                'invalid_values': [],
                'statistics': {}
            }
            
            # تجميع البيانات
            settings_by_key = defaultdict(list)
            for setting in all_settings:
                settings_by_key[setting.key].append(setting)
                result['settings_data'][setting.key] = {
                    'id': setting.id,
                    'value': setting.value,
                    'description': setting.description,
                    'created_at': setting.created_at.isoformat() if setting.created_at is not None else None,
                    'updated_at': setting.updated_at.isoformat() if setting.updated_at is not None else None
                }
            
            # البحث عن التكرارات
            for key, settings_list in settings_by_key.items():
                if len(settings_list) > 1:
                    result['duplicates'].append({
                        'key': key,
                        'count': len(settings_list),
                        'ids': [s.id for s in settings_list],
                        'values': [s.value for s in settings_list]
                    })
            
            # البحث عن الإعدادات المفقودة
            existing_keys = set(settings_by_key.keys())
            expected_keys = set(self.expected_settings.keys())
            
            result['missing_settings'] = list(expected_keys - existing_keys)
            result['unexpected_settings'] = list(existing_keys - expected_keys)
            
            # التحقق من صحة القيم
            for key, setting_info in self.expected_settings.items():
                if key in result['settings_data']:
                    current_value = result['settings_data'][key]['value']
                    if not self._validate_setting_value(key, current_value, setting_info):
                        result['invalid_values'].append({
                            'key': key,
                            'current_value': current_value,
                            'expected_type': setting_info['type'],
                            'default_value': setting_info['default']
                        })
            
            # إحصائيات
            result['statistics'] = {
                'settings_by_group': self._get_settings_by_group(result['settings_data']),
                'boolean_settings': self._count_boolean_settings(result['settings_data']),
                'empty_values': self._count_empty_values(result['settings_data']),
                'recent_updates': self._get_recent_updates(all_settings)
            }
            
            return result
            
        except Exception as e:
            print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
            return {'error': str(e)}
    
    def _validate_setting_value(self, key: str, value: str, setting_info: Dict) -> bool:
        """التحقق من صحة قيمة الإعداد"""
        setting_type = setting_info['type']
        
        try:
            if setting_type == 'boolean':
                return value.lower() in ['true', 'false', '1', '0']
            elif setting_type == 'number':
                float(value)
                return True
            elif setting_type in ['text', 'textarea', 'theme', 'select']:
                return isinstance(value, str)
            else:
                return True
        except:
            return False
    
    def _get_settings_by_group(self, settings_data: Dict) -> Dict[str, int]:
        """تجميع الإعدادات حسب المجموعة"""
        groups = defaultdict(int)
        for key in settings_data.keys():
            if key in self.expected_settings:
                group = self.expected_settings[key]['group']
                groups[group] += 1
        return dict(groups)
    
    def _count_boolean_settings(self, settings_data: Dict) -> Dict[str, int]:
        """عد الإعدادات المنطقية"""
        boolean_count = {'true': 0, 'false': 0, 'invalid': 0}
        for key, data in settings_data.items():
            if key in self.expected_settings and self.expected_settings[key]['type'] == 'boolean':
                value = data['value'].lower()
                if value in ['true', '1']:
                    boolean_count['true'] += 1
                elif value in ['false', '0']:
                    boolean_count['false'] += 1
                else:
                    boolean_count['invalid'] += 1
        return boolean_count
    
    def _count_empty_values(self, settings_data: Dict) -> int:
        """عد القيم الفارغة"""
        return sum(1 for data in settings_data.values() if not data['value'].strip())
    
    def _get_recent_updates(self, settings: List[Setting]) -> List[Dict]:
        """الحصول على آخر التحديثات"""
        recent = []
        for setting in settings:
            if setting.updated_at is not None:
                value_str = str(setting.value)
                recent.append({
                    'key': setting.key,
                    'updated_at': setting.updated_at.isoformat(),
                    'value': value_str[:50] + '...' if len(value_str) > 50 else value_str
                })
        
        # ترتيب حسب تاريخ التحديث
        recent.sort(key=lambda x: x['updated_at'], reverse=True)
        return recent[:10]  # آخر 10 تحديثات
    
    def analyze_ui_coverage(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل تغطية الواجهة للإعدادات"""
        # الإعدادات المعروضة في الواجهة
        ui_displayed_settings = set(self.expected_settings.keys())

        # الإعدادات الموجودة في قاعدة البيانات
        db_settings = set(result['settings_data'].keys())

        # الإعدادات غير المعروضة في الواجهة
        not_in_ui = db_settings - ui_displayed_settings

        # تصنيف الإعدادات غير المعروضة
        categorized_hidden = {
            'datetime_advanced': [],
            'system_internal': [],
            'deprecated': [],
            'unknown': []
        }

        for setting in not_in_ui:
            if any(keyword in setting.lower() for keyword in ['relative_time', 'dst_auto', 'time_format_12h', 'fallback_timezone', 'timezone_cache', 'auto_sync', 'internet_time_server', 'location_detection', 'date_input']):
                categorized_hidden['datetime_advanced'].append(setting)
            elif any(keyword in setting.lower() for keyword in ['datetime_display']):
                categorized_hidden['system_internal'].append(setting)
            else:
                categorized_hidden['unknown'].append(setting)

        return {
            'total_in_db': len(db_settings),
            'total_in_ui': len(ui_displayed_settings),
            'not_in_ui': list(not_in_ui),
            'not_in_ui_count': len(not_in_ui),
            'categorized_hidden': categorized_hidden,
            'coverage_percentage': (len(ui_displayed_settings) / len(db_settings)) * 100 if db_settings else 0
        }

    def print_detailed_report(self, result: Dict[str, Any]):
        """طباعة تقرير مفصل"""
        if 'error' in result:
            print(f"❌ خطأ: {result['error']}")
            return

        print(f"\n📊 تقرير شامل لبيانات الإعدادات")
        print("=" * 60)

        # تحليل تغطية الواجهة
        ui_analysis = self.analyze_ui_coverage(result)

        # الإحصائيات العامة
        print(f"📋 إجمالي الإعدادات في قاعدة البيانات: {result['total_settings']}")
        print(f"✅ الإعدادات المعروضة في الواجهة: {ui_analysis['total_in_ui']}")
        print(f"🔍 الإعدادات غير المعروضة: {ui_analysis['not_in_ui_count']}")
        print(f"📊 نسبة التغطية: {ui_analysis['coverage_percentage']:.1f}%")
        print(f"❌ الإعدادات المفقودة: {len(result['missing_settings'])}")
        print(f"🔄 التكرارات: {len(result['duplicates'])}")
        print(f"❗ القيم غير الصحيحة: {len(result['invalid_values'])}")
        
        # الإعدادات غير المعروضة في الواجهة
        if ui_analysis['not_in_ui_count'] > 0:
            print(f"\n� الإعدادات غير المعروضة في الواجهة:")
            print("-" * 60)

            # إعدادات التاريخ والوقت المتقدمة
            if ui_analysis['categorized_hidden']['datetime_advanced']:
                print(f"\n📅 إعدادات التاريخ والوقت المتقدمة ({len(ui_analysis['categorized_hidden']['datetime_advanced'])}):")
                for key in ui_analysis['categorized_hidden']['datetime_advanced']:
                    value = result['settings_data'][key]['value']
                    print(f"  🔑 {key}: {value}")

            # إعدادات النظام الداخلية
            if ui_analysis['categorized_hidden']['system_internal']:
                print(f"\n⚙️ إعدادات النظام الداخلية ({len(ui_analysis['categorized_hidden']['system_internal'])}):")
                for key in ui_analysis['categorized_hidden']['system_internal']:
                    value = result['settings_data'][key]['value']
                    print(f"  🔑 {key}: {value}")

            # إعدادات غير معروفة
            if ui_analysis['categorized_hidden']['unknown']:
                print(f"\n❓ إعدادات أخرى غير معروضة ({len(ui_analysis['categorized_hidden']['unknown'])}):")
                for key in ui_analysis['categorized_hidden']['unknown']:
                    value = result['settings_data'][key]['value']
                    print(f"  🔑 {key}: {value[:50]}{'...' if len(value) > 50 else ''}")

        # التكرارات
        if result['duplicates']:
            print(f"\n🔄 الإعدادات المكررة:")
            print("-" * 40)
            for dup in result['duplicates']:
                print(f"  🔑 {dup['key']}: {dup['count']} نسخ")
                print(f"     IDs: {dup['ids']}")
                print(f"     القيم: {dup['values']}")

        # الإعدادات المفقودة
        if result['missing_settings']:
            print(f"\n❌ الإعدادات المفقودة:")
            print("-" * 40)
            for key in result['missing_settings']:
                expected = self.expected_settings[key]
                print(f"  🔑 {key} ({expected['group']})")
                print(f"     النوع: {expected['type']}")
                print(f"     القيمة الافتراضية: {expected['default']}")
        
        # القيم غير الصحيحة
        if result['invalid_values']:
            print(f"\n❗ القيم غير الصحيحة:")
            print("-" * 40)
            for invalid in result['invalid_values']:
                print(f"  🔑 {invalid['key']}")
                print(f"     القيمة الحالية: {invalid['current_value']}")
                print(f"     النوع المتوقع: {invalid['expected_type']}")
                print(f"     القيمة الافتراضية: {invalid['default_value']}")
        
        # الإحصائيات
        print(f"\n📊 الإحصائيات:")
        print("-" * 40)
        
        # حسب المجموعة
        print("📂 الإعدادات حسب المجموعة:")
        for group, count in result['statistics']['settings_by_group'].items():
            print(f"  - {group}: {count}")
        
        # الإعدادات المنطقية
        bool_stats = result['statistics']['boolean_settings']
        print(f"\n🔘 الإعدادات المنطقية:")
        print(f"  - مفعلة (true): {bool_stats['true']}")
        print(f"  - معطلة (false): {bool_stats['false']}")
        print(f"  - غير صحيحة: {bool_stats['invalid']}")
        
        # القيم الفارغة
        print(f"\n📝 القيم الفارغة: {result['statistics']['empty_values']}")
        
        # آخر التحديثات
        if result['statistics']['recent_updates']:
            print(f"\n🕒 آخر التحديثات:")
            for update in result['statistics']['recent_updates'][:5]:
                print(f"  - {update['key']}: {update['updated_at'][:19]}")
    
    def fix_duplicates(self) -> Dict[str, Any]:
        """إصلاح التكرارات"""
        print("\n🔧 إصلاح التكرارات...")
        
        try:
            # البحث عن التكرارات
            duplicates_query = self.db.query(
                Setting.key,
                func.count(Setting.id).label('count')
            ).group_by(Setting.key).having(func.count(Setting.id) > 1).all()
            
            fixed_count = 0
            for key, count in duplicates_query:
                # الاحتفاظ بأحدث إعداد وحذف الباقي
                settings = self.db.query(Setting).filter(Setting.key == key).order_by(Setting.id.desc()).all()
                
                # حذف النسخ المكررة (الاحتفاظ بالأول)
                for setting in settings[1:]:
                    self.db.delete(setting)
                    fixed_count += 1
                
                print(f"  ✅ تم إصلاح التكرار في: {key} (حُذف {count-1} نسخ)")
            
            self.db.commit()
            return {'success': True, 'fixed_count': fixed_count}
            
        except Exception as e:
            self.db.rollback()
            return {'success': False, 'error': str(e)}
    
    def add_missing_settings(self) -> Dict[str, Any]:
        """إضافة الإعدادات المفقودة"""
        print("\n➕ إضافة الإعدادات المفقودة...")
        
        try:
            existing_keys = {s.key for s in self.db.query(Setting.key).all()}
            added_count = 0
            
            for key, info in self.expected_settings.items():
                if key not in existing_keys:
                    new_setting = Setting(
                        key=key,
                        value=info['default'],
                        description=f"إعداد {info['group']} - {info['type']}"
                    )
                    self.db.add(new_setting)
                    added_count += 1
                    print(f"  ✅ تم إضافة: {key} = {info['default']}")
            
            self.db.commit()
            return {'success': True, 'added_count': added_count}
            
        except Exception as e:
            self.db.rollback()
            return {'success': False, 'error': str(e)}
    
    def __del__(self):
        """إغلاق الاتصال"""
        if hasattr(self, 'db'):
            self.db.close()

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء فحص سلامة بيانات الإعدادات...")
    
    checker = SettingsDataChecker()
    
    # فحص البيانات
    result = checker.check_database_settings()
    
    # طباعة التقرير
    checker.print_detailed_report(result)
    
    # اقتراح الإصلاحات
    if result.get('duplicates') or result.get('missing_settings'):
        print(f"\n🔧 اقتراحات الإصلاح:")
        print("-" * 40)
        
        if result.get('duplicates'):
            print("1. إصلاح التكرارات")
            fix_duplicates = input("   هل تريد إصلاح التكرارات؟ (y/n): ").lower() == 'y'
            if fix_duplicates:
                fix_result = checker.fix_duplicates()
                if fix_result['success']:
                    print(f"   ✅ تم إصلاح {fix_result['fixed_count']} تكرار")
                else:
                    print(f"   ❌ فشل الإصلاح: {fix_result['error']}")
        
        if result.get('missing_settings'):
            print("2. إضافة الإعدادات المفقودة")
            add_missing = input("   هل تريد إضافة الإعدادات المفقودة؟ (y/n): ").lower() == 'y'
            if add_missing:
                add_result = checker.add_missing_settings()
                if add_result['success']:
                    print(f"   ✅ تم إضافة {add_result['added_count']} إعداد")
                else:
                    print(f"   ❌ فشل الإضافة: {add_result['error']}")
    
    print("\n✅ انتهى فحص سلامة البيانات!")

if __name__ == "__main__":
    main()
