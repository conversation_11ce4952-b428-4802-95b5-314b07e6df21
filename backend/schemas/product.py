from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, field_validator, ConfigDict, computed_field

class ProductBase(BaseModel):
    """Base schema for product data."""
    name: str = Field(..., min_length=1, max_length=100)
    barcode: Optional[str] = Field(None, max_length=50)
    description: Optional[str] = None
    price: float = Field(..., gt=0)
    cost_price: float = Field(..., ge=0)
    quantity: int = Field(default=0, ge=0)
    min_quantity: int = Field(default=0, ge=0)
    category: Optional[str] = Field(None, max_length=50)
    unit: str = Field(default="piece", max_length=20)
    is_active: bool = True

    @field_validator('price')
    def price_must_be_greater_than_cost(cls, v, info):
        values = info.data
        if 'cost_price' in values and v < values['cost_price']:
            raise ValueError('Price must be greater than or equal to cost price')
        return v

class ProductCreate(ProductBase):
    """Schema for creating a new product."""
    pass

class ProductUpdate(BaseModel):
    """Schema for updating an existing product."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    barcode: Optional[str] = Field(None, max_length=50)
    description: Optional[str] = None
    price: Optional[float] = Field(None, gt=0)
    cost_price: Optional[float] = Field(None, ge=0)
    quantity: Optional[int] = Field(None, ge=0)
    min_quantity: Optional[int] = Field(None, ge=0)
    category: Optional[str] = Field(None, max_length=50)
    unit: Optional[str] = Field(None, max_length=20)
    is_active: Optional[bool] = None

    @field_validator('price')
    def price_must_be_greater_than_cost(cls, v, info):
        values = info.data
        if v is not None and 'cost_price' in values and values['cost_price'] is not None and v < values['cost_price']:
            raise ValueError('Price must be greater than or equal to cost price')
        return v

class ProductInDB(ProductBase):
    """Schema for product data as stored in database."""
    id: int
    created_at: datetime
    updated_at: Optional[datetime]
    created_by: int
    updated_by: Optional[int]

    model_config = ConfigDict(from_attributes=True)

class ProductResponse(ProductInDB):
    """Schema for product response including calculated fields."""

    @computed_field
    @property
    def profit_margin(self) -> float:
        """Calculate profit margin: ((selling_price - cost_price) / cost_price) * 100"""
        if self.cost_price > 0:
            return ((self.price - self.cost_price) / self.cost_price) * 100
        return 0.0

    @computed_field
    @property
    def stock_value(self) -> float:
        """Calculate stock value: quantity * cost_price"""
        return self.quantity * self.cost_price