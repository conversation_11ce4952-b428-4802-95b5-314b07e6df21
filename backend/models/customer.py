from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.orm import relationship

from database.base import Base
from utils.datetime_utils import tripoli_timestamp

class Customer(Base):
    """Customer model for storing customer information."""
    __tablename__ = "customers"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    phone = Column(String(20), nullable=True)
    email = Column(String(100), nullable=True)
    address = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    updated_at = Column(DateTime(timezone=True), onupdate=tripoli_timestamp())

    # Relationships
    sales = relationship("Sale", back_populates="customer")
    debts = relationship("CustomerDebt", back_populates="customer", cascade="all, delete-orphan")

class CustomerDebt(Base):
    """Customer debt model for tracking customer debts."""
    __tablename__ = "customer_debts"

    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    sale_id = Column(Integer, ForeignKey("sales.id"), nullable=True)  # Optional link to sale
    amount = Column(Float, nullable=False)  # Total debt amount
    remaining_amount = Column(Float, nullable=False)  # Remaining debt amount
    description = Column(Text, nullable=True)
    is_paid = Column(Boolean, default=False)
    payment_status = Column(String(20), nullable=False, default='unpaid')  # unpaid, partial, paid
    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())
    updated_at = Column(DateTime(timezone=True), onupdate=tripoli_timestamp())

    # Relationships
    customer = relationship("Customer", back_populates="debts")
    sale = relationship("Sale")
    payments = relationship("DebtPayment", back_populates="debt", cascade="all, delete-orphan")

class DebtPayment(Base):
    """Debt payment model for tracking debt payments."""
    __tablename__ = "debt_payments"

    id = Column(Integer, primary_key=True, index=True)
    debt_id = Column(Integer, ForeignKey("customer_debts.id"), nullable=False)
    invoice_id = Column(Integer, ForeignKey("sales.id"), nullable=True)  # Link to specific invoice
    amount = Column(Float, nullable=False)
    payment_method = Column(String(20), nullable=False, default='cash')
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=tripoli_timestamp())

    # Relationships
    debt = relationship("CustomerDebt", back_populates="payments")
    invoice = relationship("Sale", foreign_keys=[invoice_id])
