from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import registry

# Create the SQLAlchemy declarative base
Base = declarative_base()

# Create a registry for models
mapper_registry = registry()

# This can be used as a decorator for models
registry_base = mapper_registry.generate_base()

# استيراد جميع النماذج لضمان إنشاء الجداول
def import_all_models():
    """استيراد جميع النماذج لضمان إنشاء الجداول"""
    try:
        # النماذج الأساسية
        from models.user import User
        from models.product import Product
        from models.customer import Customer, CustomerDebt, DebtPayment
        from models.sale import Sale, SaleItem
        from models.setting import Setting
        from models.scheduled_task import ScheduledTask

        # نماذج بصمات الأجهزة الجديدة
        from models.device_fingerprint import DeviceFingerprint, DeviceFingerprintHistory

        # نماذج أمان الأجهزة
        from models.device_security import ApprovedDevice, BlockedDevice, PendingDevice, DeviceSecuritySettings

        # نماذج نظام المحادثة الجديدة
        from models.chat_message import ChatMessage, ChatRoom, ChatRoomMember, UserOnlineStatus

        # نموذج سجلات النظام
        from models.system_log import SystemLog

        print("✅ تم استيراد جميع النماذج بنجاح")
    except ImportError as e:
        print(f"⚠️ تحذير: فشل في استيراد بعض النماذج: {e}")

# استدعاء الدالة عند استيراد الوحدة
import_all_models()