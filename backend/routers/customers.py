from fastapi import APIRouter, Depends, HTTPException, status, Query, Response
from sqlalchemy.orm import Session
from sqlalchemy import select, update, and_, func
from typing import List, Optional

from database.session import get_db
from models.customer import Customer, CustomerDebt
from models.sale import Sale
from schemas.customer import CustomerCreate, CustomerUpdate, CustomerResponse
from utils.auth import get_current_active_user

router = APIRouter(prefix="/api/customers", tags=["customers"])

@router.post("/", response_model=CustomerResponse, status_code=status.HTTP_201_CREATED)
async def create_customer(
    customer: CustomerCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Create a new customer."""

    # Check if customer with same name already exists
    existing_customer = db.execute(
        select(Customer).where(Customer.name == customer.name)
    ).scalar_one_or_none()

    if existing_customer:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Customer with this name already exists"
        )

    # Create new customer
    new_customer = Customer(**customer.model_dump())
    db.add(new_customer)
    db.commit()
    db.refresh(new_customer)

    return new_customer

@router.get("/", response_model=List[CustomerResponse])
async def get_customers(
    response: Response,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    search: Optional[str] = Query(None),
    active_only: Optional[bool] = Query(None),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get all customers with optional filtering."""

    # Build base query for filtering
    base_query = select(Customer)

    # Apply filters
    if active_only is not None:
        base_query = base_query.where(Customer.is_active == active_only)

    if search:
        search_pattern = f"%{search}%"
        base_query = base_query.where(
            Customer.name.ilike(search_pattern) |
            Customer.phone.ilike(search_pattern) |
            Customer.email.ilike(search_pattern)
        )

    # Get total count for pagination
    total_count = db.execute(
        select(func.count()).select_from(base_query.subquery())
    ).scalar()

    # Add pagination to query
    paginated_query = base_query.offset(skip).limit(limit)
    customers = db.execute(paginated_query).scalars().all()

    # Calculate debt and sales totals for each customer
    result = []
    for customer in customers:
        # Calculate total debt
        total_debt = db.execute(
            select(func.sum(CustomerDebt.remaining_amount))
            .where(and_(CustomerDebt.customer_id == customer.id, CustomerDebt.is_paid == False))
        ).scalar() or 0.0

        # Calculate total paid sales (amount actually paid)
        total_sales = db.execute(
            select(func.sum(Sale.amount_paid))
            .where(Sale.customer_id == customer.id)
        ).scalar() or 0.0

        customer_dict = customer.__dict__.copy()
        customer_dict['total_debt'] = total_debt
        customer_dict['total_sales'] = total_sales
        result.append(CustomerResponse.model_validate(customer_dict))

    # Add total count header for pagination
    response.headers["x-total-count"] = str(total_count)

    return result

@router.get("/stats")
async def get_customer_stats(
    db: Session = Depends(get_db)
):
    """Get customer statistics including total debts and paid sales."""

    # Total customers
    total_customers = db.execute(
        select(func.count(Customer.id))
    ).scalar() or 0

    # Active customers
    active_customers = db.execute(
        select(func.count(Customer.id))
        .where(Customer.is_active == True)
    ).scalar() or 0

    # Total debts (unpaid amounts)
    total_debts = db.execute(
        select(func.sum(CustomerDebt.remaining_amount))
        .where(CustomerDebt.is_paid == False)
    ).scalar() or 0.0

    # Total paid sales (amount actually paid by customers)
    total_paid_sales = db.execute(
        select(func.sum(Sale.amount_paid))
        .where(Sale.customer_id.isnot(None))  # Only sales with customers
    ).scalar() or 0.0

    return {
        "totalCustomers": total_customers,
        "activeCustomers": active_customers,
        "totalDebts": total_debts,
        "totalPaidSales": total_paid_sales
    }

@router.get("/{customer_id}", response_model=CustomerResponse)
async def get_customer(
    customer_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get a specific customer by ID."""

    customer = db.execute(
        select(Customer).where(Customer.id == customer_id)
    ).scalar_one_or_none()

    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )

    # Calculate totals
    total_debt = db.execute(
        select(func.sum(CustomerDebt.remaining_amount))
        .where(and_(CustomerDebt.customer_id == customer.id, CustomerDebt.is_paid == False))
    ).scalar() or 0.0

    total_sales = db.execute(
        select(func.sum(Sale.amount_paid))
        .where(Sale.customer_id == customer.id)
    ).scalar() or 0.0

    customer_dict = customer.__dict__.copy()
    customer_dict['total_debt'] = total_debt
    customer_dict['total_sales'] = total_sales

    return CustomerResponse.model_validate(customer_dict)

@router.put("/{customer_id}", response_model=CustomerResponse)
async def update_customer(
    customer_id: int,
    customer_update: CustomerUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Update a customer."""

    customer = db.execute(
        select(Customer).where(Customer.id == customer_id)
    ).scalar_one_or_none()

    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )

    # Update customer fields
    update_data = customer_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(customer, field, value)

    db.commit()
    db.refresh(customer)

    return customer

@router.delete("/{customer_id}")
async def delete_customer(
    customer_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Delete a customer (soft delete by setting is_active to False)."""

    customer = db.execute(
        select(Customer).where(Customer.id == customer_id)
    ).scalar_one_or_none()

    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )

    # Check if customer has unpaid debts
    unpaid_debts = db.execute(
        select(CustomerDebt)
        .where(and_(CustomerDebt.customer_id == customer_id, CustomerDebt.is_paid == False))
    ).scalars().all()

    if unpaid_debts:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete customer with unpaid debts"
        )

    # Soft delete using update statement
    stmt = update(Customer).where(Customer.id == customer_id).values(is_active=False)
    db.execute(stmt)
    db.commit()

    return {"message": "Customer deleted successfully"}

@router.get("/default/direct", response_model=CustomerResponse)
async def get_default_customer(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get the default 'عميل مباشر' customer."""

    customer = db.execute(
        select(Customer).where(Customer.name == 'عميل مباشر')
    ).scalar_one_or_none()

    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Default customer not found"
        )

    # Calculate totals
    total_debt = db.execute(
        select(func.sum(CustomerDebt.remaining_amount))
        .where(and_(CustomerDebt.customer_id == customer.id, CustomerDebt.is_paid == False))
    ).scalar() or 0.0

    total_sales = db.execute(
        select(func.sum(Sale.amount_paid))
        .where(Sale.customer_id == customer.id)
    ).scalar() or 0.0

    customer_dict = customer.__dict__.copy()
    customer_dict['total_debt'] = total_debt
    customer_dict['total_sales'] = total_sales

    return CustomerResponse.model_validate(customer_dict)