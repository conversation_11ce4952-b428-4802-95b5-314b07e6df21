from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from sqlalchemy import select, and_, or_

from database.session import get_db
from models.product import Product
from schemas.product import (
    ProductCreate,
    ProductUpdate,
    ProductResponse,
    ProductInDB
)
from utils.auth import get_current_user, get_optional_user
from models.user import User

router = APIRouter(prefix="/api/products", tags=["products"])

@router.post("/", response_model=ProductResponse)
async def create_product(
    product: ProductCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new product."""
    try:
        # Check if the category exists
        if product.category:
            existing_categories = db.query(Product.category).distinct().filter(
                Product.category.isnot(None)
            ).all()
            existing_categories = [cat[0] for cat in existing_categories]

            # If the category doesn't exist, create it
            if product.category not in existing_categories:
                print(f"Creating new category: {product.category}")
                # We'll create the product directly with the new category
                # No need to create a placeholder

            # Check if there's a placeholder for this category
            placeholder = db.query(Product).filter(
                and_(
                    Product.category == product.category,
                    Product.name.like("_CATEGORY_PLACEHOLDER_%")
                )
            ).first()

            # If there's a placeholder and this is a real product, we can delete the placeholder
            if placeholder:
                print(f"Deleting placeholder for category: {product.category}")
                db.delete(placeholder)
                db.flush()

        # Create the product
        try:
            # Try model_dump first (for newer Pydantic versions)
            product_data = product.model_dump()
        except AttributeError:
            # Fallback to dict for older Pydantic versions
            product_data = product.dict()

        db_product = Product(**product_data, created_by=current_user.id)
        db.add(db_product)
        db.commit()
        db.refresh(db_product)
        return db_product
    except IntegrityError:
        db.rollback()
        raise HTTPException(
            status_code=400,
            detail="Product with this barcode already exists"
        )

@router.post("/no-auth", response_model=ProductResponse)
async def create_product_no_auth(
    product: ProductCreate,
    db: Session = Depends(get_db)
):
    """Create a new product without authentication (for testing only)."""
    try:
        # Check if the category exists
        if product.category:
            existing_categories = db.query(Product.category).distinct().filter(
                Product.category.isnot(None)
            ).all()
            existing_categories = [cat[0] for cat in existing_categories]

            # If the category doesn't exist, create it
            if product.category not in existing_categories:
                print(f"Creating new category: {product.category}")
                # We'll create the product directly with the new category
                # No need to create a placeholder

            # Check if there's a placeholder for this category
            placeholder = db.query(Product).filter(
                and_(
                    Product.category == product.category,
                    Product.name.like("_CATEGORY_PLACEHOLDER_%")
                )
            ).first()

            # If there's a placeholder and this is a real product, we can delete the placeholder
            if placeholder:
                print(f"Deleting placeholder for category: {product.category}")
                db.delete(placeholder)
                db.flush()

        # Create the product
        try:
            # Try model_dump first (for newer Pydantic versions)
            product_data = product.model_dump()
        except AttributeError:
            # Fallback to dict for older Pydantic versions
            product_data = product.dict()

        # Use a default user ID for testing
        db_product = Product(**product_data, created_by=1)
        db.add(db_product)
        db.commit()
        db.refresh(db_product)
        return db_product
    except IntegrityError:
        db.rollback()
        raise HTTPException(
            status_code=400,
            detail="Product with this barcode already exists"
        )

from fastapi import Response
from pydantic import BaseModel

class PaginatedProductResponse(BaseModel):
    items: List[ProductResponse]
    total: int
    page: int
    limit: int
    pages: int

@router.get("/", response_model=List[ProductResponse])
async def get_products(
    response: Response,
    page: int = 1,
    limit: int = 10,
    search: Optional[str] = None,
    category: Optional[str] = None,
    low_stock: bool = False,
    zero_stock: bool = False,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db)
):
    """Get all products with optional filtering."""
    try:
        # Calculate skip from page
        skip = (page - 1) * limit

        # Log received parameters for debugging
        print(f"GET /api/products - Received parameters:")
        print(f"  search: {search}")
        print(f"  category: {category}")
        print(f"  low_stock: {low_stock}")
        print(f"  zero_stock: {zero_stock}")
        print(f"  is_active: {is_active}")
        print(f"  page: {page}, limit: {limit}, calculated skip: {skip}")

        # Create a simple query to get all products first
        query = db.query(Product)
        total_before_filter = query.count()
        print(f"Total products before filtering: {total_before_filter}")

        # Apply filters if provided
        if search:
            print(f"Applying search filter: '{search}'")
            query = query.filter(
                (Product.name.ilike(f"%{search}%")) |
                (Product.barcode.ilike(f"%{search}%")) |
                (Product.description.ilike(f"%{search}%"))
            )
            count_after_search = query.count()
            print(f"Products after search filter: {count_after_search}")

        if category:
            print(f"Applying category filter: '{category}'")
            query = query.filter(Product.category == category)
            count_after_category = query.count()
            print(f"Products after category filter: {count_after_category}")

        # Handle stock filters - if both are enabled, use OR logic
        if low_stock and zero_stock:
            print(f"Applying both low_stock and zero_stock filters with OR logic")
            query = query.filter(
                or_(
                    and_(Product.quantity <= Product.min_quantity, Product.quantity > 0),  # Low stock
                    Product.quantity == 0  # Zero stock
                )
            )
            count_after_stock_filters = query.count()
            print(f"Products after combined stock filters: {count_after_stock_filters}")
        elif low_stock:
            print(f"Applying low_stock filter only")
            query = query.filter(
                Product.quantity <= Product.min_quantity,
                Product.quantity > 0
            )
            count_after_low_stock = query.count()
            print(f"Products after low_stock filter: {count_after_low_stock}")
        elif zero_stock:
            print(f"Applying zero_stock filter only")
            query = query.filter(Product.quantity == 0)
            count_after_zero_stock = query.count()
            print(f"Products after zero_stock filter: {count_after_zero_stock}")

        if is_active is not None:
            print(f"Applying is_active filter: {is_active}")
            query = query.filter(Product.is_active == is_active)
            count_after_active = query.count()
            print(f"Products after is_active filter: {count_after_active}")

        # Get total count before pagination for metadata
        total_count = query.count()

        # Calculate pagination metadata
        total_pages = (total_count + limit - 1) // limit if total_count > 0 else 1

        # Make sure page is within valid range
        if page > total_pages and total_count > 0:
            page = total_pages
            skip = (page - 1) * limit
            print(f"Adjusted page to {page} and skip to {skip} because it was out of range")

        # Apply pagination
        query = query.offset(skip).limit(limit)

        # Execute query
        products = query.all()
        print(f"Final products count after all filters: {len(products)}")

        # Log first few products for debugging
        if products:
            sample = products[:min(3, len(products))]
            print(f"Sample products: {[f'{p.id}: {p.name} ({p.category})' for p in sample]}")

        # Add response headers with pagination metadata
        response.headers["X-Total-Count"] = str(total_count)
        response.headers["X-Page"] = str(page)
        response.headers["X-Limit"] = str(limit)
        response.headers["X-Pages"] = str(total_pages)

        return products
    except Exception as e:
        print(f"Error in get_products: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.get("/search-by-barcode/{barcode}", response_model=ProductResponse)
async def search_product_by_barcode(
    barcode: str,
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user)
):
    """Search for a product by its barcode."""
    try:
        # Search for product with exact barcode match
        product = db.query(Product).filter(Product.barcode == barcode).first()

        if not product:
            raise HTTPException(
                status_code=404,
                detail=f"Product with barcode '{barcode}' not found"
            )

        return product

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error searching product by barcode: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@router.get("/check-barcode/{barcode}")
async def check_barcode_exists(
    barcode: str,
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user)
):
    """Check if a barcode already exists in the database."""
    existing_product = db.query(Product).filter(Product.barcode == barcode).first()
    if existing_product:
        return {
            "exists": True,
            "product": {
                "id": existing_product.id,
                "name": existing_product.name,
                "barcode": existing_product.barcode
            }
        }
    return {"exists": False}

@router.get("/{product_id}", response_model=ProductResponse)
async def get_product(
    product_id: int,
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user)
):
    """Get a specific product by ID."""
    product = db.query(Product).filter(Product.id == product_id).first()
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")
    return product

@router.put("/{product_id}", response_model=ProductResponse)
async def update_product(
    product_id: int,
    product: ProductUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update a product."""
    db_product = db.query(Product).filter(Product.id == product_id).first()
    if not db_product:
        raise HTTPException(status_code=404, detail="Product not found")

    try:
        # Try model_dump first (for newer Pydantic versions)
        update_data = product.model_dump(exclude_unset=True)
    except AttributeError:
        # Fallback to dict for older Pydantic versions
        update_data = product.dict(exclude_unset=True)

    update_data["updated_by"] = current_user.id

    for key, value in update_data.items():
        setattr(db_product, key, value)

    db.commit()
    db.refresh(db_product)
    return db_product

@router.put("/{product_id}/stock", response_model=ProductResponse)
async def update_product_stock(
    product_id: int,
    data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update a product's stock quantity."""
    # Extract quantity from request body
    quantity = data.get("quantity")

    # Validate quantity
    if quantity is None or not isinstance(quantity, int) or quantity < 0:
        raise HTTPException(status_code=422, detail="Invalid quantity. Must be a positive integer.")

    # Log the request for debugging
    print(f"Updating stock for product ID {product_id} to quantity {quantity}")

    # Find the product
    db_product = db.query(Product).filter(Product.id == product_id).first()
    if not db_product:
        raise HTTPException(status_code=404, detail="Product not found")

    # Log the current product state
    print(f"Current product state: ID={db_product.id}, Name={db_product.name}, Quantity={db_product.quantity}")

    # Update the product quantity
    setattr(db_product, "quantity", quantity)
    setattr(db_product, "updated_by", current_user.id)

    # Commit the changes
    db.commit()
    db.refresh(db_product)

    # Log the updated product state
    print(f"Updated product state: ID={db_product.id}, Name={db_product.name}, Quantity={db_product.quantity}")

    return db_product

@router.delete("/{product_id}", status_code=204)
async def delete_product(
    product_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete a product."""
    from models.sale import SaleItem

    db_product = db.query(Product).filter(Product.id == product_id).first()
    if not db_product:
        raise HTTPException(status_code=404, detail="المنتج غير موجود")

    # التحقق من وجود المنتج في فواتير
    sale_items_count = db.query(SaleItem).filter(SaleItem.product_id == product_id).count()

    if sale_items_count > 0:
        raise HTTPException(
            status_code=400,
            detail=f"لا يمكن حذف هذا المنتج لأنه مسجل في {sale_items_count} فاتورة. يمكنك إلغاء تفعيل المنتج بدلاً من حذفه."
        )

    try:
        db.delete(db_product)
        db.commit()
        return None
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail="فشل في حذف المنتج. حاول مرة أخرى."
        )

@router.get("/categories/list", response_model=List[str])
async def get_categories(
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user)
):
    """Get list of unique product categories."""
    categories = db.query(Product.category).distinct().filter(
        Product.category.isnot(None)
    ).all()
    return [cat[0] for cat in categories]

@router.post("/categories", response_model=List[str])
async def add_category(
    category_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Add a new category."""
    new_category = category_data.get("category")
    if not new_category:
        raise HTTPException(status_code=400, detail="Category name is required")

    # Check if category already exists
    existing_categories = db.query(Product.category).distinct().filter(
        Product.category.isnot(None)
    ).all()
    existing_categories = [cat[0] for cat in existing_categories]

    if new_category in existing_categories:
        raise HTTPException(status_code=400, detail="Category already exists")

    # Create a dummy product with the new category to ensure it's added to the database
    # This is a workaround since we don't have a separate categories table
    dummy_product = Product(
        name=f"_CATEGORY_PLACEHOLDER_{new_category}",
        price=0,
        cost_price=0,
        quantity=0,
        category=new_category,
        created_by=current_user.id
    )
    db.add(dummy_product)
    db.commit()

    # Get updated categories list
    categories = db.query(Product.category).distinct().filter(
        Product.category.isnot(None)
    ).all()
    return [cat[0] for cat in categories]

@router.delete("/categories/{category_name}", response_model=List[str])
async def delete_category(
    category_name: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete a category if it has no products."""
    # Check if category exists
    category_exists = db.query(Product.category).filter(
        Product.category == category_name
    ).first()

    if not category_exists:
        raise HTTPException(status_code=404, detail="Category not found")

    # Check if there are any real products with this category
    products_with_category = db.query(Product).filter(
        and_(
            Product.category == category_name,
            ~Product.name.like("_CATEGORY_PLACEHOLDER_%")
        )
    ).count()

    if products_with_category > 0:
        raise HTTPException(
            status_code=400,
            detail="Cannot delete category that has products. Reassign or delete the products first."
        )

    # Delete placeholder products for this category
    placeholder_products = db.query(Product).filter(
        and_(
            Product.category == category_name,
            Product.name.like("_CATEGORY_PLACEHOLDER_%")
        )
    ).all()

    for product in placeholder_products:
        db.delete(product)

    db.commit()

    # Get updated categories list
    categories = db.query(Product.category).distinct().filter(
        Product.category.isnot(None)
    ).all()
    return [cat[0] for cat in categories]