#!/usr/bin/env python3
"""
اختبار تقارير المديونية المحسنة
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from sqlalchemy.orm import Session
from database.session import get_db
from backend.routers.debt_reports import get_debt_summary, get_debt_aging
import asyncio
from models.user import User

async def test_debt_reports():
    """اختبار تقارير المديونية"""
    
    # إنشاء جلسة قاعدة البيانات
    db = next(get_db())
    
    # إنشاء مستخدم وهمي للاختبار
    class MockUser:
        def __init__(self):
            self.id = 1
            self.role = 'admin'
    
    mock_user = MockUser()
    
    try:
        print("🔍 اختبار ملخص المديونية...")
        summary = await get_debt_summary(db=db, current_user=mock_user)
        
        print(f"📊 النتائج:")
        print(f"  إجمالي المديونية: {summary['totalAmount']:.2f} د.ل")
        print(f"  المبلغ المحصل: {summary['paidAmount']:.2f} د.ل")
        print(f"  المبلغ المتبقي: {summary['remainingAmount']:.2f} د.ل")
        print(f"  معدل التحصيل: {summary['collectionRate']:.2f}%")
        print(f"  عدد العملاء المدينين: {summary['uniqueDebtors']}")
        print(f"  متوسط قيمة الدين: {summary['averageDebtAmount']:.2f} د.ل")
        
        # التحقق من المنطق
        calculated_remaining = summary['totalAmount'] - summary['paidAmount']
        print(f"\n🔍 التحقق من المنطق:")
        print(f"  المبلغ المتبقي المحسوب: {calculated_remaining:.2f} د.ل")
        print(f"  المبلغ المتبقي المعروض: {summary['remainingAmount']:.2f} د.ل")
        print(f"  الفرق: {abs(calculated_remaining - summary['remainingAmount']):.2f} د.ل")
        
        print("\n🔍 اختبار تقرير أعمار الديون...")
        aging = await get_debt_aging(db=db, current_user=mock_user)
        
        total_aging_amount = 0
        total_aging_percentage = 0
        
        print(f"📊 أعمار الديون:")
        for item in aging:
            print(f"  {item['range']}: {item['amount']:.2f} د.ل ({item['percentage']:.2f}%) - {item['count']} دين")
            total_aging_amount += item['amount']
            total_aging_percentage += item['percentage']
        
        print(f"\n🔍 التحقق من أعمار الديون:")
        print(f"  مجموع مبالغ الأعمار: {total_aging_amount:.2f} د.ل")
        print(f"  مجموع النسب المئوية: {total_aging_percentage:.2f}%")
        print(f"  المبلغ المتبقي من الملخص: {summary['remainingAmount']:.2f} د.ل")
        print(f"  الفرق: {abs(summary['remainingAmount'] - total_aging_amount):.2f} د.ل")
        
        if abs(summary['remainingAmount'] - total_aging_amount) > 1:
            print("⚠️  تحذير: هناك فرق كبير بين المبلغ المتبقي وأعمار الديون!")
        else:
            print("✅ البيانات متسقة!")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_debt_reports())
