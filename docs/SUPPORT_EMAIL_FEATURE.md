# ميزة إرسال البريد الإلكتروني للدعم - Support Email Feature

## 📋 نظرة عامة

تم إضافة ميزة جديدة إلى نظام SmartPOS تتيح للمستخدمين إرسال رسائل دعم مباشرة إلى فريق الدعم الفني من خلال مركز المساعدة. هذه الميزة تتضمن تلقائياً معلومات المتجر لمساعدة فريق الدعم في تحديد المرسل وتقديم المساعدة المناسبة.

## ✨ المميزات المضافة

### 🎯 زر إرسال رسالة للدعم
- **الموقع**: في نهاية قسم الدعم في مركز المساعدة
- **التصميم**: زر بارز مع أيقونة طائرة ورقية
- **الوظيفة**: فتح نافذة مودال لإرسال رسالة دعم

### 📱 نافذة إرسال الرسالة
- **التصميم**: مودال موحد مع تصميم المشروع
- **المعلومات المطلوبة**:
  - اسم المرسل الكامل
  - البريد الإلكتروني للمرسل
  - موضوع الرسالة
  - محتوى الرسالة
- **المعلومات التلقائية**: معلومات المتجر (الاسم، العنوان، الهاتف، البريد الإلكتروني)

### 📧 قالب البريد الإلكتروني
- **تصميم HTML احترافي** مع ألوان متناسقة
- **معلومات شاملة** عن المرسل والمتجر
- **سهولة الرد** مع عنوان البريد الإلكتروني للمرسل
- **تنسيق واضح** لسهولة القراءة

## 🔧 التفاصيل التقنية

### 📁 الملفات المضافة/المعدلة

#### 1. `frontend/src/components/SupportEmailModal.tsx` (جديد)
```typescript
// مكون نافذة إرسال رسالة الدعم
- نموذج لإدخال بيانات الرسالة
- جلب معلومات المتجر تلقائياً
- التحقق من صحة البيانات
- إرسال الرسالة عبر API
- عرض حالة الإرسال (نجاح/فشل)
```

#### 2. `backend/routers/support_email.py` (جديد)
```python
# endpoint جديد لإرسال رسائل الدعم
- التحقق من صحة البيانات
- دعم المستخدمين المسجلين وغير المسجلين
- استخدام خدمة البريد الإلكتروني الموجودة
- إرجاع استجابة مناسبة
```

#### 3. `backend/services/email_service.py` (معدل)
```python
# إضافة دوال جديدة:
- send_support_email_to_support()
- _simulate_support_email_send()
- _generate_support_html_content()
```

#### 4. `frontend/src/pages/HelpCenter.tsx` (معدل)
```typescript
// إضافة:
- استيراد مكون SupportEmailModal
- حالة النافذة المنبثقة
- زر إرسال رسالة للدعم
- ربط النافذة المنبثقة
```

#### 5. `backend/main.py` (معدل)
```python
# إضافة:
- استيراد support_email_router
- تسجيل الـ router الجديد
```

#### 6. `backend/utils/auth.py` (معدل)
```python
# إضافة:
- alias للدالة get_current_user_optional
```

### 🎨 التصميم والواجهة

#### الألوان والأيقونات
- **الأيقونة الرئيسية**: `FaPaperPlane` بلون أساسي
- **معلومات المتجر**: خلفية زرقاء فاتحة
- **النموذج**: تصميم نظيف ومنظم
- **حالة الإرسال**: ألوان مميزة (أخضر للنجاح، أحمر للخطأ)

#### التجاوب
- **الأجهزة المحمولة**: تصميم متجاوب كامل
- **الأجهزة اللوحية**: تخطيط مناسب
- **أجهزة سطح المكتب**: استغلال أمثل للمساحة

### 🔐 الأمان والمصادقة

#### دعم المستخدمين
- **المستخدمون المسجلون**: تضمين معلومات المستخدم في الرسالة
- **الزوار**: إمكانية الإرسال بدون تسجيل دخول
- **التحقق**: التحقق من صحة البريد الإلكتروني والبيانات

#### حماية البيانات
- **معلومات المتجر**: جلب آمن من قاعدة البيانات
- **التشفير**: استخدام HTTPS للإرسال
- **التحقق**: فلترة البيانات المدخلة

## 📊 محتوى البريد الإلكتروني

### العنوان
```
💬 رسالة دعم من [اسم المتجر] - [موضوع الرسالة]
```

### المحتوى
- **معلومات المرسل**: الاسم، البريد الإلكتروني، الدور في النظام
- **معلومات المتجر**: الاسم، العنوان، الهاتف، البريد الإلكتروني
- **تفاصيل الرسالة**: الموضوع والمحتوى
- **تعليمات الرد**: عنوان البريد الإلكتروني للرد

### التصميم
- **HTML متجاوب** مع تصميم احترافي
- **ألوان متناسقة** مع هوية النظام
- **تنسيق واضح** لسهولة القراءة
- **أقسام منظمة** لكل نوع من المعلومات

## 🚀 كيفية الاستخدام

### للمستخدمين
1. **الدخول إلى مركز المساعدة** من الصفحة الرئيسية
2. **التمرير إلى قسم الدعم** في نهاية الصفحة
3. **النقر على زر "إرسال رسالة للدعم الفني"**
4. **ملء النموذج** بالمعلومات المطلوبة
5. **النقر على "إرسال الرسالة"**
6. **انتظار تأكيد الإرسال** والرد من فريق الدعم

### للمطورين
1. **تشغيل الخادم الخلفي**: `cd backend && python main.py`
2. **تشغيل الواجهة الأمامية**: `cd frontend && npm run dev`
3. **فتح المتصفح**: `http://localhost:5175`
4. **اختبار الميزة** من مركز المساعدة

## 🔍 استكشاف الأخطاء

### مشاكل شائعة
1. **فشل في إرسال الرسالة**:
   - تحقق من إعدادات البريد الإلكتروني في `.env`
   - تحقق من اتصال الإنترنت
   - راجع سجلات الخادم

2. **عدم ظهور معلومات المتجر**:
   - تحقق من إعدادات المتجر في قاعدة البيانات
   - تحقق من endpoint `/api/settings/public`

3. **مشاكل في التصميم**:
   - تحقق من استيراد المكونات
   - تحقق من ملفات CSS

### السجلات
- **الخادم الخلفي**: راجع سجلات Python في Terminal
- **الواجهة الأمامية**: راجع Console في المتصفح
- **البريد الإلكتروني**: راجع سجلات خدمة البريد الإلكتروني

## 📞 الدعم

- **البريد الإلكتروني**: <EMAIL>
- **النظام**: SmartPOS Libya
- **الحالة**: مفعل ويعمل بكامل الطاقة ✅

---

## 🎉 الميزة جاهزة للاستخدام!

جميع المكونات مطبقة والنظام يعمل بشكل كامل.
يمكن للمستخدمين الآن إرسال رسائل دعم مباشرة من مركز المساعدة مع تضمين معلومات المتجر تلقائياً!

**تم التطوير بواسطة**: فريق SmartPOS  
**التاريخ**: 2025  
**الإصدار**: 1.0.0
